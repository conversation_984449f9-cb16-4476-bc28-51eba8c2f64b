{"origin_pdf_path": "/workspace/user_input_files/NeRFFaceSketch_SIG23.pdf", "text_in_pdf": "ORCA – Online Research @ Cardiff\nThis is a n  Op e n  Acces s doc u m e n t  dow nloa d e d  fro m  ORC A, Ca r diff U niver sity's\ninstitution al r e pository:htt ps://orc a.c a r diff.ac.uk /id/ep rint/15 9 4 6 8/\nThis is t h e  a u t ho r’s version of a  wo rk t h a t  w as  s u b mitt e d  to / a c c e p t e d  for\np u blication.\nCit ation for final p u blish e d  ve rsion:\n<PERSON>, <PERSON>, <PERSON>, <PERSON> g-<PERSON>, <PERSON> e n, S h u-Yu, <PERSON><PERSON> g, <PERSON><PERSON> e n, <PERSON>, Ch u n p e n g, <PERSON>, <PERSON><PERSON><PERSON> a n d\nF u, H o n g bo 2 0 2 3. Sk etc hFac e N eRF: Sk e tc h-b a s e d  facia l g e n e r a tion a n d  e diting in\nn e u r al r a dia nc e  fields. ACM Tr a ns a ctions on Gr a p hic s 4 2  (4) , 1 5 9. 1 0.11 4 5/35 9 2 1 0 0  \nP u blish e rs  p a g e: h t t p s://doi.org/10.11 4 5/35 9 2 1 0 0  \nPle a s e  not e: \nCh a n g e s  m a d e  a s  a  r e s ult of p u blishing p roc e s s e s  s u c h  a s  copy-e diting, for m a t ting\na n d  p a g e  n u m b e r s  m ay not  b e  r eflect e d  in t his versi on. For t h e  d efinitive version of\nt his p u blication, ple a s e  r efe r  to t h e  p u blish e d  sou rc e. You a r e  a dvis e d  to cons ult t h e\np u blish e r’s version if you wis h to cite t his p a p er.\nThis ve rsion is b eing  m a d e  av ailable in a ccor d a nc e  with p u blish e r  policies. S e e  \nh tt p://orc a.cf.ac.uk/policies.ht ml for u s a g e  polici es. Copyright  a n d  m o r al right s  for\np u blications  m a d e  av ailable in ORCA a r e  r e t ain e d  by  t h e  copyright  hold e r s.\n\nSketchFaceNeRF:Sketch-based Facial Generation and Editing in Neural\nRadianceFields\nLIN GAO∗,Instituteof ComputingTechnology,CAS and University of ChineseAcademyof Sciences,China\nFENG-LINLIU, Instituteof ComputingTechnology,CAS and University of ChineseAcademyof Sciences,China\nSHU-YU CHEN, Instituteof ComputingTechnology,ChineseAcademyofSciences,China\nKAIWEN JIANG, Instituteof ComputingTechnology,CAS and BeijingJiaotong University, China\nCHUNPENGLI, Instituteof ComputingTechnology,ChineseAcademyofSciences,China\nYU-KUN LAI, Schoolof ComputerScience and Informatics, CardiffUniversity, UK\nHONGBOFU, Schoolof CreativeMedia, CityUniversity ofHongKong,China\nSketch-based Generation\n Sketch-based Editing\nFig. 1. Our SketchFaceNeRF system supports both generation and editing of high-quality facial NeRFs from 2D sketches. As shown in the left half part,\ngiven a hand-drawn sketch (top-left corner), photo-realistic rendering results with different appearances are synthesized from scratch. The detailed geometry\nmodel and free-view rendering results are shown at the bottom. On the right half part, we show sketch-based editing of facial NeRFs and the corresponding\ngeometry,whereoriginalfacesandgeometryareshowninpurpleboxes,andtheresultsoftwoconsecutiveeditingstepsareshowningreenandorangeboxes,\nrespectively. Duringediting,localregions aremodifiedaccording to theeditedsketcheshighlightedin red,while the geometry andappearancefeaturesin\nuneditedregionsarewell preserved.\nRealistic  3D  facial  generation  based  on  Neural  Radiance  Fields  (NeRFs)  from\n2D  sketches  benefits  various  applications.  Despite  the  high  realism  of free-\nview  rendering  results  of NeRFs,  it is tedious  and  difficult  for  artists  to\n∗Corresponding  author  is Lin  Gao  (<EMAIL>).\nAuthors’  addresses:  Lin  Gao,  Feng-Lin  Liu,  Shu-Yu  Chen,  Kaiwen  Jiang  and  Chunpeng  \nLi are  with  the  Beijing  Key  Laboratory  of Mobile  Computing  and  Pervasive  Device,  \nInstitute  of Computing  Technology,  Chinese  Academy  of Sciences.  Lin  Gao  and  Feng-\nLin  Liu  are  also  with  University  of Chinese  Academy  of Sciences.  Kaiwen  Jiang  is also  \nwith  Beijing  Jiaotong  University.  Yu-Kun  Lai  is with  the  School  of Computer  Science  and  \nInformatics,  Cardiff  University.  Hongbo  Fu  is with  the  School  of Creative  Media,  City  \nUniversity  of Hong  Kong.  Authors’  e-mails:  <EMAIL>,  <EMAIL>,  \n<EMAIL>,  <EMAIL>,  <EMAIL>,  <EMAIL>,  \n<EMAIL>,duetoitsconcise-\nnessandexpressiveness,sketchinghasbeenwidelyusedfor2Dfacialimage\ngenerationandediting.ApplyingsketchingtoNeRFsischallengingdueto\ntheinherentuncertaintyfor3Dgenerationwith2Dconstraints,asignificant\ngap in content richness when generating faces from sparse sketches, and\npotential inconsistencies for sequential multi-view editing given only 2D\nsketch inputs. To address these challenges, we present SketchFaceNeRF,\na novel sketch-based 3D facial NeRF generation and editing method, to\nproducefree-viewphoto-realisticimages.Tosolvethechallengeofsketch\nsparsity, we introduce a Sketch Tri-plane Prediction net to first inject the\nappearance intosketches,thusgeneratingfeatures givenreference images\ntoallowcolorandtexturecontrol.Suchfeaturesarethenliftedintocompact\n3D tri-planes to supplement the absent 3D information, which is important\nfor improving robustness and faithfulness. However, during editing, con-\nsistency for unseen or unedited 3D regions is difficult to maintain due to\nlimited spatial hints in sketches. We thus adopt a Mask Fusion module to\ntransformfree-view2Dmasks(inferredfromsketcheditingoperations)into\nthe tri-plane space as 3D masks, which guide the fusion of the original and\nsketch-basedgeneratedfacestosynthesizeeditedfaces.Wefurtherdesignan\noptimization approach with a novel space loss to improve identity retention\n\n1:2 • Lin Gao,Feng-LinLiu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li,Yu-Kun Lai,andHongboFu\nfor 3D face modeling [Han et al .2017, 2018; Yang et al .2021b].\nHowever, these solutions arespecifically designed for mesh-based\nmodels,whichlackhigh-qualitytexturetorenderrealisticimages.\nNeRF naturally synthesizes realistic faces, but applying sketch to\nNeRFischallenging.First,duetothedomaingapbetweensparse,\nmonochromaticsketchesandreal2Dfacialimages,2Dsketch-based\nfacialmodelingisalreadychallenging,nottomentiontheinference\nof 3D information from single-view sketch inputs. Furthermore,\nusers may perform local editing operations from different views.\nSupporting multi-step local manipulations from different views\nandpreservingunedited3Dregionsvia2Dsketchesisnoteasyto\nachieve.\nIn order to generate and edit facial NeRFs from 2D sketches, a\npossible approach is to first leverage 2D sketch-based image gen-\neration methods [Chen et al .2020; Li et al .2019, 2020; Yang et al .\n2021a] or editing methods [Chen et al .2021; Jo and Park 2019; Yang\net al.2020; Zeng et al .2022] to generate photo-realistic facial im-\nages, and then project the generated images into latent space of\n3D GAN such as EG3D [Chan et al .2022]. However, as shown in\nFigs. 8 and 9, these approaches are not robust enough against hand-\ndrawn sketches, so the 2D intermediate faces may have artifacts,\nwhich would be inherited by final projection results. Inspired by\npSp [Richardson et al .2021], another possible solution is to directly\nproject2Dinputsketchesinto3DlatentspacebyaCNNencoder.\nHowever,this approach tendsto overfitsynthesized stylesketches\nbecause of the domain gap, as we will later show in Fig. 8. To solve\nthe above problems, we translate 2D sketches into 3D tri-plane\nfeatures[Chanetal .2022],whichsupplementsketcheswithcolor\nand stereoscopic information (i.e., volumetric distribution of 3D\nfaces) to reduce the domain gap. The tri-plane feature prediction\nstrategynotonlyimprovestherobustnessforhand-drawnsketches\nandaddsappearance control butalsosupports multi-view detailed\nmanipulation because of the representation consistency with the\nexistingtri-plane-basedEG3D generator.\nWe present SketchFaceNeRF, a novel sketch-based facial NeRF\ngenerationandeditingmethod.Itsynthesizeshigh-quality3Dfacial\nNeRFs from scratch with single-view hand-drawn sketches (see Fig.\n1).Asdiscussedbefore,insteadofdirectlyprojecting2Dsketches\ninto 3D latent space, we propose a Sketch Tri-plane Prediction net to\ntranslate 2D sketches into a compact 3D tri-plane representation.\nSpecifically,withanappearancereferenceimage,wefirsttransform\nsketchesinto2Dfeaturemaps,whicharethenliftedinto3Dfeature\nvolumesinthe3DEuclideanvolumerenderingspace.Inspiredby\n[Yuetal.2021],thefeatureofeach3Dpositioniscomputedfrom\nthe2D featuremapsbyperspectiveprojectiontransformationand\nbilinear interpolation. The tri-planefeatures are then generated by\nvolume reshaping and convolutions. It is noteworthy that such a\nmodule is less sensitive to the style of input sketches due to the\ninvolvedtransformationandprojectionprocesses(Fig.8).Thetri-\nplanefeaturesareconcatenatedandencodedintothelatentspace\nof EG3D to synthesize realisticfacialNeRFs.\nGivensynthesizedfacialNeRFs( e.g.,EG3Dsamples,sketch-based\ngenerations,orreal-imageinversions),the3Drepresentationallows\nuserstoeditfacialdetailsindifferentviews.Tosolvethechallengeof\npreservingunedited3Dregionsduringlocaleditingvia2Dsketches,\nwe first estimate 2D masks, which indicate edited regions based onand  editing  faithfulness.  Our  pipeline  enables  users  to flexibly  manipulate  \nfaces  from  different  viewpoints  in 3D  space,  easily  designing  desirable  facial  \nmodels.  Extensive  experiments  validate  that  our  approach  is superior  to the  \nstate-of-the-art  2D  sketch-based  image  generation  and  editing  approaches  \nin realism  and  faithfulness.\nCCS  Concepts:  · Human-centered  computing  →  Graphical  user  inter-\nfaces; · Computer  systems  organization  →  Neural  networks;  · Com-\nputing  methodologies  →  Rendering ; Volumetric  models.\nAdditional  Key  Words  and  Phrases:  Sketch-based  Interaction,  Neural  Radi-\nance  Fields,  Face  Modeling,  Face  Editing\n1 INTRODUCTION\nHighly  realistic  and  stereoscopic  face  modeling  is a popular  topic  in \ncomputer  graphics  and  has  a wide  range  of applications,  including  \ndigital  character  design,  avatar-based  virtual  meetings,  etc. Neverthe-\nless,  creating  high-quality  facial  models  in terms  of both  geometry  \nand  appearance  from  scratch  requires  laborious  authoring  with  \nprofessional  software  (e.g.,  MAYA  [Autodesk,  INC.  2019],  ZBrush  \n[Pixologic  2023]  and  NVIDIA  Omniverse  [NVIDIA  2023]).  More  \nimportantly,  it is very  difficult  for  existing  mesh-based  approaches  \nto render  photo-realistic  facial  images  without  professional  skills  \nor  high  costs,  and  realism  is probably  the  most  critical  aspect  of \npractical  applications.  Thus,  how  to conduct  facial  modeling  in an  \neasy-to-use  yet  realistic  way  is a worth-studying  research  problem.  \nThanks  to the  development  of deep  learning  approaches,  Neural  \nRadiance  Fields  (NeRFs)  [Mildenhall  et al. 2021],  a powerful  implicit  \n3D  representation,  can  easily  reconstruct  face  models  from  multi-\nview  images  and  render  photo-realistic  free-view  results.  However,  \ndirectly  using  a vanilla  NeRF  to perform  face  manipulation  is very  \nchallenging  since  information  describing  only  a specific  object  or  \nscene  is encoded  by  a NeRF  network.  Although  various  3D  GAN  \n(Generative  Adversarial  Network)  approaches  [Chan  et al. 2022;  Gu  \net al. 2022;  Niemeyer  and  Geiger  2021;  Schwarz  et al. 2020]  have  \nbeen  proposed  to generate  facial  NeRFs  by  random  sampling  in-\nstead  of reconstructing  real  scenes,  such  methods  still  lack  detailed  \ncontrol  and  interpretable  manipulations  over  synthesized  faces.\nSeveral  methods  [Bergman  et al. 2022;  Sun  et al. 2022b;  Tang  et al. \n2022;  Wu  et al. 2022]  have  attempted  to address  these  issues  by  \nusing  sliders  to interactively  edit  predefined  attributes  based  on  \n3DMM  (3D  Morphable  Models)  such  as FLAME  [Li  et al. 2017],  but  \nthey  provide  limited  manipulation  freedoms.  On  the  other  hand,  it \nis natural  for  humans  to describe  images  with  a long-lasting  tool,  \nnamely  pens,  which  can  also  be  utilized  for  efficient  and  realistic  \nmanipulation  in the  context  of 3D  facial  GAN.  Previous  methods  \n[Jiang  et al. 2022;  Sun  et al. 2022a,c]  resort  to semantic  masks  as  \nan  editing  interface,  which,  however,  does  not  offer  fine  control  of \nfacial  details  like  hairstyles,  beards,  etc.  Another  promising  pen-\nbased  editing  interface  is sketching.  2D  sketching  has  been  widely  \nused  to condition  facial  image  generation  [Chen  et al. 2020;  Li et al. \n2019;  Su  et al. 2022]  and  editing  [Chen  et al. 2021;  Jo and  Park  2019;  \nZeng  et al. 2022].  Sketch-based  interfaces  have  also  been  explored\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:3\nthe user-performed sketch creation and erasing operations. With\nthe rendered depth maps of NeRF models, a Mask Fusion module\nfurther lifts the estimated 2D masks into 3D masks, which are used\nto pick features from the original tri-plane features in the unedited\nregions and predicted tri-plane features from the edited sketches in\nthe edited regions. The fused tri-plane features are encoded back\ninto thelatent spaceofEG3Dto predict an initial editedface. We\ninclude an optimization process with a novel space loss term to\nfurther ensure faithfulness and consistency for challenging cases\n(seeexamplesinFig.12).Notethatourpipelinecanbeperformed\nrepeatedly on a single face, supporting multi-step 3D-aware human\nface editingfromdifferent views via hand-drawn sketches.\nThemaincontributionsofthisworkcanbesummarizedasfol-\nlows:\n•Wepropose the first novel sketch-based 3Dfacial NeRF gen-\neration and editing method, which enables a user-friendly\ninterfaceforauthoring3D-awarefacesandproducesphoto-\nrealisticresults.\n•We develop a novel network for translating 2D sketches to\n3DfacialNeRFs.Single-viewsketchesareaugmentedincolor\nandvolumetricspacetoimprovetherobustnessagainsthand-\ndrawn sketchesandallowappearancecontrol.\n•We introduce a mask fusion module and a sketch-based opti-\nmization approach to achieve detailed editing while preserv-\ningtheoriginal facialfeatures in uneditedregions.\n2 RELATED WORK\nOur work is related to existing works, including facial NeRF gener-\nationandediting,neuralsketch-basedfacegeneration,andsketch\nrendering of 3Dshapes.\n2.1 Facial NeRF Generationand Editing\nGenerativeNeRFs. Existing2Dimagegenerationmethods[Karras\net al.2019,2020] randomly samplefromaGaussian distribution to\ngeneratehigh-qualityfacialimages.Utilizingonly2Dimagedatasets,\nmanyworksfurtherapplythisideato3DgenerationwithNeRFs\n[Mildenhalletal .2021].Forexample,GRAF[Schwarzetal .2020]\nfirst conditions a coordinate-based MLP (Multi-layer Perception)\nrepresentation in NeRF on the additional shape and appearance\ncodesandutilizesamulti-scalepatch-baseddiscriminatorinsteadof\nareconstructionlosstotrainthemodels.Pi-GAN[Chanetal .2021]\nfurther uses a SIREN-based network [Sitzmann et al .2020] with\nFiLM [Perez et al .2018] conditioning to improvethe image quality.\nHowever,duringGANtraining,completeimagesarerenderedin-\nsteadofindividualrays,sotheresolutionofresultsislimiteddueto\nmemory restriction. To address this issue, GIRAFFE [Niemeyer and\nGeiger2021]generateslow-resolutionfeaturemapsbasedonvol-\numerendering,followedbya2DCNN-basednetworktoachievefast\ninference and super-resolution. This approach has been extensively\nused in subsequent works, but the adopted 2D network seriously\naffectstheviewconsistency.Toaddressthis,StyleNeRF[Guetal .\n2022] proposes a specific upsampler combined with a NeRF path\nregularizationlosstoreducethe3D-inconsistencyartifacts.Many\nworks propose novel representations of feature fields to improve\nthe quality and efficiency further. For example, StyleSDF [Or-Elet al.2022] builds an architecture with an SDF (Signed Distance\nField)-based 3Dvolume renderer toachieveview-consistentfacial\nresults with more detailed 3D shapes. GRAM [Deng et al .2022] rep-\nresents radiance fields as a set of implicit surfaces, replacing dense\nMonteCarlosamplingwithafewintersectionpointstorenderhigh-\nresolution images directly. EG3D [Chan et al .2022] concurrently\nintroducesalightweighttri-plane3Drepresentation,combinedwith\na super-resolution network and dual discrimination, to ensure view\nconsistency and image quality. Instead of random sampling, we\ntranslate a2D sketch into atri-plane representation to supportde-\ntailedcontrolinfacialNeRFgeneration.Tomaintain3Dconsistency\nduring editing, local swapping and fusion operations are further\nproposedin thetri-planespace.\nFacial NeRF Editing. Besides multi-view image reconstruction or\nrandom generation, many works generate radiance fields based on\nsingle-view inputs, including RGB images [Yu et al .2021], and even\nsemanticmasks[Chenetal .2022]orsketches[Joetal .2021].Instead\nofconditionalgeneration,semanticmaskshavebeenfurtherutilized\ntoachievestructureandappearancedisentanglement,supporting\ndetailedradiancefieldediting.FENeRF[Sunetal .2022c]usestwo\ndecoupled latent codes to generate spatially-aligned semantics and\ntexture volumes, which share the same geometry but with different\ndiscriminatorsforsupervision.Basedonthetri-planerepresenta-\ntion, IDE-3D [Sun et al .2022a] and NeRFFaceEditing [Jiang et al .\n2022]generategeometryplanesforsemanticvolumerenderingand\ngeometrycontrolaswellasappearanceplanesfortexturecontrol.\nUnlike contiguous 3D semantic masks, a sketch is view-dependent,\nespecially at the surface contour, making it unsuitable for 3D ge-\nometry and appearance decomposition. Besides, the above methods\nedit facial radiance fields based on time-consuming optimization or\nsingle-view encoders. In contrast, our approach can efficiently edit\nfacial NeRFs in free views with a carefully designed prediction and\nrefinement architecture.\n2.2 NeuralSketch-basedFace Generation\nSketch-based 2D Facial Image Generation. Sketching has been\nwidely used in facial image generation and editing. A pioneer work\n[Huetal.2013]representsimagesintoahierarchicalrepresentation\nand supports sketch-based editing by retrieval. Since there is a\ndomaingapbetweenfreehandsketchesandsyntheticedgemaps,\nexistingapproaches[Chenetal .2020;Lietal .2019,2020;Suetal .\n2022; Yang et al .2021a] introduce various strategies to improve\nthe robustness against different styles of freehand sketches. Instead\nofsynthesizingnewfaces,sketch-basededitingapproaches[Chen\net al.2021; Jo and Park 2019; Liu et al .2022, 2021; Portenier et al .\n2018;Yuetal .2019;Zengetal .2022]aimtomanipulaterealfacial\nimageswhileretainingtheoriginalidentityfeatures.Human-drawn\norpredicted2Dmasksareusuallyutilizedtoachievelocalediting\nresults.Theaboveapproachesonlysynthesize2Dresults,whileour\nmethodappliessketchingtorealistic3Dfacegenerationandediting\nin NeRF. Instead of generating RGB images, we first generate 3D\ntri-plane features from single-view sketchesand then project them\nintothelatentspaceofEG3D.Similarmaskguidanceisalsoadapted\ninto3Dspace to supportlocalmanipulations.\n\n1:4 • Lin Gao,Feng-LinLiu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li,Yu-Kun Lai,andHongboFu\nsingle-scenestyletransferandcannotbeusedforgenerativeNeRFs,\nsuch asEG3D [Chan et al .2022]. Besides,sincetheabovemethods\nstylizeascenebasedonasingleexampleimage,itisunclearhow\nto applythese methodstopairedimagedatasets forstylization.\n3 METHOD\n3.1 OverviewandPreliminaries\nFig.2illustratesoursketch-basedfacialNeRFgenerationandediting\nframework. In Sec. 3.2, we describe the sketch-based facial NeRF\ngenerationapproach.Takingasinputsa2Dsketchandanappear-\nance reference image, a Sketch Tri-plane Prediction net augments\nthe sketch with color and stereoscopic information to synthesize\ntri-plane features, which are projected into EG3D’s latent space\ntogeneratehigh-qualityfacialNeRFs.InSec.3.3,wedescribeour\nsketch-basedfacialNeRFeditingapproach.Tosupportfreeviewedit-\ningandmaintaintheidentitycharacteristicsinunseenandunedited\n3Dregions,thetri-planefeaturessynthesizedbytheeditedsketches\nandtheoriginaltri-planefeaturesarefusedbya MaskFusion module,\nandencodedbackintothelatentspaceofEG3D.Then,wefurther\nrefine the rendered result by a latent code optimization with sketch\nconstraints. It should be noted that the sketch-based generation\nandeditingsharethesame3Dtri-planepredictionandprojection\nnetworks.Thesketch-basedgeneratedfacescanbefurtheredited\nto achieve detailed local manipulations from flexible viewpoints, as\nshowninFig.6.Thisarchitecturehelpsuserseasilydesign3Dfacial\nNeRFs withdetailedcontrol.\nWebuildourapproach basedon EG3D [Chan et al .2022], apre-\ntrained 3D face NeRF generator. Given the latent code 𝑤, three\northogonal plane features (referred to as tri-plane features) 𝑝𝑥𝑦,\n𝑝𝑥𝑧, and𝑝𝑦𝑧are generated from the StyleGAN2 [Karras et al .2020]\nbackbone. Each 3D queried position 𝑥∈R3is projected onto the\nthreefeatureplanestogetthecorrespondingfeatures 𝐹𝑥𝑦,𝐹𝑥𝑧,and\n𝐹𝑦𝑧viabilinearinterpolation,andsuchfeaturesarethenaggregated\ninto3Dfeatures 𝐹viasummation.Animagedecoderinterprets 𝐹to\ncolor features and densities, with the subsequent volume rendering\n[Mildenhalletal .2021]tosynthesizelow-resolutionfeaturemaps\nwhosefirstthreechannelsareRGBimages.Asuper-resolutionmod-\nule further translates the feature maps into high-resolution images.\nMore details of the generator can be found in [Chan et al .2022].\nOur method generates 3D faces in EG3D’s W+space, composed\nof 14 different latent codes. The efficient tri-plane representation is\nalsoutilizedin our3Dfeaturerepresentation.\n3.2 Sketch-basedFacial NeRF Generation\nFaithfullytranslatinghand-drawn2Dsketchesintorealistic3Dfaces\nis an attractive but challenging task. A naïve approach is to directly\nencodetheinput2DsketchesintothelatentspaceofEG3Dwitha2D\nencoder(e.g.,[Richardsonetal .2021])andutilizestyle-mixing[Chan\netal.2022;Karrasetal .2020]tocontroltheappearance.However,\nsince the encoder is originally designed for 2D GANs and the style-\nmixing cannot exactly control the appearance in EG3D [Chan et al .\n2022],such asolution isnotrobustto theinput sketchesat details\nsuch as hairstyles and does not guarantee accurate appearance\nstyles,asshowninFig.8.Incontrast,wearemotivatedtofirstlift\n2Dinputsinto3Dinputsandthenencodetheminto3Doutputs.TheSketch-based  3D  Facial  Model  Generation.  Many  efforts  have  been  \nmade  to utilize  sketching  to design  3D  face  geometry.  One  category  \nof methods  [Han  et al. 2017;  Huang  et al. 2022b]  predicts  the  coeffi-\ncients  of a bilinear  face  representation  based  on  sketches,  combined  \nwith  displace  maps  [Ling  et al. 2022;  Yang  et al. 2021b]  to manipulate  \nsurface  details.  Another  category  of approaches  [Du  et al. 2020;  Han  \net al. 2018;  Luo  et al. 2021]  utilizes  sketches  to guide  the  template  de-\nformation  and  generates  diverse  types  of 3D  models  such  as animal  \nfaces.  These  previous  methods  succeed  in generating  3D  models,  but  \nthey  could  not  produce  photo-realistic  face  images  directly  since  it \nis difficult  to estimate  high-quality  texture  maps  and  materials  only  \nfrom  sketches.  Moreover,  most  of the  above  approaches  only  focus  \non  face  regions  separately  without  hair  and  facial  details  like  pupils.  \nIn our  method,  the  3D-aware  hair  and  pupils  are  generated  together.  \nThanks  to the  NeRF  representation,  our  method  not  only  gener-\nates  photo-realistic  3D  faces  from  2D  sketches,  but  also  constructs  \nhigh-fidelity  facial  geometry,  as  shown  in Fig.  4.\n2.3  Sketch  Rendering  of 3D  Shapes\nRendering  high-quality  sketches  of 3D  shapes  benefits  sketch-based  \nmodeling  since  paired  training  data  can  be  synthesized  and  ana-\nlyzed.  A differentiable  sketch  rendering  approach  can  further  utilize  \nsketches  as  constraints  to optimize  models.  To  render  sketches,  a \nstraightforward  approach  is to first  render  the  depth,  normal  or RGB  \nmaps,  and  then  utilize  image-space  algorithms  [Canny  1986;  Vinker  \net al. 2022;  Wang  et al. 2018;  Xie  and  Tu  2015;  Yi  et al. 2020,  2019]  \nto generate  line  drawings.  However,  these  image-based  methods  \ntend  to generate  inconsistent  results  across  views  because  of the  \ninsufficient  utilization  of 3D  information.  Another  branch  of meth-\nods  analyzes  the  mathematical  features  of surface  geometry  and  \ndefines  different  shape-depicted  lines  [Bénard  et al. 2019;  DeCarlo  \net al. 2003;  Judd  et al. 2007;  Ohtake  et al. 2004],  which  are  combined  \ntogether  [Liu  et al. 2020]  to generate  high-quality  sketches.  How-\never,  these  methods  require  extremely  high-quality  explicit  mesh  \nmodels  as inputs,  which  are  not  cheap  to obtain  from  a 3D  implicit  \nrepresentation  like  NeRF  with  commonly  used  techniques,  such  as \nmarching  cubes  [Lorensen  and  Cline  1987]  and  DMTet  [Shen  et al. \n2021].\nIn order  to synthesize  high-quality  sketches  in NeRF,  neural  style  \ntransfer  [Gatys  et al. 2016]  is a promising  solution.  However,  there  \nare  two  main  challenges  in NeRF  style  transfer:  extensive  mem-\nory  usage  caused  by  rendering  whole  images  during  transfer  loss  \ncalculation  and  view  inconsistency  when  adapting  image  transfer  \nalgorithms.  Chiang  et al.  [2022]  first  propose  a memory-efficient  \npatch  sub-sampling  algorithm  and  train  a color  branch  with  a fixed  \ngeometry  branch.  StylizedNeRF  [Huang  et al. 2022a]  further  de-\nsigns  a mutual  learning  framework  and  learnable  conditional  latent  \ncodes  to improve  the  view  consistency.  Other  methods  maintain  the  \noriginal  NeRF  networks  but  design  novel  training  strategies.  For  \nexample,  SNeRF  [Nguyen-Phuoc  et al. 2022]  alternates  the  NeRF  \ntraining  and  stylization  optimization  steps,  consuming  less  memory  \nin each  stage  while  retaining  the  original  consistency  of NeRF.  ARF  \n[Zhang  et al. 2022]  introduces  a new  view-consistent  style  loss  and  a \ndeferred  back-propagation  method  to enable  memory-intensive  op-\ntimization.  However,  existing  methods  are  specifically  designed  for\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:5\nLatent Code Optimization From Sketch\nSketch\nRenderingImage\nRendering\nTranslation \nNetworkTranslation \nNetwork\ncGcG\nSketch Tri-plane Prediction Network\nFeature MapInput Sketch Feature V olumeReshape\nConcat\nInput View\nInput Viewzy\nx\nsp\neditwcfcE\nMask Fusion \nModuleEG3DEditing\nOri-Image Ori-Sketch Binary MaskStrokes\nprojE\nConv \nNetworkConv\nNetwork\nvGvG\npII\nw\nepGenerationGenerationS\nFig. 2. Overview of our unified sketch-based facial NeRF generation and editing framework. Given an input sketch 𝑆, theSketch Tri-plane Prediction Network\ngeneratesatri-planefeaturerepresentation 𝑝𝑠for3Dinformation.Thecolorinformationissupplementedbyanappearanceencoder 𝐸𝑐andatranslation\nnetwork𝐺𝑐togenerateacolorizedfeaturemap 𝑓𝑐,andthestereoscopicinformationissupplementedby 𝐺𝑣.TogenerateafacialNeRFfromscratch(indicated\nbythedottedlinebelow 𝑝𝑠),thetri-planefeature 𝑝𝑠isdirectlyencodedby 𝐸𝑝𝑟𝑜𝑗togenerate3Dfaces.ForfacialNeRFediting(indicatedbythesolidline\nbelow𝑝𝑠),theMaskFusion modulefuses 𝑝𝑠andtheoriginaltri-planefeature 𝑝togenerate 𝑝𝑒,whichisalsoencodedbytheshared 𝐸𝑝𝑟𝑜𝑗.Moreover,we\npropose a sketch-basedoptimization approach toimproveediting faithfulness and originalfeatureretentionfurther.\ntri-plane representation is utilized because of its high expressive\ncapacity andadaptabilityfor2Dconvolutionin encoders.\nIn particular, to lift 2D inputs into 3D, we draw inspiration from\nPixelNeRF[Yuetal .2021],whichisdevisedtoinfer3Dinformation\nfromsparse2Dinputs.Givenasingle-viewhand-drawnsketch 𝑆,\nwedesigna SketchTri-planePrediction nettolift 𝑆intoa3Dtri-plane\nrepresentation 𝑝𝑠, including feature maps 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧, and𝑝𝑠𝑦𝑧, which\nisexpectedto liein thesamedistributionastheEG3D-generated\ntri-planerepresentation.Theinputsketchonlycontainsgeometry\ninformation, but the tri-plane features and rendered images have\ndiverseappearancedetails. Sowefirst translatethe input sketch 𝑆\ntoacolorizedfeaturemap 𝑓𝑐toinjectcolors,lighting,andtexture\ninformation. We train an appearance encoder 𝐸𝑐to extract the\nappearance information from the reference image 𝐼. To transfer\nthe appearance to 𝑆, we leverage a translation network 𝐺𝑐with\nadaptiveinstancenormalizationHuangandBelongie2017\nto generatethecolorizedfeaturemap:\n𝑓𝑐=𝐺𝑐(𝑆,𝐸𝑐(𝐼)). (1)\nTo predict a 3D tri-plane representation from a 2D feature map, we\nbuild a3D featurevolume in the Euclidean space wherethe volume\nrendering is performed.Using theestimated cameraintrinsics and\nextrinsics,eachpoint 𝑥inthe3Dvolumeisprojectedontotheinput\nimagespacetoget2Dcoordinates 𝜋(𝑥).Then,thecorresponding\nfeaturevector 𝑓𝑐(𝜋(𝑥))is retrieved for each point 𝑥via bilinear in-\nterpolationfromcolorizedfeaturemaps 𝑓𝑐.Tobalanceperformance\nand efficiency, we build the feature volume with a resolution of 128\nineachaxis,resultinginthefinalshapeof128 ×128×128×3,where\n3 is the channel number of feature maps 𝑓𝑐. The 3D feature volume\nisfurtherreshapedalongeachaxistogeneratethree128 ×128×384\nfeature maps, denoted as 𝑉𝑥𝑦,𝑉𝑥𝑧, and𝑉𝑦𝑧. These feature mapsare concatenated in the feature channel, using a 2D convolution\nnetwork𝐺𝑣toupsampleandtranslatethemintoa256 ×256×96\nsketch feature map, which is split channel-wise and reshaped to\nformthree32-channelfeatureplanes 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧,𝑝𝑠𝑦𝑧, abbreviatedas\nthetri-planefeature 𝑝𝑠:\n𝑝𝑠=𝐺𝑣(𝑉𝑥𝑦,𝑉𝑥𝑧,𝑉𝑦𝑧). (2)\nAlthough  our  Sketch  Tri-plane  Prediction  net  thoroughly  analyzes  \nthe  3D  information,  only  2D  convolution  is utilized  here  to improve  \nmemory  and  time  efficiency.  After  lifting  the  2D  inputs  into  3D  as the  \ntri-plane  representation,  we  utilize  a 2D  encoder  𝐸𝑝𝑟𝑜  𝑗 [Richardson  \net al. 2021]  to project  the  tri-plane  features  into  the  W+ space  of \nEG3D  to improve  the  quality,  which  renders  final  photo-realistic  \nfree-view  facial  images.\nTraining  Objective.  The  Sketch  Tri-plane  Prediction  net  is trained  \nusing  a synthesized  multi-view  dataset.  Given  a set  of latent  codes,  \nground-truth  tri-plane  features  𝑝 are  synthesized  based  on  EG3D.  \nFor  each  example,  we  randomly  sample  multiple  camera  poses  to \nsynthesize  paired  sketches  and  images,  using  the  sketch  generation  \napproach  discussed  in Sec.  3.3.1.\nGiven  an  input  single-view  sketch,  the  Sketch  Tri-plane  Prediction  \nnet  synthesizes  a tri-plane  feature  𝑝𝑠 , which  generates  images  𝐼𝑠 \nthrough  volume  rendering  with  the  original  decoder  of EG3D  [Chan  \net al. 2022].  These  rendered  images  have  different  views  𝑡 from  the  \ninput  sketch.  The  above  strategy  enhances  the  3D  information  by  \nenforcing  the  network  to imagine  faces  from  other  views,  as in [Sun  \net al. 2022a].  The  loss  function  L(𝐸𝑐, 𝐺𝑐, 𝐺𝑣) to train  the  Sketch  \nTri-plane  Prediction  net  is defined  as:\nL(𝐸𝑐, 𝐺𝑐, 𝐺𝑣) = 𝛽1L1 (𝑝𝑠, 𝑝) + 𝛽2L1 (𝐼𝑠, 𝐼𝑡 ) + 𝛽3L𝑉𝐺𝐺  (𝐼𝑠, 𝐼𝑡 ), (3)\n\n1:6 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\nwhereL1denotes the L1 distance, LVGGdenotes the perception\ndistance [Zhang et al .2018], and 𝐼𝑡is the ground-truth image in\natargetview.Forhigh-qualityresults,webothconstrainthesyn-\nthesizedtri-planerepresentation 𝑝𝑠tobeinthesamedistribution\nastheground-truthtri-planefeatures 𝑝,andmeasurethesimilar-\nitybetweentherenderedimagesandground-truthimages.Inour\nexperiments, weset 𝛽1=0.01,𝛽2=1.0, and𝛽3=0.1.\nTo train the encoder 𝐸𝑝𝑟𝑜𝑗, we use the same strategy of pSp\n[Richardsonetal .2021],butinsteadofRGBimages,ittakesconcate-\nnatedtri-planefeatures 𝑝𝑠asinput.Bothofthetri-planefeatures\ngenerated by the original EG3D backbone and the Sketch Tri-plane\nPrediction netarefedintothenetworktoimprovethegeneralization\nof𝐸𝑝𝑟𝑜𝑗. Pixel-wise L2 loss, LPIPS loss [Zhang et al .2018], iden-\ntity loss [Deng et al .2019a], and regularization loss are used for\ntraining such an encoder. Please refer to [Richardson et al .2021]\nfor more details. We first train the Sketch Tri-plane Prediction net to\nconvergence andthen traintheencoder.\n3.3 Sketch-basedFacial NeRF Editing\nAlthoughthegenerationof3Dfacesfrom2Dsketchesisqualifiedfor\napplicationslikecharacterdesign,usersmaydesiretofurtheradjust\n3D faces by interactively editing the corresponding 2D sketches\nfrom different views, as in [Chowdhury et al .2022]. This motivates\nus to design a sketch-based interface for facial NeRF editing, which\nallows2Dlocaleditingfromdifferentviewsandachievesconsistent\n3Deditingeffects while preservingtheuneditedregions.\n3.3.1 Free-viewSketchGeneration. Firstofall,wesynthesizefree-\nviewrenderingsketcheswhichuserscanmodifytoeditthecorre-\nsponding3Dfaces.Thisprocessisdifferentiableandfurtherused\nforlatentcodeoptimizationasdiscussedinSec.3.3.3.Anadditional\nsketchgenerationpath,whichhasasimilararchitecturetotheimage\ngenerationpathofEG3D[Chanetal .2022],isthereforedesigned\ntogeneratethecorrespondingsketchatanyviewofalatentcode.\nThesketchandimagerenderingbranchessharethesameStyleGAN\nbackboneandprojectionfeaturesbuthaveseparatedecodersand\nsuper-resolution modules. Specifically, a new sketch decoder in-\nterpretsthesamplefeaturestosketchfeatures,combinedwiththe\ndensityofthe image branch togenerate low-resolutionsketch fea-\nturemapsbasedonvolumerendering.The1stchannelofthesketch\nfeature maps corresponds to low-resolution sketches (128 ×128),\ndenotedas 𝑆′\n𝑟𝑎𝑤.Asketchsuper-resolutionmodulesimilartothe\noriginalsuper-resolutionmoduleinEG3Disalsousedtosynthesize\nfinalsketches 𝑆′.\nTo train such sketch generation path, a pretrained pix2pixHD\n[Wang et al .2018] is used to convert rendered facial images into\nground-truth high-resolution sketch 𝑆𝐺𝑇, which is, however not\n3D-consistent. We carefully design training losses to ensure consis-\ntency from different views and synthesize high-quality sketches. A\nreconstructionlossisusedtomatchtheoriginalsketchdistribution:\nL𝑟𝑒𝑐𝑜𝑛=𝛼1L1(𝑆′,𝑆𝐺𝑇) +𝛼2L1(𝑆′\n𝑟𝑎𝑤,𝑆𝑟𝑎𝑤)\n+𝛼3L𝑉𝐺𝐺(𝑆′,𝑆𝐺𝑇) +𝛼4L𝑉𝐺𝐺(𝑆′\n𝑟𝑎𝑤,𝑆𝑟𝑎𝑤),(4)Inspired by [Gu et al .2022], we utilize a regularization term to\nenforce the 3D consistency of sketches. Although the predicted\nground-truth sketch 𝑆𝐺𝑇is not view-consistent, the inherent multi-\nviewconsistencyofvolumerenderingconstrainsthefinalresults\nof the super-resolution module. This loss term compares the sub-\nsampledpixels on thefinalsketch resultsandthosegeneratedby\nNeRF:\nL𝑣𝑖𝑒𝑤=𝛼51\n/summationtext.1\n(𝑖,𝑗)∈𝐶/bardblex/bardblex/bardblex𝑆′[𝑖, 𝑗] −𝑅𝑒𝑛𝑑𝑒𝑟(𝑟𝑖,𝑗)/bardblex/bardblex/bardblex1,(5)\nwhere𝐶is the set of randomly sampled pixels in final sketches\nand𝑖, 𝑗are the coordinates in the high-resolution2D sketch space,\n𝑅𝑒𝑛𝑑𝑒𝑟(𝑟𝑖,𝑗)is thepixel result of direct volume rendering with the\ncorrespondingray 𝑟𝑖,𝑗.Inourexperiments, 𝛼5=3.0and=8,192.\nThe finaltrainingobjectiveis:\nL𝑠𝑘𝑒𝑡𝑐ℎ=L𝑟𝑒𝑐𝑜𝑛+L𝑣𝑖𝑒𝑤. (6)\nDuring the training of the free-view sketch generation, the weights\nof the StyleGAN backbone and the image generation path are fixed.\nWeonlyupdatetheweightsofthesketchdecoderandsketchsuper-\nresolution module.\nFinally,usersareabletodirectlyedittherenderedsketches(adding\nor removing strokes) to obtain edited sketches 𝑆instead of drawing\nsketchesforevery viewfromscratch.\n3.3.2 Mask Fusion Module. Given the modified sketch 𝑆, the intro-\nducedSketch Tri-plane Prediction net together with 𝐸projin Sec. 3.2\nisalreadyabletogeneratehigh-quality3Dfaces.However,unedited\nregions might suffer from undesirable changes despite local editing\noperationsonthe2Dsketchsincethe2Dsketchatacertainview\ncannot describe the entire 3D object, as demonstrated in Fig. 13.\nTargetingthisissue,weproposea Mask Fusion moduletospatially\nfuse the original 3D face generated from the input latent code 𝑤,\nand the newly generated 3D face based on the edited sketch with a\n3Dmask, preserving the unedited regionsand retaining theedited\nregions. Notice that since both faces are represented as tri-plane\nfeatures, the fusion can be conducted on tri-plane features directly.\nSubsequently,thespatiallyfused3Dfaceisencodedbackintothe\nlatent space of EG3D as 𝑤𝑒𝑑𝑖𝑡. It should be noticed that the input\nlatent code 𝑤can be an EG3D sample, real image projection, or\nthe previously edited result to apply multi-step manipulations from\ndifferent views.\nTo obtain the guiding 3D fusion mask, after users perform the\neditingoperationsviaourinterface,wefirstestimatea2Dbinary\nmask𝑀indicating the edited regions (introduced in Sec. 4). For\ntheinputlatentcode 𝑤,theoriginaltri-planerepresentation( 𝑝𝑥𝑦,\n𝑝𝑥𝑧,𝑝𝑦𝑧) is synthesized based on the EG3D backbone along with a\ngenerateddepthmap 𝐷.Basedonthedepthmap 𝐷,eachpixelin\n𝑀is converted to its 3D location in the Euclidean space, forming a\nsetof 3Dpoints. Sincethefusionisessentially conductedonthetri-\nplanefeatures,suchasetof3Dpointsareprojectedintothetri-plane\nspace to synthesize three sets of 2D points. For the modified sketch\n𝑆𝑒𝑑𝑖𝑡, anewtri-planerepresentation( 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧,𝑝𝑠𝑦𝑧)issynthesized\nbytheSketchTri-planePrediction netalongwithitsgenerateddepth\nmap, and another three sets of 2D points are obtained similarly.\nBy uniting two sets of 2D points on each plane, dilating them for\na smoother border, and connecting sufficiently close regions, wewhere  𝑆𝑟𝑎𝑤  represents  the  downsampled  sketch  of a high-resolution  \nsketch  𝑆𝐺𝑇  . In our  experiments,  we  empirically  set  𝛼1 = 𝛼2 = 3.0 \nand  𝛼3 = 𝛼4 = 2.0.\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:7\nobtain the guiding 3D fusion mask, which is composed of three 2D\nfusionmasks on eachplane, denotedas 𝑀𝑥𝑦,𝑀𝑥𝑧,𝑀𝑦𝑧.\nThen, theoriginal andpredictedtri-planefeatures arefusedas:\n𝑃𝑒\n𝑥𝑦=𝑀𝑥𝑦·𝑝𝑠\n𝑥𝑦+ (1−𝑀𝑥𝑦) ·𝑝𝑥𝑦. (7)\nHereweonlytake 𝑥𝑦asanexample,andoperationsontheother\ntwo planes are the same. We denote the generated new fused tri-\nplanefeaturesas 𝑝𝑒𝑥𝑦,𝑝𝑒𝑥𝑧,𝑝𝑒𝑦𝑧.Itshouldbenoticedthatswapping\naregioninthetri-planefeaturesaffectsthewholeorthogonal3D\ncolumnarspace,sothefusedtri-planefeaturescannotbedirectly\nused for volume rendering. To solve this problem, we utilize the\nsame encoder 𝐸𝑝𝑟𝑜𝑗in Sec. 3.2 to further project the fused tri-plane\nfeatureintothe W+space, as 𝑤𝑒𝑑𝑖𝑡=𝐸𝑝𝑟𝑜𝑗(𝑝𝑒𝑥𝑦,𝑝𝑒𝑥𝑧,𝑝𝑒𝑦𝑧).\n3.3.3 Latent Code Optimization from Sketch. We further refine\n𝑤𝑒𝑑𝑖𝑡toensurebettersketchfaithfulnessandoriginalfeaturereten-\ntion in challenging cases. For example, the predicted hair structure\noreyeglassshapesmayhavesmallbiaseswithdrawnsketches,as\nshown in Fig. 12. The background of the original faces is some-\ntimes too sophisticated to reconstruct. Our optimization strategy is\ndesignedto solvethese problemsin challenging editingsituations.\nEditing Optimization. Inspired by [Liu et al .2022], we propose\nan optimization approach to refine the predicted latent code 𝑤𝑒𝑑𝑖𝑡.\nWith the image rendering branch R𝑥and the sketch rendering\nbranchR𝑠, thefollowinglosstermsareoptimized:\nL𝑒𝑑𝑖𝑡=L𝑉𝐺𝐺(R𝑠(𝑤𝑒𝑑𝑖𝑡) ⊙𝑀,𝑆⊙𝑀), (8)\nL𝑖𝑚𝑔=L𝑉𝐺𝐺(R𝑥(𝑤𝑒𝑑𝑖𝑡) ⊙/tildewide𝑀,R𝑥(𝑤) ⊙/tildewide𝑀),(9)\nwhere/tildewide𝑀referstotheuneditedregions,and ⊙denotespixel-wise\nmultiplication. These loss terms encourage the edited regions to\nhaveconsistentsketchesas 𝑆andremainingregionstobeunaltered\nasmuch aspossible.\nUtilizingtheabovelosstermsisadequatetosolvethe2Dimage\neditingproblembutnotenoughfor3DfaceeditinginNeRF,sincethe\nstereoscopic features should be maintained to ensure that unedited\nregions are retained in arbitrary views. One possible approach is to\naddmulti-viewlosses,butviewpointselectionandview-specific2D\nmask transformation are difficult. So, we propose a novel space loss\nterm to measure the similarity of sample points’ features utilized in\nvolume rendering:\nL𝑠𝑝𝑎𝑐𝑒=1\n𝑁/tildewidest/summationtext.1/tildewide𝑀𝑟/summationtext.1𝑁\n𝑖∥Φ(𝑤𝑒𝑑𝑖𝑡,𝑟(𝑖)) −Φ(𝑤,𝑟(𝑖))∥1,(10)\nwhere𝑟(𝑖)is the𝑖th sample point along the rendering ray 𝑟in\nunedited regions, 𝑁is the number of sample points, and Φde-\nnotesthepoint-wisefeaturecalculationprocess,includingtri-plane\nprojection, and feature decoding. Hierarchical volume sampling\n[Mildenhall et al .2021] is used in NeRF rendering, while we only\ncalculate the space loss on coarse samples which have the same\npositionfordifferentidentities.Theoveralllossfunctionforopti-\nmizationis:\nL(𝑤𝑒𝑑𝑖𝑡)=𝛾1L𝑒𝑑𝑖𝑡+𝛾2L𝑖𝑚𝑔+𝛾3L𝑠𝑝𝑎𝑐𝑒,(11)\nwhere𝛾1,𝛾2, and𝛾3are hyper-parameters tuned by users. In our\nexperiments,theyaresetas 𝛾1=40,𝛾2=20,and𝛾3=0.2bydefault,\nFig. 3. The user interface of SketchFaceNeRF. The interface has two modes.\nIn the generation mode, the left window is used for creating freehand\nsketches. In the editing mode, the left window is used for editing line\ndrawings from a 3D face. In both modes, the right window shows the\ngeneratedface.Acontrolpanelatthetopoftheinterfacesupportsmany\nessentialoperations,includingtheselectionofpenciloreraser,brushsize\ncontrol, and rotationof viewpoints.\nandtheiterationstepsaresetas10withthebalanceofefficiency\nandquality.\n4 USER INTERFACE\nAsshowninFig.3,wedesignauserinterfaceontopoftheproposed\npipelinetosupportsketch-basedfacialgenerationandmulti-step\nediting in different views. To generate facial NeRFs from scratch,\nusers draw sketches on the left drawing canvas, and our system\nthensynthesizesanddisplaysphoto-realistic3Dfacesintheright\nwindow.Ourinterfacefurthersupportsdetailedfacialeditingvia\nediting sketches synthesized from previously generated 3D faces\n(Fig. 6), EG3D random samples, and real face images (Fig. 5). Users\ncan change the views with the sliderson the controlpanel, during\nwhichprocessthegenerated3Dfacesandcorrespondingsketches\nare rotated simultaneously. Thanks to our free-view sketch gen-\neration approach, the synthesized sketches are consistent during\nview changes, thus improving the user interaction experience. Dur-\ning editing, users may erase undesired lines and draw new lines\ndepicting desired structures. These operations provide adequate\ninformation to infer the mask 𝑀representing the edited regions.\nSpecifically,wedilatethenewlydrawnlinesandunitethemwith\nthe erasing regions to generate an initial mask. Then, the small\nholeswithintheeditedregionsarefilledbyconnectiondetection,\nand the border is smoothed by polygonal curves. With the input\nface,modifiedsketch,andinferredmask,ouralgorithmgenerates\nnew edited NeRF faces, which are rendered and shown in our UI\nsystem.Aftereditinginasingleviewpoint,userscanrotatetheface\nand continuously edit it in other views, supporting detailed and\nexpressivefacialmanipulation.\n\n1:8 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\nFig.4. FacialNeRFgenerationresultsgivenhand-drawnsketches.Theinputsketchesandgeneratedgeometryareshowninthefirsttwocolumns.Photo-\nrealistic rendering results withfree viewpoints are shown in the following columns, with different appearance images in the top-left corner of each example.\nOurmethodgeneratesdetailedgeometrybysketchesandcontrolstheappearancewithreferenceimages.Sincewefocusonfacialregiongeneration,the\nbackground of referenceimagesismasked.Thegeneratedimageshavesemi-random backgrounds entangledwithfaces in the EG3D space.\nOriginal\n Edit\n Result\n Real Image\n Projection\n Edit\n Result\nFig.5. InteractiveFacialNeRFeditingresults.Userscaninteractivelyselectanarbitraryviewandthenedit3Dfacesbymodifyingtherenderedsketches.\nHand-drawnsketchesarelabeledinred,andthegrayregionsaretheinferredmasksoftheuserinterface.Someeditingexamplesareshowninthisfigure,e.g.,\nhairstyle, glasses, beard, eyes, and expression. Our method edits the local regions while maintaining the global features of the original faces. Real images can\nbeprojectedandeditedtosynthesize free-view results.\nof5𝑒−3andoneiterationcosts0.18sonourdevice.Moredetails\nof the dataset and training settings can be found in supplementary\nmaterials.\n5.1 Results\nOur method supports high-quality facial NeRF generation based on\nsingle-viewsketchesandappearancereferenceimages.Wetreatthe\nfacialNeRFgenerationfromscratchasaspecialeditingsituation\nwhere the predicted tri-plane features are directly projected into\nthelatentspacewithoutmaskfusionandoptimization.Theview-\ning angles of hand-drawn sketches are estimated by [Chen et al .\n2020; Deng et al .2019b]. As shown in Fig. 4, given the hand-drawn\nsketches that represent the facial geometry details, including the\nfacialcomponentshapes,hairstructures,andbeard,ourapproach\ngenerates high-quality 3D geometry models with good faithfulness\nforsketches.Althoughthehand-drawnsketcheshavevariousdraw-\ningstylesthataredifferentfromthoseinthetrainingdataset,our\nmethodisrobustandcanstillgeneratehigh-qualityfacialmodels.5 EVALUATION\nIn this  section,  a series  of qualitative  and  quantitative  experiments  \nare  conducted  to demonstrate  the  superiority  of our  framework.  In \nSec.  5.1,  we  show  the  sketch-based  facial  NeRF  editing  and  genera-\ntion  results  of our  method.  In Sec.  5.2,  the  qualitative  and  quanti-\ntative  comparisons  with  state-of-the-art  methods  are  conducted  to \ndemonstrate  the  better  performance  of our  approach.  In Sec.  5.3,  we  \nconduct  an  ablation  study  to validate  the  effectiveness  of each  mod-\nule  and  network  design  in this  framework.  A user  study  is presented  \nin Sec.  5.4  to further  prove  the  superiority  of our  approach.\nImplementation  Details.  To  train  the  facial  NeRF  manipulation  \nframework,  we  synthesize  a multi-view  dataset  based  on  EG3D  \nwith  110k  training  samples.  For  each  example,  25  rendered  images  \nfrom  different  views  are  generated  while  the  tri-plane  features  are  \nsynthesized  on  the  fly  during  training.  Our  networks  are  trained  \nand  tested  on  an  NVIDIA  RTX  3090  GPU.  During  optimization,  we  \nuse  the  ADAM  [Kingma  and  Ba  2014]  optimizer  with  a learning  rate\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:9\n(a) Image\n (b) Sketch\n (c) Results\nFig.6. Sketch-basedgeneration(1strow)andediting(2ndrow)forfacial\nNeRF design. The 1st row shows a sketch-based generation example based\nonanappearanceimage(a)andahand-drawnsketch(b).Userscanfurther\napplydetailededitingviaasynthesizedsketch(b)whilemaintainingthe\noriginalidentity characteristics.\nSketchesonlyprovidegeometryinformationbutlackcolorinforma-\ntion,so ourfaceregressionnetworkcontrols theappearancewith\nexampleimages.FacialNeRFmodelswithdifferentcolors,materi-\nals,andlightingaresynthesized,andtheviewpointscanbefreely\ncontrolled by users, as shown in Fig. 4. More generation results can\nbefound in thesupplementary materials.\nWith our carefully designed framework and user interface, users\ncaninteractivelyeditfacialdetailsviasketchesfromfreeviewpoints.\nAfter the sketch modification using brush and eraser tools, the user\ninterface automatically infers the edited masks (colored as gray\nin Fig. 5). As illustrated, the masks accurately label locally edited\nregions. High-quality results are generated by our method with\nvarious types of editing operations, including adding/removing\neyeglasses,changinghairstructures,andmodifyingfacialshapesor\nexpressions.Theeditedregionsshowgoodeditingeffects,whilethe\nglobal features are well maintained. As shown in Fig. 7, our system\ngenerates good multi-step editing results for a single example from\nmultiple views. The editing operations performed from different\nviewsarealleffective.Theresultsshownodeteriorationwiththe\naccumulationofeditingoperationsthankstoourtri-planeprojection\nand optimization approaches. More editing results with different\ndrawing styles canbefound in oursupplementary materials.\n5.2 Comparison\nSketch-basedFacialNeRF Generation. Sinceourmethodcangen-\neratehigh-qualityfacialNeRFsbasedonsingle-viewsketches,we\ncompare it with possible existing sketch-based facial NeRF genera-\ntion approaches, with some adaptations. PixelNeRF [Yu et al .2021]\nsynthesizes NeRFs with the input of single-view images, which are\nreplacedwithsingle-viewsketchesinourexperimentstosupport\nourtask.As shownin Fig. 8, this approach cannotcontroltheface\nappearanceandisnotrobustforhand-drawnsketcheswithfuzzy\ndetails. DeepFaceEditing [Chen et al .2021] synthesizes face images\nfor hand-drawn sketches and controls the appearance accurately,\nbut thereare still artifactsaround the neck and hair regions. Since\n(a) Original\n (b) Sketch\n (c) Result\n (d)ResultFront\nFig. 7. Results of multi-step editing (from top to bottom) in different views.\nIn (a), the topmost image is the original face, and the rest are previously\neditedresultsforfurtherediting.Modifiedsketchesandgeneratedresults\nareshownin(b)and(c),respectively.Theeditingmanipulationsareaddedin\ndifferentviews.Ourmethodwellmaintainstheoriginalfeaturesinunedited\nregions (d) and avoids deterioration even though the results are recursively\nused.\nthedata-drivenmanifoldprojectionisutilized,somelesscommon\nappearances,likethebigcurlyhair(3rdrow)andbangs(4throw),\ncannot be synthesized well. We further project the results of Deep-\nFaceEditingintoEG3D’slatentspacetogenerateNeRFresults,as\nshown in the 5th column in Fig. 8. Based on the pretrained gen-\nerator, the projection results are more realistic but still have low\nfaithfulnesswithinputsketches,e.g.,themistakenhairstructures.\npSp [Richardson et al .2021] is a new image translation approach\nbased on 2D StyleGAN [Karras et al .2020]. For fair comparison,\nwereplacetheStyleGANwiththeEG3Dgeneratorandutilizethe\nstyle-mixing to swap the last 7 layers of latent codes to support ap-\npearancecontrol.Althoughthisapproachgeneratesgoodresultson\nsynthesized sketches (see supplementary material), it is not robust\nforhand-drawnsketchesandhaspoorgeometryfaithfulness.The\nstyle-mixingalsocannotcontroltheappearanceaccuratelyin3D\nGAN because of the complicated rendering process. Our method is\nthefirstapproachtosynthesizingfacialNeRFsfromsketchesand\nhasbetter resultsthanpossiblebaselines.\n\n1:10 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\n(a) Sketch\n (b) PixelNeRF\n (c) Appearance\n (d)DFE\n (e)DFE-Projection\n (f) pSp-Ref\n (g) Ours\nFig. 8. Comparisons with state-of-the-art methods for hand-drawn sketches to facial NeRF translation. In each row, (a) is a user-drawn sketch, and (c) is an\nappearance reference image. PixelNeRF (b) cannot control the appearance and generates blurry results. Other existing methods (d) ∼(f) generate results with\nthe input ofreference appearance but have poorfaithfulness withsketches(d,e) or appearance (f), while ourmethod (g) generates the best results. DFE and\nDFE-projarethe abbreviationsof DeepFaceEditing [Chen etal.2021] and itsNeRFprojection version.\nPixelNeRF DFE DFE-projpSp pSp-Ref Ours\nFID↓189.30 77.63 97.94 94.15 80.69 72.63\nKID↓16.25±0.21.98±0.22.99±0.24.52±0.22.56±0.22.06±0.2\nTable 1. Quantitative results compared with sketch-based facial generation\napproaches.WereporttheFIDandKIDmean×100 ±std.×100.Ourresults\nhave lower (i.e., better) values compared with other approaches, except\ncomparablevalueswithDeepFaceEditing(DFE),which,however,isdesigned\nonlyfor2D imagesinstead ofNeRF.\nforfaithfulness,fusethemwiththeoriginaltri-planefeaturesfor\nconsistency, and encode the fused tri-plane features back to the 3D-\naware generativeprior to improvethe quality. We also include the\noptimizationprocesstoensurefaithfulnessandconsistencybetter.\nNoticethatourmethodalsodoesnotrequirelaboriousmanualmask\ndrawing,similar to SketchEdit.\nQuantitative Comparison. To measure the facial NeRF generation\nand editing quality of the compared approaches, we report the\nFréchet Inception Distance (FID) [Heusel et al .2017] and Kernel\nInceptionDistance(KID)[Binkowskietal .2018]inTables1and2.\nFor sketch-based facial generation, we collected 100 hand-drawn\nsketches,whichweresharedbytheauthorsofDeepFaceDrawing\n[Chen et al .2020]and collected withtheir online demo system. AsSketch-based  Facial  NeRF  Editing.  Our  method  supports  detailed  \nsketch-based  editing  of 3D  human  faces  and  generates  high-quality  \nediting  results  in given  views.  Thus,  we  compare  it with  existing  \nsketch-based  face  editing  methods.  However,  due  to the  2D  nature  \nof the  existing  methods,  they  are  not  3D-aware  as our  method  since  \nwe  are  the  first  to edit  3D  human  faces  by  sketch.  As  shown  in Fig.  9, \ngiven  original  facial  images  (a)  and  edited  sketches  (b),  DeepPS  [Yang  \net al. 2020]  dilates  the  sketches  to achieve  higher-quality  results  \nthan  those  without  dilation  but  compromises  the  faithfulness,  e.g.,  \nfailing  to turn  the  straight  hair  into  curly  hair  in the  second  row.  \nBesides,  it has  obvious  artifacts  near  the  boundary  of the  masks  \ndue  to its  inpainting  fusion.  DeepFaceEditing  [Chen  et al. 2021]  \nproduces  reasonably  edited  results,  but  the  image  quality  degrades  \nwith  complex  editing  manipulations.  Its  results  also  exhibit  artifacts  \nsuch  as darkened  areas  on  the  eyes  (1st  row)  and  forehead  (2nd  row)  \nbecause  of the  local  appearance  disturbance  of the  original  images.  \nSketchEdit  [Zeng  et al. 2022]  generates  unrealistic  edited  results  \nfor  glasses  removal  and  curly  hair,  despite  its  efforts  to  estimate  \nthe  masks  for  the  edited  regions.  In contrast,  our  method  produces  \nhigh-quality  and  3D-aware  facial  images  rendered  from  different  \nviews.  Additionally,  our  method  is faithful  to the  edited  sketches  and  \npreserves  the  untouched  regions  well.  The  reason  is that  we  carefully  \npredict  the  edited  tri-plane  features  directly  from  the  edited  sketches\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:11\n(a) Image\n (b) Sketch\n (c) DeepPS\n (d)DeepFaceEditing\n (e)SketchEdit\n (f) Ours\nFig.9. Comparisonswithstate-of-the-artmethodsforsketch-basedfacialediting.Givenoriginalimages(a)andmodifiedsketches(b),DeepPSgenerates\nplausible results (c) in edited regions but has obvious artifacts on the editing region boundaries. DeepFaceEditing generates results (d) with the appearance of\ninput images (a), thus causing color bias in local regions when removing the hair and glasses. SketchEdit is not robust against hand-drawn sketches and\nsynthesizesblurryresults(e).Ourmethodnotonlygeneratesbetterresults(f)intheoriginalviewsthantheotherapproachesbutcanalsorenderrealistic\nfree-view results.\nDeepPS DFE SketchEdit Ours\nFID↓108.8 98.65 112.51 87.68\nKID↓5.94±0.54.23±0.56.44±0.54.22±0.4\nTable2. Quantitativeresultscomparedwithsketch-basedfaceeditingap-\nproaches. We report the FID and KID mean×100 ± std.×100. Our results\nhave the lowest values among all the compared approaches, indicating the\nbestimage quality.\nshown in Table 1, our method outperforms the other approaches\nexcept for the comparable KID compared with DeepFaceEditing\n[Chenetal. 2021],whose resultsareonly2Dimages andhavelow\nsketchfaithfulness,asshowninFig.8.Afterprojection,theartifacts\nare accumulated and have worse value results. For sketch-based\nfacial editing, we collect 50 editing examples based on the user\ninterface(Fig. 3).AsshowninTable2, eventhoughourmethodis\ndesigned explicitly for NeRF editing, it outperforms all the state-\nof-the-artimageeditingmethodsatthemanipulationviewpoints.\nDuringthe quantitativecalculation, thebackgroundismaskedout\nbecauseweonlyfocus on thequality of facialregions.\n5.3 AblationStudy\nWe conduct ablation studies to prove the effectiveness of each com-\nponent in our framework. The key components of the Sketch Tri-\nplanePrediction netandMaskFusion modulearedisabledtoshow\ntheirimpacts.Then,thelosstermsinsketch-basedoptimizationare\nevaluated respectively to prove their effectiveness. We also replace\nthesketchgenerationapproach withother approachesto evaluate\ntheviewconsistency of our3Dsketches.\nIn theSketch Tri-plane Prediction net, the sketches are translated\nintofeaturemapstosupplementcolorinformation.AsshowninFig.\n10, the appearance is unable to control without such an appearance\ntransferprocess,inconsistentwiththeappearancereferenceimages.\n(a) Sketch\n (b) Appear.\n (c) w/o Color\n (d) w/o Volume\n (e) Ours\nFig. 10. Ablation study of the Sketch Tri-plane Prediction net given hand-\ndrawn sketches (a) and reference appearance images (b). Without coloriza-\ntion,theappearancecannotbecontrolled(c).Withoutthefeaturevolume\nand directly predicting tri-planes features based on input sketches, the\nresults have lowfaithfulness (d) with the inputsketches. Our method gen-\nerates the bestresults (e) basedonthe sketches and appearanceimages.\nAdditionally, the stereoscopic information is added by lifting the\n2D feature maps into 3D feature volumes through space projection.\nWithoutsuch alifting processto enhancethe3D information,the\nencodedresultssufferfromlossoffaithfulness,especiallyforhair\nandsmalldetailssuchaseyebrows,sinceitishardtodirectlyencode\nlatentcodesfor3Dmodelsfrom2Dinputs.Incontrast,ourfullmodel\ngeneratesthebestresultsregardingbothgeometryfaithfulnesswith\ntheinputsketchesandappearancefaithfulnesswiththeappearance\nreference images.\nAstothe MaskFusion module,withoutnegativelyaffectingthe\nediting effects, the fusion operation solves identity distortion in the\noriginal view and preserves unedited regions in other views. As\nshown in the first row in Fig. 11, the baseline without the fusion\nstrategy predicts images that exhibit subtle distortions on the back-\nground and facial shape in the original view (b). When we rotate\nit into the front view, the hair is also totally changed (d). Although\n\n1:12 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\nFig. 11. Ablation study of the Mask Fusion module. The original image and drawn sketch are shown in (a). The mouth is closed in this example. As shown in\nthe 1st row, without the Mask Fusion module, the predicted (b) and optimized images (c) have different backgrounds from the original images. When we\nrotate into the front view, the predicted (d) and optimized faces (e) ∼(g) have different haircuts compared with the original faces. As shown in the 2nd row, our\nmethod generates the same background (c) in the original view. The front-view faces well retain the original features with fewer optimization steps compared\nwithbaseline approaches.\n(a) InputImage\n (b) Sketch\n (c) w/o Opt\n (d)w/o Prediction\n (e)w/o𝐿𝑖𝑚𝑔\n (f) w/o𝐿𝑒𝑑𝑖𝑡\n (g) Pix2PixHD\n (h)Ours\nFig. 12. Ablation study of optimization approaches proposed in Sec. 3.3.3. Input images (a) and modified sketches (b) are shown in the first two columns, with\nthegrayregionsindicatingtheinferredmasksbyourmethod.Directpredictionresults(c)withoutoptimizationareacceptablebuthavealittlebiaswith\ndrawn sketches. Optimization started from the original latent (d), and without 𝐿𝑒𝑑𝑖𝑡(f) has limited effects, and removing 𝐿𝑖𝑚𝑔(e) changes unedited regions.\nUtilizingPix2PixHD[Wangetal .2018]tocalculatesketchlosshassimilarresultsastheinitialpredictedresults,whileourapproachfurtherimprovesthe\nfaithfulness tosketches asseen in the hairpatternsin the firstrow and the shapeof glassesin the secondrow.\ninthesecondrow.Weintroduceseverallosstermstoensurefaithful\nandconsistenteditingeffects.Without Limg,thedetails,suchasthe\nbeardinthefirstrow,failtobepreserved.Without Ledittoguide\nthe desired shape, the optimized results do not follow the edited\nsketches.\nAsillustratedinFig.13,withoutthenovelspacesamplelossterm,\nacceptable editing results are still achieved at the original views,\nand the unedited regions are also well preserved. However, from\ntheperspectiveof3Dhumanfacesinsteadof2Dfacialimages,such\n3D models are subject to substantial geometry changes, which can\nbedetected from thefrontal views. Wealsotest thesketchgenera-\ntion approach by replacing it with Pix2PixHD [Wang et al .2018] to\ndirectly predict sketches from the rendered images. However, since\nPix2PixHD is quite heavy and not robust, the predicted sketches\nhave a relatively indirect connection with the underlying 3D hu-\nman faces, making the optimized results have similar effects to the\ninitialpredictedones,asshowninFig.12.Incomparison,ourfullpart  of the  original  features  can  be  restored  with  the  optimization  \napproach,  the  final  results  (c)&(g)  still  have  subtle  differences  from  \nthe  original  facial  NeRF  in unedited  regions,  such  as hair  details  and  \nbackground  patterns.  Our  approach  can  well  preserve  the  original  \nfeatures  with  fewer  steps  compared  with  the  baseline  approaches,  \nproving  the  effectiveness  of the  Mask  Fusion  module.\nAn  optimization  process  is included  to address  challenging  editing  \ncases  and  enhance  the  correspondence  between  the  edited  sketches  \nand  results  in terms  of details.  In Fig.  12,  it is obvious  that  without  \nsuch  an  optimization  process,  the  encoded  results  are  acceptable  but  \ndiffer  from  the  desired  sketches  in small  details,  such  as  the  area  of \nhair  in the  first  row,  and  the  shape  of eyeglasses  in the  second  row.  \nHowever,  if we  remove  the  encoding  module,  i.e.,  by  directly  opti-\nmizing  the  latent  codes  based  on  the  initial  latent  codes  and  edited  \nsketches,  the  optimized  results  have  very  low  consistency  with  the  \ndesired  sketches  despite  long  optimization  steps.  For  example,  the  \nhair  barely  changes  in the  first  row,  and  the  glasses  cannot  be  added\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:13\n(a) Image\n (b) Sketch\n (c) w/o𝐿𝑠𝑝𝑎𝑐𝑒\n (d)Ours\n (e)Front Image\n (f) Front w/o 𝐿𝑠𝑝𝑎𝑐𝑒\n (g) Front Ours\nFig. 13. Ablation study of the space sample loss during optimization. In the 1st row, the double chins of a girl are removed. In the 2nd row, the nose bridge is\nraised,andthehairbangsareadded.Themethodswithandwithout 𝐿𝑠𝑝𝑎𝑐𝑒bothgenerategoodresultsobservedfromtheeditingviews.However,when\nobservedfrom otherviews,theresultswithout 𝐿𝑠𝑝𝑎𝑐𝑒have undesirable changes in uneditedregions, such as thehair patterns in thefirst row andthe lefthair\nin the secondrow.\nPix2PixHD UPDw/o𝐿𝑣Ours\nInconsistency ↓0.108 0.0840.0640.056\nTable3. Consistencyevaluationof3Dsketches.Ourresultshavealower\ninconsistencyscorethanthebaselineapproachesPix2PixHD[Wangetal .\n2018] and UPD [Yi et al .2020], meaning that our sketches have the best\nconsistency across views.\noptimizationsettingmakesupforthechallengingdetails,suchas\nthe height of hair and the shape of eyeglasses, while preserving the\nuneditedregionsin 3Dspace.\nWe add a new sketch generation approach to synthesize 3D\nsketches based on EG3D’s latent codes. As shown in Fig. 3, the\nsynthesized sketches are displayed in the UI system and rotated\nwiththesynthesizedfacialimagessimultaneously.So,sketchconsis-\ntencyduringviewchangesaffectsusers’interactionexperience.We\nreplaceoursketchgenerationapproachwithtwoimage-to-sketch\ntranslation methods, including Pix2PixHD and Unpaired Portrait\nDrawing (UPD for short) [Yi et al .2020]. The regularization term\nwe used in the training stage cannot be applied to these methods\nsince they are not 3D-aware. A short-range consistency score is\nmeasured as in [Huang et al .2021] to measure the inconsistency\nduringviewchanges.Wedonotusealong-rangeconsistencyscore\nsince sketches are view-dependent and naturally vary with large\nviewchanges.Werandomlygenerate30facestocalculatethemetric\nandshowtheresultsinTable3.Itcanbeseenthatoursketcheshave\nthebestviewconsistencycomparedwiththealternativeapproaches.\nUPDgeneratessketcheswiththreedifferentstyles,sowereportthe\nlowest value among them in Table3.\n5.4 UserStudy\nGiventheaboveextensivequalitativeandquantitativecomparisons,\nwefinditbeneficialtoconductaperceptionstudytofullytestifyour\nmethodfromtheperspectiveofhumanviewers.Specifically,weeval-\nuate our method on two tasks: facial generation from hand-drawn2D sketches, and facial editing by modifying the corresponding 2D\nsketches.\nFor the facial generation, we compare our method against the\nsamesetof state-of-the-art methods in thequalitativecomparison\nofsketch-basedfacialNeRFgeneration,i.e.,PixelNeRF,DeepFaceEd-\niting(denotedasłDFEž),DeepFaceEditingfollowedbyNeRFprojec-\ntion (denoted as łDFE-Projectionž), and pSp (denoted as łpSp-Refž).\nWeprepare15casestocoverasmuchdiversity(suchasthedraw-\ningstyleandpersonalattributes,includingage,gender,hairstyle,\netc.) as possible, each of which consists of an input 2D hand-drawn\nsketch,areferenceappearancefacialimageandthefacialimages\ngeneratedbythecomparedmethods.Sincethesemethodsarenotall\n3D-aware, we only display the results rendered from the viewpoint\noftheinputsketches,inrandomorder.However,itisnoteworthy\nthatourmethodfurthersupports facerotationduetothe3D-aware\nNeRF representation. Users are invited to rank the generated facial\nimages in order (the lower, the better) from: the perspective of real-\nism, geometry consistency with the input sketches, and appearance\nconsistencywiththereferenceappearanceimages.Thescoresare\nobtained by averaging the received rankings for each method of\neach case on each criterion. For each invited user, we randomly\nselect 5 cases from all the available cases to save his/her time. Thus,\nwe collect 5 ×3=15 answers from each user. In total, 39 people (28\nmalesand11femalesintheageof18 −40participatedinthisstudy.\nTherefore, 39 ×15=585 answerswerecollected.\nFig. 14 (a) plots the statisticsof the evaluation results. Wefound\nthesignificanteffectsforallthreecriteriathroughone-wayANOVA\ntests: realism ( 𝐹(2,42)=80.33,𝑝<0.001), geometry consistency\n(𝐹(2,42)=14.88,𝑝<0.001), and appearance consistency ( 𝐹(2,42)=\n61.57,𝑝<0.001).Wealsoconductpairedt-teststoconfirmthesupe-\nriorityintermsofgeometryconsistencyofourmethod(mean:2.00)\ntoPixelNeRF(mean:3.23;[ 𝑡=−6.42,𝑝<0.001]), DFE(mean:3.18;\n[𝑡=−6.53,𝑝<0.001]), DFE-Projection (mean: 3.09; [ 𝑡=−6.13,𝑝<\n0.001]), and pSp-Ref (mean: 3.48; [ 𝑡=−7.36,𝑝<0.001]). Besides its\nsuperior performance in geometry consistency, our method is also\nrated as one of the best in terms of realism, and it (mean: 1.87) is\n\n1:14 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\n(a) (b)Realism\nGeometry Consistency\nAppearance ConsistencyRealism\nRetention\nFaithfulnessPixelNeRF DFE DFE-\nProjectionpSp-Ref Ours\nPixelNeRF DFE DFE-\nProjectionpSp-Ref Ours\nPixelNeRF DFE DFE-\nProjectionpSp-Ref OursDeepPS DFE SketchEdit Ours\nDeepPS DFE SketchEdit Ours\nDeepPS DFE SketchEdit Ours\nFig. 14. Box plots of averaged perception rankings (the lower, the bet-\nter). (a) The comparisonof facial generation with five methods: PixelNeRF\n[Yu et al.2021], DeepFaceEditing [Chen et al .2021] (denoted as łDFEž),\nDeepFaceEditing-Projection(denotedasłDFE-Projectionž),pSp[Richard-\nson et al.2021] (denoted as łpSp-Refž), and ours in terms of the realism,\ngeometryconsistency,andappearanceconsistency.(b)Thecomparisonof\nfacialeditingwithfourmethods:DeepPS[Yangetal .2020],DeepFaceEd-\niting [Chen et al .2021], SketchEdit [Zeng et al .2022], and ours in terms\nofrealism,retention,andfaithfulness.Theboxesaredrawnfromthefirst\nquartile to the third quartile, with a middle horizontal line denoting the\nmedian. The whiskers are the minimum and maximum values excluding\nanyoutliers.\nmorepreferredthanPixelNeRF(mean:4.79;[ 𝑡=−17.46,𝑝<0.001]),\nDFE (mean: 3.17; [ 𝑡=−6.21,𝑝<0.001]), and pSp-Ref (mean:\n3.07; [𝑡=−11.77,𝑝<0.001]). In terms of appearance consis-\ntency,it(mean:2.30)alsooutperformsPixelNeRF(mean:4.42;[ 𝑡=\n−13.44,𝑝<0.001]) and pSp-Ref (mean: 3.74; [ 𝑡=−7.05,𝑝<0.001]).\nHowever, since DFE-Projection projects the results back into the\nlatentspace ofour backbone, therealism of DFE-Projection (mean:\n2.08;[𝑡=−1.00,𝑝=0.32])iscomparabletoours,asexpected.Dueto\nthewell-disentangledpropertyofgeometryandappearanceofDFE,\nthe appearance consistency of DFE (mean: 2.28; [ 𝑡=0.11,𝑝=0.90])\nand DFE-Projection (mean: 2.24; [ 𝑡=0.31,𝑝=0.75]) are again\ncomparable to ours.AstothefacialNeRFediting,wealsocompareourmethodagainst\nthesamesetofstate-of-the-artmethodsinthequalitativecompar-\nison of sketch-based facial editing, i.e., DeepPS, DeepFaceEditing\n(denotedasłDFEž),andSketchEdit.Weprepare15casestocover\nvarying personalattributes of the original identities, different edit-\ning regions (such as nose, hair, mouth, etc.), and different editing\nangles (frontal or tilted). Each case consists of an original facial\nimage, a modified sketch where edited regions are emphasized, and\nthe edited facial images generated by each method. Again, we only\ndisplay the results from the viewpoints of the original facial images\nsince not all the compared methods are 3D-aware. For each invited\nuser,wealsorandomlyselect5casesfromalltheavailablecasesand\nasktheusertoranktheeditedfacialimages(presentedinarandom\norder) from the perspectiveof realism, retention of theunchanged\nregions, and faithfulness to the edited sketches. Thus, we collect\n5×3=15answers from eachuser.In total, 39people (28 malesand\n11 females in the age of 18 −40 with normal vision) without any\nspecial experience successfully participated in this study. Therefore\nwecollected 39 ×15=585 answersin total.\nFig. 14 (b) plots the statistics of the evaluation results. We found\nthesignificanteffectsforallthreecriteriathroughone-wayANOVA\ntests: realism ( 𝐹(2,42)=70.49,𝑝<0.001), retention ( 𝐹(2,42)=\n32.50,𝑝<0.001), and faithfulness ( 𝐹(2,42)=37.71,𝑝<0.001). We\nalso conduct paired t-tests to confirm the superiority in all three\ncriteria, i.e., realism, retention, and faithfulness in order, of our\nmethod (mean: 1.22, 1.71, 1.55) over DeepPS (mean: 2.41, 2.35, 2.96;\n[𝑡=−8.32,−4.33,−11.82,𝑝<0.001]), DFE (mean: 3.49, 3.24, 2.64;\n[𝑡=−17.51,−12.69,−7.32,𝑝<0.001]),andSketchEdit(mean:2.86,\n2.68, 2.83;[ 𝑡=−10.02,−5.76,−8.92,𝑝<0.001]).\n6 APPLICATIONS\nIn this section, we propose two novel applications of our system,\nnamely,SemanticFacialNeRFEditingandLocalAppearanceCon-\ntrol.\nEditing Propagation. As to the facial editing by sketches, since\nwecarefullypreserveourgenerativepriorandachievethe editing\nby modifying the latent codes, our system can be further utilized to\nfindeditingdirectionsforsemanticcontrols.Forexample,asshown\nin Fig. 15, we close the mouth in thefirst row and shorten thehair\nin the second row by editing the sketches. We subtract the latent\ncodes for the original facial images from the derived latent codes\nfor the edited facial images. Following this idea of latent vector\narithmetic to GANs [Radford et al .2016], the differences in the\nlatentspaceareviewed aseditingdirectionsfor closingthe mouth\nand shortening the hair, and thus such editing directions can be\nappliedtoothersmilingorlong-haircases.Itisclearthattheediting\ndirectionsinferredbyoursystemcangeneralizetoothercaseswell,\nas seen from (d) ∼(g). Another interesting phenomenon is that by\nmodifying the latent codes using our estimated semantic editing\ndirections, not only are unedited regions well-preserved with small\ndisturbances,suchasthoseonthebackgroundsbutalsothelighting\neffects are changed correspondingly. Such effects are more evident\nin thesecond caseof shorteningthe hair:the rightcheekbecomes\nlitsincetheocclusionisremovedby shorteningthe hair.\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:15\n(a) Image\n (b) Sketch\n (c) Result\n (d)Face1\n (e)Propagation1\n (f) Face2\n (g) Propagation2\nFig.15. ResultsofEditingPropagation.In thefirstthreecolumns(from lefttoright),weshow theinputimages,editedsketches, andeditedresults.Mouth\nclosing and haircuttingare appliedby users.Itcanbeseen thatsimilarediting effectscanbepropagatedtoother persons.\n(a) Image\n (b) Mask\n (c) Result1\n (d)Result2\nFig. 16. Resultsof localappearancecontrol.Given faces renderedin certain\nviews(a),users drawmaskstoindicatelocaleditingregions(b). Asshown\nin (c) and (d), with the reference images on the top-left corner, the local\nappearanceinhairandskinregionsischangedwhilethefeaturesinunedited\nregionsaremaintained.\nLocalAppearanceControl. Thankstothe3Dmaskestimationand\nfusionstrategy,oursystemcanextendtheappearancecontrolby\nreferenceimagesfromglobalspacetolocalregions.Giventheorigi-\nnal3Dface,asketchrepresentingitsfacialgeometryisrenderedby\nthesketchgenerationapproach.Anewtri-planerepresentationthat\nhasthesamegeometryastheoriginalfacebuthasanewappearance\nisgeneratedbythe SketchTri-planePrediction netinSec.3.2.The\noriginalandnewtri-planefeaturesarefusedby3Dmasksestimated\nfromthe2Dmasks 𝑀(drawnbyusers),similartothefunctionof\ntheMask Fusion module. The fused tri-plane feature is expected to\npreservetheoriginalgeometryoftheentirefaceandappearanceon\nuntouchedregionsbutchangetheappearanceofthedrawnregions,\nas shown in Fig. 16. During the optimization, we further utilize a\nhistogram lossto maintain theappearancefaithfulness:\nL=Lℎ𝑖𝑠𝑡(H(𝐼′⊙𝑀),H(𝐼𝑟𝑒𝑓⊙𝑀))+\nLℎ𝑖𝑠𝑡(H(𝐼′⊙/tildewide𝑀),H(𝐼𝑔𝑒𝑜⊙/tildewide𝑀)), (12)\nwhere H denotes the histogram features and Lℎ𝑖𝑠𝑡denotes the\nfeaturedistanceasin[Afifietal .2021].𝐼𝑔𝑒𝑜,𝐼𝑟𝑒𝑓,and𝐼′representthe\noriginalimage,appearancereferenceimage,andgeneratedimage,\nrespectively, and /tildewide𝑀refers to unedited regions. As shown in Fig. 16,\nlocal region appearance like hair and skin is modified effectively,\n(a) Sketch\n (b) Result\n (c) Image\n (d)Sketch\n (e)Result\nFig.17. Failure cases.When the hand-drawnsketches (a) are tooabstract\nand cartoonish, the generated faces(b) arestill of good qualitybut cannot\ncaptureoverlyexaggeratedcharacteristics.Ourapproachalsocannothandle\nuncommon personal accessories suchasthe hatin (e).\nwhile the features in other regions are retained. Since the fusion\nisconductedinthe3Dspace,ourresultscanfurtherberotatedto\nother viewpointswith3Dconsistency.\n7 CONCLUSIONS AND DISCUSSIONS\nIn this paper,wehavepresented the first novel sketch-based facial\nNeRFgenerationandeditingmethod.The SketchTri-planePredic-\ntionnet is designed to supplement the appearance and stereoscopic\ninformationinto2Dsketches,combinedwithapretrainedgenerator\ntosynthesizehigh-qualityfacesNeRFs.Oursystemisrobustagainst\ndiverse drawing styles and allows appearance control. To preserve\nunedited 3D regions during local editing, we further propose the\nMask Fusion module and a latent code optimization from sketch\nstrategy,whichcanbeperformedrepeatedlytosupport3D-aware\nmulti-step manipulations from different viewpoints. Our approach\noutperforms existing sketch-based facial generation and editing ap-\nproaches not only on faithfulness, and visual quality but also on 3D\nview consistency. We also adapted our system for two applications:\nsemantic facialNeRF editingandlocalappearancecontrol.\nThankstothe SketchTri-planePrediction netandthepretrained\ngenerator, our system is robust for hand-drawn sketches. However,\nas shown in Fig. 17, when users draw too abstract or cartoonish\nsketches,generated3Dfacesmightfailtocaptureoverlyexagger-\natedcharacteristics,thoughtheyarestillofgoodquality.Besides,\noursystemisdesignedtogenerate3Dfacesfromscratchwiththe\ninput of front-view sketches because it is hard for novice users\n\n1:16 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu\nto draw free-view facial sketches, and the camera parameter pre-\ndictionisverychallenging.Designingaspecificmethodtodetect\ncamera poses from hand-drawn sketches can partly solve this prob-\nlem. Moreover, as shown in Fig. 17, our system cannot deal with\nuncommon personal accessories such as hats since these examples\nare rare in our training dataset. Designing a specific approach to\nsolvingthedataimbalanceoraugmentingthetrainingdatacould\nalleviatethisproblem.Infuture,wewouldimplementtheSketch-\nFaceNeRFinJittor[Huetal .2020],whichisafullyjust-in-time(JIT)\ncomplieddeep framework.\nEthicalDiscussion. Ourworkoriginatesfromandbenefitsposi-\ntivereal-worldapplications,suchasdigitalcharacterdesign,virtual\nmeetings, and entertainment. However, the facial image genera-\ntionandeditingworkshavelongsufferedfrompotentialharmful\nabuses.Topreventmisuse,manyworks[Rossleretal .2019;Tolosana\net al.2020; Zhao et al .2021] in the fake detection community could\ndiscriminate between the synthesized and real faces. Besides, the\ngeneratedfree-viewfacialimagesofourmethodcanalsobeutilized\nasa trainingdatasetto benefitthefakedetectionresearch.\nACKNOWLEDGMENTS\nThis work was supported by grants from the Beijing Municipal\nNaturalScienceFoundationforDistinguishedYoungScholars(No.\nJQ21013), the National Natural Science Foundation of China (No.\n62061136007andNo.62102403),ChinaPostdoctoralScienceFoun-\ndation(No.2022M713205),theResearchGrantsCounciloftheHong\nKongSpecialAdministrativeRegion,China(No.CityU11212119),\nChow Sang Sang Group Research Fund (No. 9229119), and the Cen-\ntre for Applied Computing and Interactive Media (ACIM) of School\nof CreativeMedia, CityU.\nREFERENCES\nMahmoudAfifi,MarcusABrubaker,andMichaelSBrown.2021. Histogan:Controlling\ncolors of gan-generated and real images via color histograms. In Conference on\nComputer Vision and Pattern Recognition .7941ś7950.\nAutodesk,INC.2019. Maya. https:/autodesk.com/maya\nPierreBénard,AaronHertzmann,etal .2019. Linedrawingsfrom3Dmodels:Atutorial.\nFoundationsandTrends®in Computer Graphics and Vision 11, 1-2 (2019),1ś159.\nAlexanderW.Bergman,PetrKellnhofer,WangYifan,EricR.Chan,DavidB.Lindell,\nand Gordon Wetzstein. 2022. Generative Neural Articulated Radiance Fields. In\nAdvances in NeuralInformationProcessing Systems .\nMikolaj Binkowski, Danica J. Sutherland, Michael Arbel, and Arthur Gretton. 2018.\nDemystifyingMMDGANs.In InternationalConferenceonLearningRepresentations .\nJohn Canny. 1986. Acomputationalapproach toedge detection. IEEE Transactions on\npattern analysisand machineintelligence 6(1986),679ś698.\nEric R Chan, Connor Z Lin, Matthew A Chan, Koki Nagano, Boxiao Pan, Shalini\nDeMello,OrazioGallo,LeonidasJGuibas,JonathanTremblay,SamehKhamis,etal .\n2022. Efficient geometry-aware 3D generative adversarial networks. In Conference\non ComputerVision andPattern Recognition .16123ś16133.\nEric R Chan, Marco Monteiro, Petr Kellnhofer, Jiajun Wu, and Gordon Wetzstein.\n2021. pi-gan: Periodic implicit generative adversarial networks for 3d-aware image\nsynthesis. In Conference on Computer Vision and Pattern Recognition .5799ś5809.\nShu-Yu Chen, Feng-LinLiu,Yu-Kun Lai,PaulL. Rosin,ChunpengLi, HongboFu, and\nLinGao.2021. DeepFaceEditing:deepfacegenerationandeditingwithdisentangled\ngeometry and appearance control. ACM Trans. Graph. 40, 4, Article 90 (2021),\n15pages.\nShu-Yu Chen, Wanchao Su, Lin Gao, Shihong Xia, and Hongbo Fu. 2020. DeepFace-\nDrawing:Deepgeneration offaceimagesfromsketches. ACMTrans.Graph. 39,4,\nArticle 72(2020),16pages.\nYuedong Chen, Qianyi Wu, Chuanxia Zheng, Tat-Jen Cham, and Jianfei Cai. 2022.\nSem2NeRF:ConvertingSingle-ViewSemanticMaskstoNeuralRadianceFields.In\nEuropean Conference Computer Vision ,Vol. 13674.730ś748.Pei-Ze Chiang, Meng-Shiun Tsai, Hung-Yu Tseng, Wei-Sheng Lai, and Wei-Chen Chiu.\n2022. Stylizing3D sceneviaimplicitrepresentationandHyperNetwork.In Winter\nConference on Applications of Computer Vision .1475ś1484.\nPinaki Nath Chowdhury, Tuanfeng Wang, Duygu Ceylan, Yi-Zhe Song, and Yulia\nGryaditskaya.2022. GarmentIdeation:IterativeView-AwareSketch-BasedGarment\nModeling. In InternationalConference on 3D Vision .22ś31.\nDoug DeCarlo, Adam Finkelstein, Szymon Rusinkiewicz, and Anthony Santella. 2003.\nSuggestive Contoursfor ConveyingShape. ACMTrans.Graph. 22, 3, 848ś855.\nJiankangDeng,JiaGuo,NiannanXue,andStefanosZafeiriou.2019a. Arcface:Additive\nangular margin loss for deep face recognition. In Conference on Computer Vision\nand Pattern Recognition .4690ś4699.\nYuDeng,JiaolongYang,JianfengXiang,andXinTong.2022. Gram:Generativeradiance\nmanifolds for 3d-aware image generation. In Conference on Computer Vision and\nPattern Recognition .10673ś10683.\nYu Deng, Jiaolong Yang, Sicheng Xu, Dong Chen, Yunde Jia, and Xin Tong. 2019b.\nAccurate 3DFaceReconstructionWith Weakly-Supervised Learning:FromSingle\nImage to Image Set. In Conference on Computer Vision and Pattern Recognition\nWorkshops .285ś295.\nDong Du, Xiaoguang Han, Hongbo Fu, Feiyang Wu, Yizhou Yu, Shuguang Cui, and\nLigangLiu.2020. SAniHead:Sketchinganimal-like3Dcharacterheadsusingaview-\nsurface collaborative mesh generative network. IEEE Transactions on Visualization\nand ComputerGraphics 28, 6(2020),2415ś2429.\nLeonAGatys,AlexanderSEcker,andMatthiasBethge.2016. Imagestyletransferusing\nconvolutionalneuralnetworks.In WinterConferenceonApplicationsofComputer\nVision.2414ś2423.\nJiataoGu,LingjieLiu,PengWang,andChristianTheobalt.2022. StyleNeRF:AStyle-\nbased3DAwareGeneratorforHigh-resolutionImageSynthesis.In International\nConference on Learning Representations .\nXiaoguang Han,ChangGao,and YizhouYu. 2017. DeepSketch2Face:a deep learning\nbased sketchingsystem for3D faceandcaricaturemodeling. ACM Trans. Graph. 36,\n4, Article126 (2017),12pages.\nXiaoguang Han, Kangcheng Hou, Dong Du, Yuda Qiu, Shuguang Cui, Kun Zhou,\nand Yizhou Yu. 2018. Caricatureshop: Personalized and photorealistic caricature\nsketching. IEEETransactionsonVisualizationandComputerGraphics 26,7(2018),\n2349ś2361.\nMartin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp\nHochreiter. 2017. GANs trained by a two time-scale update rule converge to a local\nNashequilibrium.In AdvancesinNeuralInformationProcessingSystems .6626ś6637.\nShi-MinHu,DunLiang,Guo-YeYang,Guo-WeiYang,andWen-YangZhou.2020. Jittor:\na novel deep learning framework with meta-operators and unified graph execution.\nScience ChinaInformation Sciences 63, 222103(2020),1ś21.\nShi-Min Hu, Fang-Lue Zhang, Miao Wang, Ralph R. Martin, and Jue Wang. 2013.\nPatchNet:APatch-BasedImageRepresentationforInteractiveLibrary-DrivenImage\nEditing.ACMTrans.Graph. 32, 6, Article 196 (2013),12pages.\nHsin-PingHuang,Hung-YuTseng,SaurabhSaini,ManeeshSingh,andMing-Hsuan\nYang.2021. LearningtoStylizeNovelViews.In InternationalConferenceonComputer\nVision.13849ś13858.\nXunHuangandSergeBelongie.2017. Arbitrarystyletransferinreal-timewithadaptive\ninstancenormalization.In ConferenceonComputerVisionandPatternRecognition .\n1501ś1510.\nXinHuang,DongLiang,HongruiCai,JuyongZhang,andJinyuanJia.2022b.CariPainter:\nSketch Guided Interactive Caricature Generation. In ACM International Conference\non Multimedia .1232ś1240.\nYi-Hua Huang, Yue He, Yu-Jie Yuan, Yu-Kun Lai, and Lin Gao. 2022a. StylizedNeRF:\nconsistent 3D scene stylization as stylized NeRF via 2D-3D mutual learning. In\nConference on Computer Vision and Pattern Recognition .18342ś18352.\nKaiwenJiang,Shu-YuChen,Feng-LinLiu,HongboFu,andLinGao.2022. NeRFFaceEd-\niting: Disentangled Face Editing in Neural Radiance Fields. In SIGGRAPH Asia 2022\nConference Papers .Association for ComputingMachinery,Article 31, 9pages.\nKyungmin Jo, Gyumin Shim, Sanghun Jung, Soyoung Yang, and Jaegul Choo. 2021. Cg-\nnerf: Conditional generative neural radiance fields. arXiv preprint arXiv:2112.03517\n(2021).\nYoungjoo Jo and Jongyoul Park. 2019. Sc-fegan: Face editing generative adversarial\nnetwork with user’s sketch and color. In Conference on Computer Vision and Pattern\nRecognition .1745ś1753.\nTilkeJudd,FrédoDurand,andEdwardAdelson.2007.ApparentRidgesforLineDrawing.\nInACMTrans.Graph. ,Vol. 26. 19.\nTeroKarras,Samuli Laine,andTimoAila.2019. Astyle-basedgeneratorarchitecture\nfor generative adversarial networks.In Conference on Computer Vision and Pattern\nRecognition .4401ś4410.\nTeroKarras,SamuliLaine,MiikaAittala,JanneHellsten,JaakkoLehtinen,andTimo\nAila.2020. Analyzingandimprovingtheimagequalityofstylegan.In Conference\non ComputerVision andPattern Recognition .8110ś8119.\nDiederik PKingma andJimmy Ba. 2014. Adam: A methodfor stochasticoptimization.\narXivpreprintarXiv:1412.6980 (2014).\n\nSketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:17\nTianye Li, Timo Bolkart, Michael.J. Black, HaoLi, and JavierRomero. 2017. Learning\na model of facial shape and expression from 4D scans. ACM Trans. Graph. 36, 6,\nArticle 194 (2017),17pages.\nYuhangLi, Xuejin Chen,Feng Wu, andZheng-JunZha. 2019. Linestofacephoto: Face\nphoto generation from lines with conditional self-attention generative adversarial\nnetworks. In ACMInternationalConference on Multimedia .2323ś2331.\nYuhang Li, Xuejin Chen, Binxin Yang, Zihan Chen, Zhihua Cheng, and Zheng-Jun\nZha.2020. Deepfacepencil:Creatingfaceimagesfromfreehandsketches.In ACM\nInternationalConference on Multimedia .991ś999.\nJingwang Ling, Zhibo Wang, Ming Lu, Quan Wang, Chen Qian, and Feng Xu. 2022.\nStructure-Aware Editable Morphable Model for 3D Facial Detail Animation and\nManipulation.In EuropeanConference on Computer Vision .249ś267.\nDifan Liu, Mohamed Nabail, Aaron Hertzmann, and Evangelos Kalogerakis. 2020.\nNeuralcontours:Learningtodrawlinesfrom3dshapes.In WinterConferenceon\nApplications of Computer Vision .5428ś5436.\nFeng-Lin Liu, Shu-Yu Chen, Yu-Kun Lai, Chunpeng Li, Yue-Ren Jiang, Hongbo Fu, and\nLin Gao. 2022. DeepFaceVideoEditing: sketch-based deep editing of face videos.\nACMTrans.Graph. 41, 4, Article 167 (2022),16pages.\nHongyuLiu,ZiyuWan,WeiHuang,YibingSong,XintongHan,JingLiao,BinJiang,and\nWei Liu. 2021. DeFLOCNet: Deep Image Editing via Flexible Low-Level Controls. In\nConference on Computer Vision and Pattern Recognition .10765ś10774.\nWilliamE.LorensenandHarveyE.Cline.1987. MarchingCubes:AHighResolution\n3DSurfaceConstructionAlgorithm. SIGGRAPHComput.Graph. 21,4(aug1987),\n163ś169.\nZhongjin Luo, Jie Zhou, Heming Zhu, Dong Du, Xiaoguang Han, and Hongbo Fu.\n2021. SimpModeling: Sketching Implicit Field to Guide Mesh Modeling for 3D\nAnimalmorphic Head Design. In ACM Symposium on User Interface Software and\nTechnology .854ś863.\nBen Mildenhall, Pratul P Srinivasan, Matthew Tancik, Jonathan T Barron, Ravi Ra-\nmamoorthi,andRenNg.2021. Nerf:Representingscenesasneuralradiancefields\nfor view synthesis. Commun. ACM 65, 1(2021),99ś106.\nThu Nguyen-Phuoc, Feng Liu, and Lei Xiao. 2022. SNeRF: Stylized Neural Implicit\nRepresentationsfor3DScenes. ACMTrans.Graph. 41,4,Article142(2022),11pages.\nMichael Niemeyer and Andreas Geiger. 2021. Giraffe: Representing scenes as composi-\ntionalgenerativeneuralfeaturefields.In ConferenceonComputerVisionandPattern\nRecognition .11453ś11464.\nNVIDIA. 2023. NVIDIAOmniverse . https://www.nvidia.com/en-us/omniverse/\nYutaka Ohtake, Alexander Belyaev, and Hans-Peter Seidel. 2004. Ridge-valley lines on\nmeshes via implicit surfacefitting. Vol.23. 609ś612.\nRoy Or-El, Xuan Luo, Mengyi Shan, Eli Shechtman, Jeong Joon Park, and Ira\nKemelmacher-Shlizerman. 2022. Stylesdf: High-resolution 3d-consistent image\nandgeometrygeneration.In ConferenceonComputerVisionandPatternRecognition .\n13503ś13513.\nEthanPerez,FlorianStrub,HarmDeVries,VincentDumoulin,andAaronCourville.\n2018. Film: Visual reasoning with a general conditioning layer. In Proceedings of the\nAAAIConference on ArtificialIntelligence ,Vol. 32.\nPixologic.2023. ZBrush. http://pixologic.com/features/about-zbrush.php\nTizianoPortenier,QiyangHu,AttilaSzabó,SiavashArjomandBigdeli,PaoloFavaro,\nand Matthias Zwicker. 2018. Faceshop: deep sketch-based face image editing. ACM\nTrans.Graph. 37, 4, Article 99(2018),13pages.\nAlecRadford,LukeMetz,andSoumithChintala.2016. UnsupervisedRepresentation\nLearningwithDeepConvolutionalGenerativeAdversarialNetworks.In Interna-\ntional Conference on Learning Representations .\nEladRichardson, YuvalAlaluf,OrPatashnik,YotamNitzan, YanivAzar,StavShapiro,\nandDanielCohen-Or.2021.Encodinginstyle:astyleganencoderforimage-to-image\ntranslation. In Conference on Computer Vision and Pattern Recognition .2287ś2296.\nAndreas Rossler, Davide Cozzolino, Luisa Verdoliva, Christian Riess, Justus Thies, and\nMatthias Nießner. 2019. Faceforensics++: Learning to detect manipulated facial\nimages. In InternationalConference on Computer Vision .1ś11.\nKatjaSchwarz,YiyiLiao,MichaelNiemeyer,andAndreasGeiger.2020. Graf:Generative\nradiance fields for 3d-aware image synthesis. Advances in Neural Information\nProcessing Systems 33(2020),20154ś20166.\nTianchang Shen, Jun Gao, Kangxue Yin, Ming-Yu Liu, and Sanja Fidler. 2021. Deep\nmarchingtetrahedra:ahybridrepresentationforhigh-resolution3dshapesynthesis.\nAdvances in NeuralInformationProcessing Systems (2021),6087ś6101.\nVincent Sitzmann, Julien Martel, Alexander Bergman, David Lindell, and Gordon\nWetzstein. 2020. Implicit neural representations with periodic activation functions.\nAdvances in NeuralInformationProcessing Systems 33(2020),7462ś7473.\nWanchaoSu,HuiYe,Shu-YuChen,LinGao,andHongboFu.2022. DrawingInStyles:\nPortrait Image Generation and Editing with Spatially Conditioned StyleGAN. IEEE\nTransactions on Visualization andComputer Graphics (2022).\nJingxiang Sun, Xuan Wang, Yichun Shi, Lizhen Wang, Jue Wang, and Yebin Liu. 2022a.\nIDE-3D:InteractiveDisentangledEditingforHigh-Resolution3D-AwarePortrait\nSynthesis. ACMTrans.Graph. 41, 6, Article 270 (2022),10pages.\nJingxiangSun,XuanWang,LizhenWang,XiaoyuLi,YongZhang,HongwenZhang,and\nYebinLiu.2022b. Next3D:GenerativeNeuralTextureRasterizationfor3D-AwareHead Avatars. arXivpreprintarXiv:2211.11208 (2022).\nJingxiangSun,XuanWang,YongZhang,XiaoyuLi,QiZhang,YebinLiu,andJueWang.\n2022c. Fenerf: Face editing in neural radiance fields. In Conference on Computer\nVision andPattern Recognition .7672ś7682.\nJunshuTang,BoZhang,BinxinYang,TingZhang,DongChen,LizhuangMa,andFang\nWen.2022. ExplicitlyControllable3D-AwarePortraitGeneration. arXivpreprint\narXiv:2209.05434 (2022).\nRubenTolosana,RubenVera-Rodriguez,JulianFierrez,AythamiMorales,andJavier\nOrtega-Garcia. 2020. Deepfakes and beyond: A survey of face manipulation and\nfake detection. Information Fusion 64(2020),131ś148.\nYaelVinker,EhsanPajouheshgar,JessicaY.Bo,RomanChristianBachmann,AmitHaim\nBermano, Daniel Cohen-Or, Amir Zamir, and Ariel Shamir. 2022. CLIPasso:\nSemantically-AwareObjectSketching. ACMTrans.Graph. 41,4,Article86(2022),\n11pages.\nTing-Chun Wang, Ming-Yu Liu, Jun-Yan Zhu, Andrew Tao, Jan Kautz, and Bryan\nCatanzaro. 2018. High-resolution image synthesis and semantic manipulation with\nconditionalgans.In ConferenceonComputerVisionandPatternRecognition .8798ś\n8807.\nYue Wu, Yu Deng, Jiaolong Yang, Fangyun Wei, Chen Qifeng, and Xin Tong. 2022.\nAniFaceGAN: Animatable 3D-Aware Face Image Generationfor Video Avatars.In\nAdvances in NeuralInformationProcessing Systems .\nSainingXieandZhuowenTu.2015. Holistically-NestedEdgeDetection.In International\nConference on Computer Vision .1395ś1403.\nLi Yang, Jing Wu, Jing Huo, Yu-Kun Lai, and Yang Gao. 2021b. Learning 3D face\nreconstruction froma singlesketch. GraphicalModels 115 (2021),101102.\nShuai Yang, Zhangyang Wang, Jiaying Liu, and Zongming Guo. 2020. Deep plastic\nsurgery: Robust and controllable image editing with human-drawn sketches. In\nEuropean Conference on ComputerVision .601ś617.\nShuaiYang,ZhangyangWang,JiayingLiu,andZongmingGuo.2021a. Controllable\nsketch-to-imagetranslationforrobustfacesynthesis. IEEETransactionsonImage\nProcessing 30(2021),8797ś8810.\nRan Yi, Yong-Jin Liu, Yu-Kun Lai, and PaulL. Rosin. 2020. Unpaired Portrait Drawing\nGeneration via Asymmetric Cycle Mapping.In Conference on Computer Vision and\nPattern Recognition .8214ś8222.\nRanYi,Yong-JinLiu,Yu-KunLai,andPaulLRosin.2019. APDrawingGAN:Generating\nartistic portrait drawings from face photos with hierarchical gans. In Conference on\nComputer Vision and Pattern Recognition .10743ś10752.\nAlexYu,VickieYe,MatthewTancik,andAngjooKanazawa.2021. pixelnerf:Neural\nradiancefieldsfromoneorfewimages.In ConferenceonComputerVisionandPattern\nRecognition .4578ś4587.\nJiahui Yu, Zhe Lin, Jimei Yang, Xiaohui Shen, Xin Lu, and Thomas S Huang. 2019.\nFree-form image inpainting with gated convolution. In Conference on Computer\nVision andPattern Recognition .4471ś4480.\nYuZeng,ZheLin,andVishalMPatel.2022. Sketchedit:Mask-freelocalimagemanipula-\ntionwithpartialsketches.In ConferenceonComputerVisionandPatternRecognition .\n5951ś5961.\nKai Zhang, Nicholas I. Kolkin, Sai Bi, Fujun Luan, Zexiang Xu, Eli Shechtman, and\nNoahSnavely.2022. ARF:ArtisticRadianceFields.In EuropeanConferenceComputer\nVision,Vol. 13691.717ś733.\nRichardZhang,PhillipIsola,AlexeiAEfros,EliShechtman,andOliverWang.2018. The\nunreasonable effectiveness of deep features as a perceptual metric. In Conference on\nComputer Vision and Pattern Recognition .586ś595.\nHanqing Zhao, Wenbo Zhou, Dongdong Chen, Tianyi Wei, Weiming Zhang, and Neng-\nhaiYu.2021. Multi-attentionaldeepfakedetection.In ConferenceonComputerVision\nand Pattern Recognition .2185ś2194.", "files_in_pdf": []}