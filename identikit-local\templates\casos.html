{% extends "base.html" %}

{% block title %}Casos - Sistema Identikit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-folder"></i>
        Gestión de Casos
    </h1>
    <a href="{{ url_for('nuevo_caso') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i>
        Nuevo Caso
    </a>
</div>

<!-- Filtros de búsqueda -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Buscar por título</label>
                <input type="text" class="form-control" id="searchTitle" placeholder="Título del caso...">
            </div>
            <div class="col-md-2">
                <label class="form-label">Estado</label>
                <select class="form-select" id="filterStatus">
                    <option value="">Todos</option>
                    <option value="activo">Activo</option>
                    <option value="resuelto">Resuelto</option>
                    <option value="archivado">Archivado</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Ubicación</label>
                <input type="text" class="form-control" id="searchLocation" placeholder="Ubicación...">
            </div>
            <div class="col-md-2">
                <label class="form-label">Fecha desde</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                    <i class="bi bi-search"></i>
                    Filtrar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Lista de casos -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-list-ul"></i>
            Lista de Casos ({{ casos|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if casos %}
        <div class="table-responsive">
            <table class="table table-hover" id="casosTable">
                <thead>
                    <tr>
                        <th>Caso</th>
                        <th>Ubicación</th>
                        <th>Fecha Incidente</th>
                        <th>Estado</th>
                        <th>Investigador</th>
                        <th>Identikits</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    {% for caso in casos %}
                    <tr data-caso-id="{{ caso.id }}">
                        <td>
                            <div>
                                <strong>{{ caso.titulo }}</strong>
                                <br>
                                <small class="text-muted">{{ caso.descripcion[:80] }}{% if caso.descripcion|length > 80 %}...{% endif %}</small>
                            </div>
                        </td>
                        <td>{{ caso.ubicacion }}</td>
                        <td>{{ caso.fecha_incidente }}</td>
                        <td>
                            {% if caso.estado == 'activo' %}
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-exclamation-circle"></i> Activo
                                </span>
                            {% elif caso.estado == 'resuelto' %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle"></i> Resuelto
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="bi bi-archive"></i> Archivado
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {% if caso.investigador_id %}
                                    Asignado
                                {% else %}
                                    Sin asignar
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <span class="badge bg-info">0</span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('caso_detalle', caso_id=caso.id) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="Ver detalles">
                                    <i class="bi bi-eye"></i>
                                </a>
                                
                                {% if user.rol in ['admin', 'investigador'] %}
                                <a href="{{ url_for('generar_ia', caso_id=caso.id) }}" 
                                   class="btn btn-sm btn-outline-success" 
                                   title="Generar identikit">
                                    <i class="bi bi-robot"></i>
                                </a>
                                {% endif %}
                                
                                {% if user.rol == 'admin' %}
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete('{{ caso.id }}')" 
                                        title="Eliminar caso">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Paginación -->
        <nav aria-label="Paginación de casos" class="mt-3">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">Anterior</a>
                </li>
                <li class="page-item active">
                    <a class="page-link" href="#">1</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">Siguiente</a>
                </li>
            </ul>
        </nav>
        
        {% else %}
        <!-- Estado vacío -->
        <div class="text-center py-5">
            <i class="bi bi-folder-x" style="font-size: 4rem; color: #cbd5e0;"></i>
            <h4 class="text-muted mt-3">No hay casos registrados</h4>
            <p class="text-muted">Comienza creando tu primer caso criminal</p>
            <a href="{{ url_for('nuevo_caso') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                Crear Primer Caso
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal de confirmación de eliminación -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-danger"></i>
                    Confirmar Eliminación
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>¿Estás seguro de que deseas eliminar este caso?</p>
                <p class="text-danger">
                    <strong>Esta acción no se puede deshacer.</strong>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash"></i>
                    Eliminar Caso
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let casoToDelete = null;

function applyFilters() {
    const title = document.getElementById('searchTitle').value.toLowerCase();
    const status = document.getElementById('filterStatus').value;
    const location = document.getElementById('searchLocation').value.toLowerCase();
    const dateFrom = document.getElementById('dateFrom').value;
    
    const rows = document.querySelectorAll('#casosTable tbody tr');
    
    rows.forEach(row => {
        const titleText = row.cells[0].textContent.toLowerCase();
        const statusText = row.cells[3].textContent.toLowerCase();
        const locationText = row.cells[1].textContent.toLowerCase();
        const dateText = row.cells[2].textContent;
        
        let show = true;
        
        if (title && !titleText.includes(title)) show = false;
        if (status && !statusText.includes(status)) show = false;
        if (location && !locationText.includes(location)) show = false;
        if (dateFrom && dateText < dateFrom) show = false;
        
        row.style.display = show ? '' : 'none';
    });
}

function confirmDelete(casoId) {
    casoToDelete = casoId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (casoToDelete) {
        // Aquí harías la llamada AJAX para eliminar el caso
        fetch(`/api/casos/${casoToDelete}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error al eliminar el caso');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar el caso');
        });
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
    }
});

// Filtrado en tiempo real
document.getElementById('searchTitle').addEventListener('input', applyFilters);
document.getElementById('filterStatus').addEventListener('change', applyFilters);
document.getElementById('searchLocation').addEventListener('input', applyFilters);
document.getElementById('dateFrom').addEventListener('change', applyFilters);
</script>
{% endblock %}
