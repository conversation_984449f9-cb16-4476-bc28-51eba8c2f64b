{"extracted_information": "El contenido web es la página del repositorio oficial de GitHub para ControlNet. Contiene información sobre la estructura de ControlNet, modelos pre-entrenados, instalación, y ejemplos de uso, incluyendo secciones específicas para bocetos (scribbles) que son relevantes para la consulta 'sketch-to-image'.", "specifications": {"hardware_requirements": {"vram": "Se menciona un 'modo Low VRAM' para usuarios con GPU de 8GB o menos, o para tamaños de lote más grandes. Esto sugiere que 8GB de VRAM puede ser el requisito mínimo para este modo, y más para el modo estándar o tareas más exigentes."}}, "pricing": {}, "features": ["ControlNet permite controlar modelos de difusión añadiendo condiciones extra (como bordes, profundidad, pose, segmentación, bocetos).", "Reutiliza el codificador de modelos de difusión (como Stable Diffusion) como \"backbone\".", "Usa \"zero convolution\" para evitar distorsiones iniciales y permitir fine-tuning sin destruir el modelo original.", "Admite entrenamiento en dispositivos de pequeña escala o personales.", "Compatible con Stable Diffusion V1.5 (y V2.1 para entrenamiento).", "Modo Guess Mode / Non-Prompt Mode: El codificador de ControlNet intenta \"adivinar\" el contenido del mapa de control incluso sin prompts.", "Combinación de múltiples ControlNets para control multi-condición (experimental en plugins de terceros).", "Posibilidad de usar ControlNet con cualquier modelo de la comunidad (SD1.X) mediante transferencia o plugins de terceros.", "Scripts para anotar datos propios y entrenar con datos propios."], "statistics": {"github_stars": "32.5k", "github_forks": "2.9k"}, "temporal_info": {"latest_commit_date": "Sep 9, 2023", "controlnet_1_1_release_announcement": "2023/0/14 (lanzamiento de versión nightly, se fusionará con este repo más tarde)", "non_prompt_mode_release": "2023/02/20"}, "geographical_data": {}, "references": ["ControlNet 1.1 Nightly: https://github.com/lllyasviel/ControlNet-v1-1-nightly", "Modelos y detectores: https://huggingface.co/lllyasviel/ControlNet", "Artículo de investigación: Adding Conditional Control to Text-to-Image Diffusion Models (arXiv:2302.05543)", "Plugin WebUI Mikubill's A1111: https://github.com/Mikubill/sd-webui-controlnet", "Hugging Face Space por Hysts: https://huggingface.co/spaces/hysts/ControlNet", "ControlNet-for-Diffusers por haofanwang: https://github.com/haofanwang/ControlNet-for-Diffusers", "Citación: @misc{zhang2023adding, title={Adding Conditional Control to Text-to-Image Diffusion Models}, author={<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON>}, booktitle={IEEE International Conference on Computer Vision (ICCV)} year={2023},}"]}