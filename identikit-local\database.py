"""
Módulo de base de datos para Sistema Identikit Local
Configuración y gestión de SQLite
"""

import sqlite3
import uuid
from datetime import datetime
import os

DATABASE_PATH = 'data/identikit.db'

def get_db_connection():
    """Obtener conexión a la base de datos"""
    # Crear directorio si no existe
    os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
    
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # Para acceder a columnas por nombre
    return conn

def init_database():
    """Inicializar base de datos con esquema y datos de prueba"""
    conn = get_db_connection()
    
    # Crear tabla de usuarios
    conn.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            nombre TEXT NOT NULL,
            rango TEXT NOT NULL,
            rol TEXT NOT NULL CHECK (rol IN ('admin', 'investigador', 'analista')),
            fecha_creacion TEXT NOT NULL,
            activo INTEGER DEFAULT 1
        )
    ''')
    
    # Crear tabla de casos
    conn.execute('''
        CREATE TABLE IF NOT EXISTS casos (
            id TEXT PRIMARY KEY,
            titulo TEXT NOT NULL,
            descripcion TEXT,
            ubicacion TEXT,
            fecha_incidente TEXT,
            estado TEXT DEFAULT 'activo' CHECK (estado IN ('activo', 'resuelto', 'archivado')),
            investigador_id TEXT,
            fecha_creacion TEXT NOT NULL,
            fecha_actualizacion TEXT,
            FOREIGN KEY (investigador_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Crear tabla de identikits
    conn.execute('''
        CREATE TABLE IF NOT EXISTS identikits (
            id TEXT PRIMARY KEY,
            caso_id TEXT NOT NULL,
            imagen_original TEXT NOT NULL,
            imagen_procesada TEXT,
            parametros TEXT,
            calidad_score REAL DEFAULT 0.0,
            fecha_creacion TEXT NOT NULL,
            creado_por TEXT,
            FOREIGN KEY (caso_id) REFERENCES casos (id),
            FOREIGN KEY (creado_por) REFERENCES usuarios (id)
        )
    ''')
    
    # Crear tabla de evidencias
    conn.execute('''
        CREATE TABLE IF NOT EXISTS evidencias (
            id TEXT PRIMARY KEY,
            caso_id TEXT NOT NULL,
            tipo TEXT NOT NULL,
            descripcion TEXT,
            archivo_path TEXT,
            fecha_creacion TEXT NOT NULL,
            creado_por TEXT,
            FOREIGN KEY (caso_id) REFERENCES casos (id),
            FOREIGN KEY (creado_por) REFERENCES usuarios (id)
        )
    ''')
    
    # Insertar usuarios por defecto si no existen
    usuarios_default = [
        {
            'id': str(uuid.uuid4()),
            'username': 'admin',
            'password': 'admin123',
            'nombre': 'Administrador del Sistema',
            'rango': 'Comisario',
            'rol': 'admin'
        },
        {
            'id': str(uuid.uuid4()),
            'username': 'investigador',
            'password': 'inv123',
            'nombre': 'Detective Principal',
            'rango': 'Inspector',
            'rol': 'investigador'
        },
        {
            'id': str(uuid.uuid4()),
            'username': 'analista',
            'password': 'ana123',
            'nombre': 'Analista Forense',
            'rango': 'Técnico Especialista',
            'rol': 'analista'
        }
    ]
    
    for usuario in usuarios_default:
        # Verificar si el usuario ya existe
        existing = conn.execute(
            'SELECT id FROM usuarios WHERE username = ?', 
            (usuario['username'],)
        ).fetchone()
        
        if not existing:
            conn.execute('''
                INSERT INTO usuarios (id, username, password, nombre, rango, rol, fecha_creacion)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                usuario['id'], usuario['username'], usuario['password'],
                usuario['nombre'], usuario['rango'], usuario['rol'],
                datetime.now().isoformat()
            ))
    
    # Insertar casos de ejemplo si no existen
    casos_ejemplo = [
        {
            'id': str(uuid.uuid4()),
            'titulo': 'Robo en Joyería Central',
            'descripcion': 'Robo a mano armada en joyería del centro de la ciudad. Testigo describe al sospechoso.',
            'ubicacion': 'Av. Principal 123, Centro',
            'fecha_incidente': '2025-01-10',
            'estado': 'activo'
        },
        {
            'id': str(uuid.uuid4()),
            'titulo': 'Asalto en Estacionamiento',
            'descripcion': 'Asalto a víctima en estacionamiento de centro comercial. Se cuenta con descripción física.',
            'ubicacion': 'Centro Comercial Plaza Norte',
            'fecha_incidente': '2025-01-08',
            'estado': 'activo'
        },
        {
            'id': str(uuid.uuid4()),
            'titulo': 'Hurto en Residencia',
            'descripcion': 'Hurto en casa habitación. Vecino observó al sospechoso.',
            'ubicacion': 'Colonia Las Flores, Calle 5',
            'fecha_incidente': '2025-01-05',
            'estado': 'resuelto'
        }
    ]
    
    # Obtener ID del investigador por defecto
    investigador = conn.execute(
        'SELECT id FROM usuarios WHERE username = "investigador"'
    ).fetchone()
    investigador_id = investigador['id'] if investigador else None
    
    for caso in casos_ejemplo:
        # Verificar si el caso ya existe
        existing = conn.execute(
            'SELECT id FROM casos WHERE titulo = ?', 
            (caso['titulo'],)
        ).fetchone()
        
        if not existing:
            conn.execute('''
                INSERT INTO casos (id, titulo, descripcion, ubicacion, fecha_incidente, 
                                 estado, investigador_id, fecha_creacion)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                caso['id'], caso['titulo'], caso['descripcion'],
                caso['ubicacion'], caso['fecha_incidente'], caso['estado'],
                investigador_id, datetime.now().isoformat()
            ))
    
    conn.commit()
    conn.close()
    print("✅ Base de datos inicializada correctamente")

def get_user_by_credentials(username, password):
    """Obtener usuario por credenciales"""
    conn = get_db_connection()
    user = conn.execute(
        'SELECT * FROM usuarios WHERE username = ? AND password = ? AND activo = 1',
        (username, password)
    ).fetchone()
    conn.close()
    return dict(user) if user else None

def get_casos_stats():
    """Obtener estadísticas de casos"""
    conn = get_db_connection()
    stats = {
        'total': conn.execute('SELECT COUNT(*) FROM casos').fetchone()[0],
        'activos': conn.execute('SELECT COUNT(*) FROM casos WHERE estado = "activo"').fetchone()[0],
        'resueltos': conn.execute('SELECT COUNT(*) FROM casos WHERE estado = "resuelto"').fetchone()[0],
        'archivados': conn.execute('SELECT COUNT(*) FROM casos WHERE estado = "archivado"').fetchone()[0]
    }
    conn.close()
    return stats

def get_identikits_stats():
    """Obtener estadísticas de identikits"""
    conn = get_db_connection()
    stats = {
        'total': conn.execute('SELECT COUNT(*) FROM identikits').fetchone()[0],
        'este_mes': conn.execute(
            'SELECT COUNT(*) FROM identikits WHERE fecha_creacion >= date("now", "start of month")'
        ).fetchone()[0]
    }
    conn.close()
    return stats

if __name__ == '__main__':
    # Ejecutar inicialización si se ejecuta directamente
    init_database()
