{"extracted_information": "El contenido web describe un proyecto de traducción de imagen de boceto a rostro utilizando un enfoque Multi-GANs. El modelo utiliza una serie de modelos GAN: Contextual GANs, GFPGANs y DeOldify. El proyecto fue alojado en un servidor Anvil. La página proporciona pasos para implementar el modelo, incluyendo entrenamiento, guardado del modelo, uso de un cuaderno de Colab, edición de archivos y conexión con Anvil. Se mencionan las capas de procesamiento, aunque las imágenes visuales no se cargaron. Se proporcionan métricas de evaluación para el método.", "specifications": {}, "pricing": {}, "features": ["Traducción de imagen de boceto a rostro", "Uso de modelos GAN: Contextual GANs, GFPGANs, DeOldify"], "statistics": {"Structural Similarity Index Measure (SSIM)": 0.7804028372700662, "L2-Normalization Score": 93.7520664666667}, "temporal_info": {}, "geographical_data": {}, "references": ["Enlace al código de entrenamiento de Sketch to Face (externo): https://github.com/Malikanhar/Face-Sketch-to-Image-Generation-using-GAN/blob/master/Predict%20Image.ipynb", "Archivos Python actualizados para compatibilidad con Python 3.10 (en este repositorio)", "Cuaderno de Colab del proyecto: /0sparsh2/Sketch-to-Face-using-Multi-GANs/blob/main/Sketch_To_Face_Using_Multi_GANs.ipynb", "Tutorial de muestra de Anvil: https://anvil.works/learn/tutorials/deepnote-to-web-app", "Documentación oficial de Anvil: https://anvil.works/docs/overview"]}