{% extends "base.html" %}

{% block title %}Dashboard - Sistema Identikit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-speedometer2"></i>
        Dashboard Criminalístico
    </h1>
    <div class="text-muted">
        <i class="bi bi-calendar"></i>
        {{ moment().format('DD/MM/YYYY HH:mm') if moment else '' }}
    </div>
</div>

<!-- Tarjetas de estadísticas -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.total_casos }}</div>
                <div class="stats-label">Total de Casos</div>
                <i class="bi bi-folder-fill" style="font-size: 2rem; opacity: 0.3; position: absolute; top: 10px; right: 15px;"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.casos_activos }}</div>
                <div class="stats-label">Casos Activos</div>
                <i class="bi bi-exclamation-circle-fill" style="font-size: 2rem; opacity: 0.3; position: absolute; top: 10px; right: 15px;"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.identikits_generados }}</div>
                <div class="stats-label">Identikits Generados</div>
                <i class="bi bi-robot" style="font-size: 2rem; opacity: 0.3; position: absolute; top: 10px; right: 15px;"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stats-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.casos_resueltos }}</div>
                <div class="stats-label">Casos Resueltos</div>
                <i class="bi bi-check-circle-fill" style="font-size: 2rem; opacity: 0.3; position: absolute; top: 10px; right: 15px;"></i>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Panel de casos recientes -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    Casos Recientes
                </h5>
            </div>
            <div class="card-body">
                {% if casos_recientes %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Caso</th>
                                <th>Ubicación</th>
                                <th>Estado</th>
                                <th>Fecha</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for caso in casos_recientes %}
                            <tr>
                                <td>
                                    <strong>{{ caso.titulo }}</strong><br>
                                    <small class="text-muted">{{ caso.descripcion[:50] }}...</small>
                                </td>
                                <td>{{ caso.ubicacion }}</td>
                                <td>
                                    {% if caso.estado == 'activo' %}
                                        <span class="badge bg-warning">Activo</span>
                                    {% elif caso.estado == 'resuelto' %}
                                        <span class="badge bg-success">Resuelto</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Archivado</span>
                                    {% endif %}
                                </td>
                                <td>{{ caso.fecha_incidente }}</td>
                                <td>
                                    <a href="{{ url_for('caso_detalle', caso_id=caso.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    {% if user.rol in ['admin', 'investigador'] %}
                                    <a href="{{ url_for('generar_ia', caso_id=caso.id) }}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-robot"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-folder-x" style="font-size: 3rem; color: #cbd5e0;"></i>
                    <p class="text-muted mt-2">No hay casos registrados</p>
                    <a href="{{ url_for('nuevo_caso') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Crear Primer Caso
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Panel de acciones rápidas -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-charge"></i>
                    Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('nuevo_caso') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Nuevo Caso
                    </a>
                    
                    {% if user.rol in ['admin', 'investigador'] %}
                    <button class="btn btn-success" onclick="showUploadModal()">
                        <i class="bi bi-robot"></i>
                        Generar Identikit
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('casos') }}" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                        Buscar Casos
                    </a>
                    
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-file-earmark-text"></i>
                        Generar Reporte
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Panel de información del usuario -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i>
                    Información del Usuario
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="bi bi-person-circle" style="font-size: 4rem; color: #3182ce;"></i>
                </div>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Nombre:</strong></td>
                        <td>{{ user.nombre }}</td>
                    </tr>
                    <tr>
                        <td><strong>Rango:</strong></td>
                        <td>{{ user.rango }}</td>
                    </tr>
                    <tr>
                        <td><strong>Rol:</strong></td>
                        <td>
                            {% if user.rol == 'admin' %}
                                <span class="badge bg-danger">Administrador</span>
                            {% elif user.rol == 'investigador' %}
                                <span class="badge bg-primary">Investigador</span>
                            {% else %}
                                <span class="badge bg-info">Analista</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal para carga rápida de identikit -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-robot"></i>
                    Generar Identikit Rápido
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Selecciona un caso existente para generar un identikit:</p>
                <select class="form-select" id="casoSelect">
                    <option value="">Seleccionar caso...</option>
                    {% for caso in casos_recientes %}
                    <option value="{{ caso.id }}">{{ caso.titulo }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="goToGenerate()">Continuar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

function goToGenerate() {
    const casoId = document.getElementById('casoSelect').value;
    if (casoId) {
        window.location.href = `/generar-ia/${casoId}`;
    } else {
        alert('Por favor selecciona un caso');
    }
}

// Actualizar estadísticas cada 30 segundos
setInterval(function() {
    // Aquí podrías hacer una llamada AJAX para actualizar las estadísticas
    console.log('Actualizando estadísticas...');
}, 30000);
</script>
{% endblock %}
