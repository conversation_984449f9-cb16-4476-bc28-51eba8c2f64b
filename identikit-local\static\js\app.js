/**
 * Sistema Identikit Fotorrealista - JavaScript Principal
 * Funcionalidades comunes y utilidades
 */

// Configuración global
const APP_CONFIG = {
    name: 'Sistema Identikit Fotorrealista',
    version: '1.0.0',
    apiUrl: window.location.origin,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedImageTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/gif']
};

// Utilidades globales
const Utils = {
    /**
     * Formatear fecha
     */
    formatDate: function(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('es-ES');
    },

    /**
     * Formatear tamaño de archivo
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Validar archivo de imagen
     */
    validateImageFile: function(file) {
        if (!file) {
            return { valid: false, error: 'No se seleccionó archivo' };
        }

        if (!APP_CONFIG.allowedImageTypes.includes(file.type)) {
            return { valid: false, error: 'Tipo de archivo no permitido. Use JPG, PNG, BMP o GIF' };
        }

        if (file.size > APP_CONFIG.maxFileSize) {
            return { valid: false, error: `Archivo demasiado grande. Máximo ${Utils.formatFileSize(APP_CONFIG.maxFileSize)}` };
        }

        return { valid: true };
    },

    /**
     * Mostrar notificación toast
     */
    showToast: function(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast show align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Auto-remove después de 5 segundos
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    },

    /**
     * Crear contenedor de toasts
     */
    createToastContainer: function() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    },

    /**
     * Copiar texto al portapapeles
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('Texto copiado al portapapeles', 'success');
            }).catch(() => {
                this.showToast('Error al copiar texto', 'danger');
            });
        } else {
            // Fallback para navegadores antiguos
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showToast('Texto copiado al portapapeles', 'success');
            } catch (err) {
                this.showToast('Error al copiar texto', 'danger');
            }
            document.body.removeChild(textArea);
        }
    },

    /**
     * Hacer petición AJAX
     */
    ajax: function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const config = { ...defaultOptions, ...options };

        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('Error en petición AJAX:', error);
                this.showToast('Error en la comunicación con el servidor', 'danger');
                throw error;
            });
    }
};

// Funcionalidades de la aplicación
const App = {
    /**
     * Inicializar aplicación
     */
    init: function() {
        console.log(`${APP_CONFIG.name} v${APP_CONFIG.version} iniciado`);
        
        // Configurar eventos globales
        this.setupGlobalEvents();
        
        // Configurar tooltips de Bootstrap
        this.initTooltips();
        
        // Configurar sidebar responsive
        this.setupSidebar();
        
        // Configurar auto-save para formularios
        this.setupAutoSave();
    },

    /**
     * Configurar eventos globales
     */
    setupGlobalEvents: function() {
        // Prevenir envío de formularios vacíos
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.tagName === 'FORM') {
                const requiredFields = form.querySelectorAll('[required]');
                let hasErrors = false;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        hasErrors = true;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    Utils.showToast('Por favor completa todos los campos requeridos', 'warning');
                }
            }
        });

        // Limpiar validación al escribir
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('is-invalid')) {
                e.target.classList.remove('is-invalid');
            }
        });
    },

    /**
     * Inicializar tooltips
     */
    initTooltips: function() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    },

    /**
     * Configurar sidebar responsive
     */
    setupSidebar: function() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });

            // Cerrar sidebar al hacer clic fuera en móvil
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });
        }
    },

    /**
     * Configurar auto-save para formularios
     */
    setupAutoSave: function() {
        const forms = document.querySelectorAll('form[data-autosave]');
        
        forms.forEach(form => {
            const formId = form.id || 'form_' + Date.now();
            let saveTimeout;

            form.addEventListener('input', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.saveFormData(formId, form);
                }, 2000);
            });

            // Cargar datos guardados
            this.loadFormData(formId, form);
        });
    },

    /**
     * Guardar datos del formulario
     */
    saveFormData: function(formId, form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        try {
            localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
            Utils.showToast('Borrador guardado automáticamente', 'info');
        } catch (error) {
            console.error('Error guardando borrador:', error);
        }
    },

    /**
     * Cargar datos del formulario
     */
    loadFormData: function(formId, form) {
        try {
            const savedData = localStorage.getItem(`autosave_${formId}`);
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = data[key];
                    }
                });
                Utils.showToast('Borrador cargado', 'info');
            }
        } catch (error) {
            console.error('Error cargando borrador:', error);
        }
    },

    /**
     * Limpiar datos guardados del formulario
     */
    clearFormData: function(formId) {
        localStorage.removeItem(`autosave_${formId}`);
    }
};

// Funciones específicas para identikits
const IdentikitManager = {
    /**
     * Procesar imagen
     */
    processImage: function(file, parameters, onProgress, onComplete) {
        const validation = Utils.validateImageFile(file);
        if (!validation.valid) {
            Utils.showToast(validation.error, 'danger');
            return;
        }

        const formData = new FormData();
        formData.append('image', file);
        Object.keys(parameters).forEach(key => {
            formData.append(key, parameters[key]);
        });

        // Simular progreso
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90;
            onProgress(progress);
        }, 500);

        // Hacer petición
        fetch('/api/process-image', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            clearInterval(progressInterval);
            onProgress(100);
            setTimeout(() => onComplete(data), 500);
        })
        .catch(error => {
            clearInterval(progressInterval);
            console.error('Error:', error);
            Utils.showToast('Error procesando imagen', 'danger');
        });
    }
};

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    App.init();
});

// Exportar para uso global
window.Utils = Utils;
window.App = App;
window.IdentikitManager = IdentikitManager;
