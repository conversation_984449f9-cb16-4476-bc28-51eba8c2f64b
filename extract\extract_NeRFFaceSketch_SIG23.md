ORCA – Online Research @ Cardiff
This is a n  Op e n  Acces s doc u m e n t  dow nloa d e d  fro m  ORC A, Ca r diff U niver sity's
institution al r e pository:htt ps://orc a.c a r diff.ac.uk /id/ep rint/15 9 4 6 8/
This is t h e  a u t ho r’s version of a  wo rk t h a t  w as  s u b mitt e d  to / a c c e p t e d  for
p u blication.
Cit ation for final p u blish e d  ve rsion:
<PERSON>, <PERSON>, <PERSON>, <PERSON> g-<PERSON>, <PERSON> e n, S h u-Yu, <PERSON><PERSON> g, <PERSON><PERSON> e n, <PERSON>, Ch u n p e n g, <PERSON>, <PERSON><PERSON><PERSON> a n d
F u, H o n g bo 2 0 2 3. Sk etc hFac e N eRF: Sk e tc h-b a s e d  facia l g e n e r a tion a n d  e diting in
n e u r al r a dia nc e  fields. ACM Tr a ns a ctions on Gr a p hic s 4 2  (4) , 1 5 9. 1 0.11 4 5/35 9 2 1 0 0  
P u blish e rs  p a g e: h t t p s://doi.org/10.11 4 5/35 9 2 1 0 0  
Ple a s e  not e: 
Ch a n g e s  m a d e  a s  a  r e s ult of p u blishing p roc e s s e s  s u c h  a s  copy-e diting, for m a t ting
a n d  p a g e  n u m b e r s  m ay not  b e  r eflect e d  in t his versi on. For t h e  d efinitive version of
t his p u blication, ple a s e  r efe r  to t h e  p u blish e d  sou rc e. You a r e  a dvis e d  to cons ult t h e
p u blish e r’s version if you wis h to cite t his p a p er.
This ve rsion is b eing  m a d e  av ailable in a ccor d a nc e  with p u blish e r  policies. S e e  
h tt p://orc a.cf.ac.uk/policies.ht ml for u s a g e  polici es. Copyright  a n d  m o r al right s  for
p u blications  m a d e  av ailable in ORCA a r e  r e t ain e d  by  t h e  copyright  hold e r s.

SketchFaceNeRF:Sketch-based Facial Generation and Editing in Neural
RadianceFields
LIN GAO∗,Instituteof ComputingTechnology,CAS and University of ChineseAcademyof Sciences,China
FENG-LINLIU, Instituteof ComputingTechnology,CAS and University of ChineseAcademyof Sciences,China
SHU-YU CHEN, Instituteof ComputingTechnology,ChineseAcademyofSciences,China
KAIWEN JIANG, Instituteof ComputingTechnology,CAS and BeijingJiaotong University, China
CHUNPENGLI, Instituteof ComputingTechnology,ChineseAcademyofSciences,China
YU-KUN LAI, Schoolof ComputerScience and Informatics, CardiffUniversity, UK
HONGBOFU, Schoolof CreativeMedia, CityUniversity ofHongKong,China
Sketch-based Generation
 Sketch-based Editing
Fig. 1. Our SketchFaceNeRF system supports both generation and editing of high-quality facial NeRFs from 2D sketches. As shown in the left half part,
given a hand-drawn sketch (top-left corner), photo-realistic rendering results with different appearances are synthesized from scratch. The detailed geometry
model and free-view rendering results are shown at the bottom. On the right half part, we show sketch-based editing of facial NeRFs and the corresponding
geometry,whereoriginalfacesandgeometryareshowninpurpleboxes,andtheresultsoftwoconsecutiveeditingstepsareshowningreenandorangeboxes,
respectively. Duringediting,localregions aremodifiedaccording to theeditedsketcheshighlightedin red,while the geometry andappearancefeaturesin
uneditedregionsarewell preserved.
Realistic  3D  facial  generation  based  on  Neural  Radiance  Fields  (NeRFs)  from
2D  sketches  benefits  various  applications.  Despite  the  high  realism  of free-
view  rendering  results  of NeRFs,  it is tedious  and  difficult  for  artists  to
∗Corresponding  author  is Lin  Gao  (<EMAIL>).
Authors’  addresses:  Lin  Gao,  Feng-Lin  Liu,  Shu-Yu  Chen,  Kaiwen  Jiang  and  Chunpeng  
Li are  with  the  Beijing  Key  Laboratory  of Mobile  Computing  and  Pervasive  Device,  
Institute  of Computing  Technology,  Chinese  Academy  of Sciences.  Lin  Gao  and  Feng-
Lin  Liu  are  also  with  University  of Chinese  Academy  of Sciences.  Kaiwen  Jiang  is also  
with  Beijing  Jiaotong  University.  Yu-Kun  Lai  is with  the  School  of Computer  Science  and  
Informatics,  Cardiff  University.  Hongbo  Fu  is with  the  School  of Creative  Media,  City  
University  of Hong  Kong.  Authors’  e-mails:  <EMAIL>,  <EMAIL>,  
<EMAIL>,  <EMAIL>,  <EMAIL>,  <EMAIL>,  
<EMAIL>,duetoitsconcise-
nessandexpressiveness,sketchinghasbeenwidelyusedfor2Dfacialimage
generationandediting.ApplyingsketchingtoNeRFsischallengingdueto
theinherentuncertaintyfor3Dgenerationwith2Dconstraints,asignificant
gap in content richness when generating faces from sparse sketches, and
potential inconsistencies for sequential multi-view editing given only 2D
sketch inputs. To address these challenges, we present SketchFaceNeRF,
a novel sketch-based 3D facial NeRF generation and editing method, to
producefree-viewphoto-realisticimages.Tosolvethechallengeofsketch
sparsity, we introduce a Sketch Tri-plane Prediction net to first inject the
appearance intosketches,thusgeneratingfeatures givenreference images
toallowcolorandtexturecontrol.Suchfeaturesarethenliftedintocompact
3D tri-planes to supplement the absent 3D information, which is important
for improving robustness and faithfulness. However, during editing, con-
sistency for unseen or unedited 3D regions is difficult to maintain due to
limited spatial hints in sketches. We thus adopt a Mask Fusion module to
transformfree-view2Dmasks(inferredfromsketcheditingoperations)into
the tri-plane space as 3D masks, which guide the fusion of the original and
sketch-basedgeneratedfacestosynthesizeeditedfaces.Wefurtherdesignan
optimization approach with a novel space loss to improve identity retention

1:2 • Lin Gao,Feng-LinLiu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li,Yu-Kun Lai,andHongboFu
for 3D face modeling [Han et al .2017, 2018; Yang et al .2021b].
However, these solutions arespecifically designed for mesh-based
models,whichlackhigh-qualitytexturetorenderrealisticimages.
NeRF naturally synthesizes realistic faces, but applying sketch to
NeRFischallenging.First,duetothedomaingapbetweensparse,
monochromaticsketchesandreal2Dfacialimages,2Dsketch-based
facialmodelingisalreadychallenging,nottomentiontheinference
of 3D information from single-view sketch inputs. Furthermore,
users may perform local editing operations from different views.
Supporting multi-step local manipulations from different views
andpreservingunedited3Dregionsvia2Dsketchesisnoteasyto
achieve.
In order to generate and edit facial NeRFs from 2D sketches, a
possible approach is to first leverage 2D sketch-based image gen-
eration methods [Chen et al .2020; Li et al .2019, 2020; Yang et al .
2021a] or editing methods [Chen et al .2021; Jo and Park 2019; Yang
et al.2020; Zeng et al .2022] to generate photo-realistic facial im-
ages, and then project the generated images into latent space of
3D GAN such as EG3D [Chan et al .2022]. However, as shown in
Figs. 8 and 9, these approaches are not robust enough against hand-
drawn sketches, so the 2D intermediate faces may have artifacts,
which would be inherited by final projection results. Inspired by
pSp [Richardson et al .2021], another possible solution is to directly
project2Dinputsketchesinto3DlatentspacebyaCNNencoder.
However,this approach tendsto overfitsynthesized stylesketches
because of the domain gap, as we will later show in Fig. 8. To solve
the above problems, we translate 2D sketches into 3D tri-plane
features[Chanetal .2022],whichsupplementsketcheswithcolor
and stereoscopic information (i.e., volumetric distribution of 3D
faces) to reduce the domain gap. The tri-plane feature prediction
strategynotonlyimprovestherobustnessforhand-drawnsketches
andaddsappearance control butalsosupports multi-view detailed
manipulation because of the representation consistency with the
existingtri-plane-basedEG3D generator.
We present SketchFaceNeRF, a novel sketch-based facial NeRF
generationandeditingmethod.Itsynthesizeshigh-quality3Dfacial
NeRFs from scratch with single-view hand-drawn sketches (see Fig.
1).Asdiscussedbefore,insteadofdirectlyprojecting2Dsketches
into 3D latent space, we propose a Sketch Tri-plane Prediction net to
translate 2D sketches into a compact 3D tri-plane representation.
Specifically,withanappearancereferenceimage,wefirsttransform
sketchesinto2Dfeaturemaps,whicharethenliftedinto3Dfeature
volumesinthe3DEuclideanvolumerenderingspace.Inspiredby
[Yuetal.2021],thefeatureofeach3Dpositioniscomputedfrom
the2D featuremapsbyperspectiveprojectiontransformationand
bilinear interpolation. The tri-planefeatures are then generated by
volume reshaping and convolutions. It is noteworthy that such a
module is less sensitive to the style of input sketches due to the
involvedtransformationandprojectionprocesses(Fig.8).Thetri-
planefeaturesareconcatenatedandencodedintothelatentspace
of EG3D to synthesize realisticfacialNeRFs.
GivensynthesizedfacialNeRFs( e.g.,EG3Dsamples,sketch-based
generations,orreal-imageinversions),the3Drepresentationallows
userstoeditfacialdetailsindifferentviews.Tosolvethechallengeof
preservingunedited3Dregionsduringlocaleditingvia2Dsketches,
we first estimate 2D masks, which indicate edited regions based onand  editing  faithfulness.  Our  pipeline  enables  users  to flexibly  manipulate  
faces  from  different  viewpoints  in 3D  space,  easily  designing  desirable  facial  
models.  Extensive  experiments  validate  that  our  approach  is superior  to the  
state-of-the-art  2D  sketch-based  image  generation  and  editing  approaches  
in realism  and  faithfulness.
CCS  Concepts:  · Human-centered  computing  →  Graphical  user  inter-
faces; · Computer  systems  organization  →  Neural  networks;  · Com-
puting  methodologies  →  Rendering ; Volumetric  models.
Additional  Key  Words  and  Phrases:  Sketch-based  Interaction,  Neural  Radi-
ance  Fields,  Face  Modeling,  Face  Editing
1 INTRODUCTION
Highly  realistic  and  stereoscopic  face  modeling  is a popular  topic  in 
computer  graphics  and  has  a wide  range  of applications,  including  
digital  character  design,  avatar-based  virtual  meetings,  etc. Neverthe-
less,  creating  high-quality  facial  models  in terms  of both  geometry  
and  appearance  from  scratch  requires  laborious  authoring  with  
professional  software  (e.g.,  MAYA  [Autodesk,  INC.  2019],  ZBrush  
[Pixologic  2023]  and  NVIDIA  Omniverse  [NVIDIA  2023]).  More  
importantly,  it is very  difficult  for  existing  mesh-based  approaches  
to render  photo-realistic  facial  images  without  professional  skills  
or  high  costs,  and  realism  is probably  the  most  critical  aspect  of 
practical  applications.  Thus,  how  to conduct  facial  modeling  in an  
easy-to-use  yet  realistic  way  is a worth-studying  research  problem.  
Thanks  to the  development  of deep  learning  approaches,  Neural  
Radiance  Fields  (NeRFs)  [Mildenhall  et al. 2021],  a powerful  implicit  
3D  representation,  can  easily  reconstruct  face  models  from  multi-
view  images  and  render  photo-realistic  free-view  results.  However,  
directly  using  a vanilla  NeRF  to perform  face  manipulation  is very  
challenging  since  information  describing  only  a specific  object  or  
scene  is encoded  by  a NeRF  network.  Although  various  3D  GAN  
(Generative  Adversarial  Network)  approaches  [Chan  et al. 2022;  Gu  
et al. 2022;  Niemeyer  and  Geiger  2021;  Schwarz  et al. 2020]  have  
been  proposed  to generate  facial  NeRFs  by  random  sampling  in-
stead  of reconstructing  real  scenes,  such  methods  still  lack  detailed  
control  and  interpretable  manipulations  over  synthesized  faces.
Several  methods  [Bergman  et al. 2022;  Sun  et al. 2022b;  Tang  et al. 
2022;  Wu  et al. 2022]  have  attempted  to address  these  issues  by  
using  sliders  to interactively  edit  predefined  attributes  based  on  
3DMM  (3D  Morphable  Models)  such  as FLAME  [Li  et al. 2017],  but  
they  provide  limited  manipulation  freedoms.  On  the  other  hand,  it 
is natural  for  humans  to describe  images  with  a long-lasting  tool,  
namely  pens,  which  can  also  be  utilized  for  efficient  and  realistic  
manipulation  in the  context  of 3D  facial  GAN.  Previous  methods  
[Jiang  et al. 2022;  Sun  et al. 2022a,c]  resort  to semantic  masks  as  
an  editing  interface,  which,  however,  does  not  offer  fine  control  of 
facial  details  like  hairstyles,  beards,  etc.  Another  promising  pen-
based  editing  interface  is sketching.  2D  sketching  has  been  widely  
used  to condition  facial  image  generation  [Chen  et al. 2020;  Li et al. 
2019;  Su  et al. 2022]  and  editing  [Chen  et al. 2021;  Jo and  Park  2019;  
Zeng  et al. 2022].  Sketch-based  interfaces  have  also  been  explored

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:3
the user-performed sketch creation and erasing operations. With
the rendered depth maps of NeRF models, a Mask Fusion module
further lifts the estimated 2D masks into 3D masks, which are used
to pick features from the original tri-plane features in the unedited
regions and predicted tri-plane features from the edited sketches in
the edited regions. The fused tri-plane features are encoded back
into thelatent spaceofEG3Dto predict an initial editedface. We
include an optimization process with a novel space loss term to
further ensure faithfulness and consistency for challenging cases
(seeexamplesinFig.12).Notethatourpipelinecanbeperformed
repeatedly on a single face, supporting multi-step 3D-aware human
face editingfromdifferent views via hand-drawn sketches.
Themaincontributionsofthisworkcanbesummarizedasfol-
lows:
•Wepropose the first novel sketch-based 3Dfacial NeRF gen-
eration and editing method, which enables a user-friendly
interfaceforauthoring3D-awarefacesandproducesphoto-
realisticresults.
•We develop a novel network for translating 2D sketches to
3DfacialNeRFs.Single-viewsketchesareaugmentedincolor
andvolumetricspacetoimprovetherobustnessagainsthand-
drawn sketchesandallowappearancecontrol.
•We introduce a mask fusion module and a sketch-based opti-
mization approach to achieve detailed editing while preserv-
ingtheoriginal facialfeatures in uneditedregions.
2 RELATED WORK
Our work is related to existing works, including facial NeRF gener-
ationandediting,neuralsketch-basedfacegeneration,andsketch
rendering of 3Dshapes.
2.1 Facial NeRF Generationand Editing
GenerativeNeRFs. Existing2Dimagegenerationmethods[Karras
et al.2019,2020] randomly samplefromaGaussian distribution to
generatehigh-qualityfacialimages.Utilizingonly2Dimagedatasets,
manyworksfurtherapplythisideato3DgenerationwithNeRFs
[Mildenhalletal .2021].Forexample,GRAF[Schwarzetal .2020]
first conditions a coordinate-based MLP (Multi-layer Perception)
representation in NeRF on the additional shape and appearance
codesandutilizesamulti-scalepatch-baseddiscriminatorinsteadof
areconstructionlosstotrainthemodels.Pi-GAN[Chanetal .2021]
further uses a SIREN-based network [Sitzmann et al .2020] with
FiLM [Perez et al .2018] conditioning to improvethe image quality.
However,duringGANtraining,completeimagesarerenderedin-
steadofindividualrays,sotheresolutionofresultsislimiteddueto
memory restriction. To address this issue, GIRAFFE [Niemeyer and
Geiger2021]generateslow-resolutionfeaturemapsbasedonvol-
umerendering,followedbya2DCNN-basednetworktoachievefast
inference and super-resolution. This approach has been extensively
used in subsequent works, but the adopted 2D network seriously
affectstheviewconsistency.Toaddressthis,StyleNeRF[Guetal .
2022] proposes a specific upsampler combined with a NeRF path
regularizationlosstoreducethe3D-inconsistencyartifacts.Many
works propose novel representations of feature fields to improve
the quality and efficiency further. For example, StyleSDF [Or-Elet al.2022] builds an architecture with an SDF (Signed Distance
Field)-based 3Dvolume renderer toachieveview-consistentfacial
results with more detailed 3D shapes. GRAM [Deng et al .2022] rep-
resents radiance fields as a set of implicit surfaces, replacing dense
MonteCarlosamplingwithafewintersectionpointstorenderhigh-
resolution images directly. EG3D [Chan et al .2022] concurrently
introducesalightweighttri-plane3Drepresentation,combinedwith
a super-resolution network and dual discrimination, to ensure view
consistency and image quality. Instead of random sampling, we
translate a2D sketch into atri-plane representation to supportde-
tailedcontrolinfacialNeRFgeneration.Tomaintain3Dconsistency
during editing, local swapping and fusion operations are further
proposedin thetri-planespace.
Facial NeRF Editing. Besides multi-view image reconstruction or
random generation, many works generate radiance fields based on
single-view inputs, including RGB images [Yu et al .2021], and even
semanticmasks[Chenetal .2022]orsketches[Joetal .2021].Instead
ofconditionalgeneration,semanticmaskshavebeenfurtherutilized
toachievestructureandappearancedisentanglement,supporting
detailedradiancefieldediting.FENeRF[Sunetal .2022c]usestwo
decoupled latent codes to generate spatially-aligned semantics and
texture volumes, which share the same geometry but with different
discriminatorsforsupervision.Basedonthetri-planerepresenta-
tion, IDE-3D [Sun et al .2022a] and NeRFFaceEditing [Jiang et al .
2022]generategeometryplanesforsemanticvolumerenderingand
geometrycontrolaswellasappearanceplanesfortexturecontrol.
Unlike contiguous 3D semantic masks, a sketch is view-dependent,
especially at the surface contour, making it unsuitable for 3D ge-
ometry and appearance decomposition. Besides, the above methods
edit facial radiance fields based on time-consuming optimization or
single-view encoders. In contrast, our approach can efficiently edit
facial NeRFs in free views with a carefully designed prediction and
refinement architecture.
2.2 NeuralSketch-basedFace Generation
Sketch-based 2D Facial Image Generation. Sketching has been
widely used in facial image generation and editing. A pioneer work
[Huetal.2013]representsimagesintoahierarchicalrepresentation
and supports sketch-based editing by retrieval. Since there is a
domaingapbetweenfreehandsketchesandsyntheticedgemaps,
existingapproaches[Chenetal .2020;Lietal .2019,2020;Suetal .
2022; Yang et al .2021a] introduce various strategies to improve
the robustness against different styles of freehand sketches. Instead
ofsynthesizingnewfaces,sketch-basededitingapproaches[Chen
et al.2021; Jo and Park 2019; Liu et al .2022, 2021; Portenier et al .
2018;Yuetal .2019;Zengetal .2022]aimtomanipulaterealfacial
imageswhileretainingtheoriginalidentityfeatures.Human-drawn
orpredicted2Dmasksareusuallyutilizedtoachievelocalediting
results.Theaboveapproachesonlysynthesize2Dresults,whileour
methodappliessketchingtorealistic3Dfacegenerationandediting
in NeRF. Instead of generating RGB images, we first generate 3D
tri-plane features from single-view sketchesand then project them
intothelatentspaceofEG3D.Similarmaskguidanceisalsoadapted
into3Dspace to supportlocalmanipulations.

1:4 • Lin Gao,Feng-LinLiu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li,Yu-Kun Lai,andHongboFu
single-scenestyletransferandcannotbeusedforgenerativeNeRFs,
such asEG3D [Chan et al .2022]. Besides,sincetheabovemethods
stylizeascenebasedonasingleexampleimage,itisunclearhow
to applythese methodstopairedimagedatasets forstylization.
3 METHOD
3.1 OverviewandPreliminaries
Fig.2illustratesoursketch-basedfacialNeRFgenerationandediting
framework. In Sec. 3.2, we describe the sketch-based facial NeRF
generationapproach.Takingasinputsa2Dsketchandanappear-
ance reference image, a Sketch Tri-plane Prediction net augments
the sketch with color and stereoscopic information to synthesize
tri-plane features, which are projected into EG3D’s latent space
togeneratehigh-qualityfacialNeRFs.InSec.3.3,wedescribeour
sketch-basedfacialNeRFeditingapproach.Tosupportfreeviewedit-
ingandmaintaintheidentitycharacteristicsinunseenandunedited
3Dregions,thetri-planefeaturessynthesizedbytheeditedsketches
andtheoriginaltri-planefeaturesarefusedbya MaskFusion module,
andencodedbackintothelatentspaceofEG3D.Then,wefurther
refine the rendered result by a latent code optimization with sketch
constraints. It should be noted that the sketch-based generation
andeditingsharethesame3Dtri-planepredictionandprojection
networks.Thesketch-basedgeneratedfacescanbefurtheredited
to achieve detailed local manipulations from flexible viewpoints, as
showninFig.6.Thisarchitecturehelpsuserseasilydesign3Dfacial
NeRFs withdetailedcontrol.
Webuildourapproach basedon EG3D [Chan et al .2022], apre-
trained 3D face NeRF generator. Given the latent code 𝑤, three
orthogonal plane features (referred to as tri-plane features) 𝑝𝑥𝑦,
𝑝𝑥𝑧, and𝑝𝑦𝑧are generated from the StyleGAN2 [Karras et al .2020]
backbone. Each 3D queried position 𝑥∈R3is projected onto the
threefeatureplanestogetthecorrespondingfeatures 𝐹𝑥𝑦,𝐹𝑥𝑧,and
𝐹𝑦𝑧viabilinearinterpolation,andsuchfeaturesarethenaggregated
into3Dfeatures 𝐹viasummation.Animagedecoderinterprets 𝐹to
color features and densities, with the subsequent volume rendering
[Mildenhalletal .2021]tosynthesizelow-resolutionfeaturemaps
whosefirstthreechannelsareRGBimages.Asuper-resolutionmod-
ule further translates the feature maps into high-resolution images.
More details of the generator can be found in [Chan et al .2022].
Our method generates 3D faces in EG3D’s W+space, composed
of 14 different latent codes. The efficient tri-plane representation is
alsoutilizedin our3Dfeaturerepresentation.
3.2 Sketch-basedFacial NeRF Generation
Faithfullytranslatinghand-drawn2Dsketchesintorealistic3Dfaces
is an attractive but challenging task. A naïve approach is to directly
encodetheinput2DsketchesintothelatentspaceofEG3Dwitha2D
encoder(e.g.,[Richardsonetal .2021])andutilizestyle-mixing[Chan
etal.2022;Karrasetal .2020]tocontroltheappearance.However,
since the encoder is originally designed for 2D GANs and the style-
mixing cannot exactly control the appearance in EG3D [Chan et al .
2022],such asolution isnotrobustto theinput sketchesat details
such as hairstyles and does not guarantee accurate appearance
styles,asshowninFig.8.Incontrast,wearemotivatedtofirstlift
2Dinputsinto3Dinputsandthenencodetheminto3Doutputs.TheSketch-based  3D  Facial  Model  Generation.  Many  efforts  have  been  
made  to utilize  sketching  to design  3D  face  geometry.  One  category  
of methods  [Han  et al. 2017;  Huang  et al. 2022b]  predicts  the  coeffi-
cients  of a bilinear  face  representation  based  on  sketches,  combined  
with  displace  maps  [Ling  et al. 2022;  Yang  et al. 2021b]  to manipulate  
surface  details.  Another  category  of approaches  [Du  et al. 2020;  Han  
et al. 2018;  Luo  et al. 2021]  utilizes  sketches  to guide  the  template  de-
formation  and  generates  diverse  types  of 3D  models  such  as animal  
faces.  These  previous  methods  succeed  in generating  3D  models,  but  
they  could  not  produce  photo-realistic  face  images  directly  since  it 
is difficult  to estimate  high-quality  texture  maps  and  materials  only  
from  sketches.  Moreover,  most  of the  above  approaches  only  focus  
on  face  regions  separately  without  hair  and  facial  details  like  pupils.  
In our  method,  the  3D-aware  hair  and  pupils  are  generated  together.  
Thanks  to the  NeRF  representation,  our  method  not  only  gener-
ates  photo-realistic  3D  faces  from  2D  sketches,  but  also  constructs  
high-fidelity  facial  geometry,  as  shown  in Fig.  4.
2.3  Sketch  Rendering  of 3D  Shapes
Rendering  high-quality  sketches  of 3D  shapes  benefits  sketch-based  
modeling  since  paired  training  data  can  be  synthesized  and  ana-
lyzed.  A differentiable  sketch  rendering  approach  can  further  utilize  
sketches  as  constraints  to optimize  models.  To  render  sketches,  a 
straightforward  approach  is to first  render  the  depth,  normal  or RGB  
maps,  and  then  utilize  image-space  algorithms  [Canny  1986;  Vinker  
et al. 2022;  Wang  et al. 2018;  Xie  and  Tu  2015;  Yi  et al. 2020,  2019]  
to generate  line  drawings.  However,  these  image-based  methods  
tend  to generate  inconsistent  results  across  views  because  of the  
insufficient  utilization  of 3D  information.  Another  branch  of meth-
ods  analyzes  the  mathematical  features  of surface  geometry  and  
defines  different  shape-depicted  lines  [Bénard  et al. 2019;  DeCarlo  
et al. 2003;  Judd  et al. 2007;  Ohtake  et al. 2004],  which  are  combined  
together  [Liu  et al. 2020]  to generate  high-quality  sketches.  How-
ever,  these  methods  require  extremely  high-quality  explicit  mesh  
models  as inputs,  which  are  not  cheap  to obtain  from  a 3D  implicit  
representation  like  NeRF  with  commonly  used  techniques,  such  as 
marching  cubes  [Lorensen  and  Cline  1987]  and  DMTet  [Shen  et al. 
2021].
In order  to synthesize  high-quality  sketches  in NeRF,  neural  style  
transfer  [Gatys  et al. 2016]  is a promising  solution.  However,  there  
are  two  main  challenges  in NeRF  style  transfer:  extensive  mem-
ory  usage  caused  by  rendering  whole  images  during  transfer  loss  
calculation  and  view  inconsistency  when  adapting  image  transfer  
algorithms.  Chiang  et al.  [2022]  first  propose  a memory-efficient  
patch  sub-sampling  algorithm  and  train  a color  branch  with  a fixed  
geometry  branch.  StylizedNeRF  [Huang  et al. 2022a]  further  de-
signs  a mutual  learning  framework  and  learnable  conditional  latent  
codes  to improve  the  view  consistency.  Other  methods  maintain  the  
original  NeRF  networks  but  design  novel  training  strategies.  For  
example,  SNeRF  [Nguyen-Phuoc  et al. 2022]  alternates  the  NeRF  
training  and  stylization  optimization  steps,  consuming  less  memory  
in each  stage  while  retaining  the  original  consistency  of NeRF.  ARF  
[Zhang  et al. 2022]  introduces  a new  view-consistent  style  loss  and  a 
deferred  back-propagation  method  to enable  memory-intensive  op-
timization.  However,  existing  methods  are  specifically  designed  for

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:5
Latent Code Optimization From Sketch
Sketch
RenderingImage
Rendering
Translation 
NetworkTranslation 
Network
cGcG
Sketch Tri-plane Prediction Network
Feature MapInput Sketch Feature V olumeReshape
Concat
Input View
Input Viewzy
x
sp
editwcfcE
Mask Fusion 
ModuleEG3DEditing
Ori-Image Ori-Sketch Binary MaskStrokes
projE
Conv 
NetworkConv
Network
vGvG
pII
w
epGenerationGenerationS
Fig. 2. Overview of our unified sketch-based facial NeRF generation and editing framework. Given an input sketch 𝑆, theSketch Tri-plane Prediction Network
generatesatri-planefeaturerepresentation 𝑝𝑠for3Dinformation.Thecolorinformationissupplementedbyanappearanceencoder 𝐸𝑐andatranslation
network𝐺𝑐togenerateacolorizedfeaturemap 𝑓𝑐,andthestereoscopicinformationissupplementedby 𝐺𝑣.TogenerateafacialNeRFfromscratch(indicated
bythedottedlinebelow 𝑝𝑠),thetri-planefeature 𝑝𝑠isdirectlyencodedby 𝐸𝑝𝑟𝑜𝑗togenerate3Dfaces.ForfacialNeRFediting(indicatedbythesolidline
below𝑝𝑠),theMaskFusion modulefuses 𝑝𝑠andtheoriginaltri-planefeature 𝑝togenerate 𝑝𝑒,whichisalsoencodedbytheshared 𝐸𝑝𝑟𝑜𝑗.Moreover,we
propose a sketch-basedoptimization approach toimproveediting faithfulness and originalfeatureretentionfurther.
tri-plane representation is utilized because of its high expressive
capacity andadaptabilityfor2Dconvolutionin encoders.
In particular, to lift 2D inputs into 3D, we draw inspiration from
PixelNeRF[Yuetal .2021],whichisdevisedtoinfer3Dinformation
fromsparse2Dinputs.Givenasingle-viewhand-drawnsketch 𝑆,
wedesigna SketchTri-planePrediction nettolift 𝑆intoa3Dtri-plane
representation 𝑝𝑠, including feature maps 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧, and𝑝𝑠𝑦𝑧, which
isexpectedto liein thesamedistributionastheEG3D-generated
tri-planerepresentation.Theinputsketchonlycontainsgeometry
information, but the tri-plane features and rendered images have
diverseappearancedetails. Sowefirst translatethe input sketch 𝑆
toacolorizedfeaturemap 𝑓𝑐toinjectcolors,lighting,andtexture
information. We train an appearance encoder 𝐸𝑐to extract the
appearance information from the reference image 𝐼. To transfer
the appearance to 𝑆, we leverage a translation network 𝐺𝑐with
adaptiveinstancenormalizationHuangandBelongie2017
to generatethecolorizedfeaturemap:
𝑓𝑐=𝐺𝑐(𝑆,𝐸𝑐(𝐼)). (1)
To predict a 3D tri-plane representation from a 2D feature map, we
build a3D featurevolume in the Euclidean space wherethe volume
rendering is performed.Using theestimated cameraintrinsics and
extrinsics,eachpoint 𝑥inthe3Dvolumeisprojectedontotheinput
imagespacetoget2Dcoordinates 𝜋(𝑥).Then,thecorresponding
featurevector 𝑓𝑐(𝜋(𝑥))is retrieved for each point 𝑥via bilinear in-
terpolationfromcolorizedfeaturemaps 𝑓𝑐.Tobalanceperformance
and efficiency, we build the feature volume with a resolution of 128
ineachaxis,resultinginthefinalshapeof128 ×128×128×3,where
3 is the channel number of feature maps 𝑓𝑐. The 3D feature volume
isfurtherreshapedalongeachaxistogeneratethree128 ×128×384
feature maps, denoted as 𝑉𝑥𝑦,𝑉𝑥𝑧, and𝑉𝑦𝑧. These feature mapsare concatenated in the feature channel, using a 2D convolution
network𝐺𝑣toupsampleandtranslatethemintoa256 ×256×96
sketch feature map, which is split channel-wise and reshaped to
formthree32-channelfeatureplanes 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧,𝑝𝑠𝑦𝑧, abbreviatedas
thetri-planefeature 𝑝𝑠:
𝑝𝑠=𝐺𝑣(𝑉𝑥𝑦,𝑉𝑥𝑧,𝑉𝑦𝑧). (2)
Although  our  Sketch  Tri-plane  Prediction  net  thoroughly  analyzes  
the  3D  information,  only  2D  convolution  is utilized  here  to improve  
memory  and  time  efficiency.  After  lifting  the  2D  inputs  into  3D  as the  
tri-plane  representation,  we  utilize  a 2D  encoder  𝐸𝑝𝑟𝑜  𝑗 [Richardson  
et al. 2021]  to project  the  tri-plane  features  into  the  W+ space  of 
EG3D  to improve  the  quality,  which  renders  final  photo-realistic  
free-view  facial  images.
Training  Objective.  The  Sketch  Tri-plane  Prediction  net  is trained  
using  a synthesized  multi-view  dataset.  Given  a set  of latent  codes,  
ground-truth  tri-plane  features  𝑝 are  synthesized  based  on  EG3D.  
For  each  example,  we  randomly  sample  multiple  camera  poses  to 
synthesize  paired  sketches  and  images,  using  the  sketch  generation  
approach  discussed  in Sec.  3.3.1.
Given  an  input  single-view  sketch,  the  Sketch  Tri-plane  Prediction  
net  synthesizes  a tri-plane  feature  𝑝𝑠 , which  generates  images  𝐼𝑠 
through  volume  rendering  with  the  original  decoder  of EG3D  [Chan  
et al. 2022].  These  rendered  images  have  different  views  𝑡 from  the  
input  sketch.  The  above  strategy  enhances  the  3D  information  by  
enforcing  the  network  to imagine  faces  from  other  views,  as in [Sun  
et al. 2022a].  The  loss  function  L(𝐸𝑐, 𝐺𝑐, 𝐺𝑣) to train  the  Sketch  
Tri-plane  Prediction  net  is defined  as:
L(𝐸𝑐, 𝐺𝑐, 𝐺𝑣) = 𝛽1L1 (𝑝𝑠, 𝑝) + 𝛽2L1 (𝐼𝑠, 𝐼𝑡 ) + 𝛽3L𝑉𝐺𝐺  (𝐼𝑠, 𝐼𝑡 ), (3)

1:6 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
whereL1denotes the L1 distance, LVGGdenotes the perception
distance [Zhang et al .2018], and 𝐼𝑡is the ground-truth image in
atargetview.Forhigh-qualityresults,webothconstrainthesyn-
thesizedtri-planerepresentation 𝑝𝑠tobeinthesamedistribution
astheground-truthtri-planefeatures 𝑝,andmeasurethesimilar-
itybetweentherenderedimagesandground-truthimages.Inour
experiments, weset 𝛽1=0.01,𝛽2=1.0, and𝛽3=0.1.
To train the encoder 𝐸𝑝𝑟𝑜𝑗, we use the same strategy of pSp
[Richardsonetal .2021],butinsteadofRGBimages,ittakesconcate-
natedtri-planefeatures 𝑝𝑠asinput.Bothofthetri-planefeatures
generated by the original EG3D backbone and the Sketch Tri-plane
Prediction netarefedintothenetworktoimprovethegeneralization
of𝐸𝑝𝑟𝑜𝑗. Pixel-wise L2 loss, LPIPS loss [Zhang et al .2018], iden-
tity loss [Deng et al .2019a], and regularization loss are used for
training such an encoder. Please refer to [Richardson et al .2021]
for more details. We first train the Sketch Tri-plane Prediction net to
convergence andthen traintheencoder.
3.3 Sketch-basedFacial NeRF Editing
Althoughthegenerationof3Dfacesfrom2Dsketchesisqualifiedfor
applicationslikecharacterdesign,usersmaydesiretofurtheradjust
3D faces by interactively editing the corresponding 2D sketches
from different views, as in [Chowdhury et al .2022]. This motivates
us to design a sketch-based interface for facial NeRF editing, which
allows2Dlocaleditingfromdifferentviewsandachievesconsistent
3Deditingeffects while preservingtheuneditedregions.
3.3.1 Free-viewSketchGeneration. Firstofall,wesynthesizefree-
viewrenderingsketcheswhichuserscanmodifytoeditthecorre-
sponding3Dfaces.Thisprocessisdifferentiableandfurtherused
forlatentcodeoptimizationasdiscussedinSec.3.3.3.Anadditional
sketchgenerationpath,whichhasasimilararchitecturetotheimage
generationpathofEG3D[Chanetal .2022],isthereforedesigned
togeneratethecorrespondingsketchatanyviewofalatentcode.
ThesketchandimagerenderingbranchessharethesameStyleGAN
backboneandprojectionfeaturesbuthaveseparatedecodersand
super-resolution modules. Specifically, a new sketch decoder in-
terpretsthesamplefeaturestosketchfeatures,combinedwiththe
densityofthe image branch togenerate low-resolutionsketch fea-
turemapsbasedonvolumerendering.The1stchannelofthesketch
feature maps corresponds to low-resolution sketches (128 ×128),
denotedas 𝑆′
𝑟𝑎𝑤.Asketchsuper-resolutionmodulesimilartothe
originalsuper-resolutionmoduleinEG3Disalsousedtosynthesize
finalsketches 𝑆′.
To train such sketch generation path, a pretrained pix2pixHD
[Wang et al .2018] is used to convert rendered facial images into
ground-truth high-resolution sketch 𝑆𝐺𝑇, which is, however not
3D-consistent. We carefully design training losses to ensure consis-
tency from different views and synthesize high-quality sketches. A
reconstructionlossisusedtomatchtheoriginalsketchdistribution:
L𝑟𝑒𝑐𝑜𝑛=𝛼1L1(𝑆′,𝑆𝐺𝑇) +𝛼2L1(𝑆′
𝑟𝑎𝑤,𝑆𝑟𝑎𝑤)
+𝛼3L𝑉𝐺𝐺(𝑆′,𝑆𝐺𝑇) +𝛼4L𝑉𝐺𝐺(𝑆′
𝑟𝑎𝑤,𝑆𝑟𝑎𝑤),(4)Inspired by [Gu et al .2022], we utilize a regularization term to
enforce the 3D consistency of sketches. Although the predicted
ground-truth sketch 𝑆𝐺𝑇is not view-consistent, the inherent multi-
viewconsistencyofvolumerenderingconstrainsthefinalresults
of the super-resolution module. This loss term compares the sub-
sampledpixels on thefinalsketch resultsandthosegeneratedby
NeRF:
L𝑣𝑖𝑒𝑤=𝛼51
/summationtext.1
(𝑖,𝑗)∈𝐶/bardblex/bardblex/bardblex𝑆′[𝑖, 𝑗] −𝑅𝑒𝑛𝑑𝑒𝑟(𝑟𝑖,𝑗)/bardblex/bardblex/bardblex1,(5)
where𝐶is the set of randomly sampled pixels in final sketches
and𝑖, 𝑗are the coordinates in the high-resolution2D sketch space,
𝑅𝑒𝑛𝑑𝑒𝑟(𝑟𝑖,𝑗)is thepixel result of direct volume rendering with the
correspondingray 𝑟𝑖,𝑗.Inourexperiments, 𝛼5=3.0and=8,192.
The finaltrainingobjectiveis:
L𝑠𝑘𝑒𝑡𝑐ℎ=L𝑟𝑒𝑐𝑜𝑛+L𝑣𝑖𝑒𝑤. (6)
During the training of the free-view sketch generation, the weights
of the StyleGAN backbone and the image generation path are fixed.
Weonlyupdatetheweightsofthesketchdecoderandsketchsuper-
resolution module.
Finally,usersareabletodirectlyedittherenderedsketches(adding
or removing strokes) to obtain edited sketches 𝑆instead of drawing
sketchesforevery viewfromscratch.
3.3.2 Mask Fusion Module. Given the modified sketch 𝑆, the intro-
ducedSketch Tri-plane Prediction net together with 𝐸projin Sec. 3.2
isalreadyabletogeneratehigh-quality3Dfaces.However,unedited
regions might suffer from undesirable changes despite local editing
operationsonthe2Dsketchsincethe2Dsketchatacertainview
cannot describe the entire 3D object, as demonstrated in Fig. 13.
Targetingthisissue,weproposea Mask Fusion moduletospatially
fuse the original 3D face generated from the input latent code 𝑤,
and the newly generated 3D face based on the edited sketch with a
3Dmask, preserving the unedited regionsand retaining theedited
regions. Notice that since both faces are represented as tri-plane
features, the fusion can be conducted on tri-plane features directly.
Subsequently,thespatiallyfused3Dfaceisencodedbackintothe
latent space of EG3D as 𝑤𝑒𝑑𝑖𝑡. It should be noticed that the input
latent code 𝑤can be an EG3D sample, real image projection, or
the previously edited result to apply multi-step manipulations from
different views.
To obtain the guiding 3D fusion mask, after users perform the
editingoperationsviaourinterface,wefirstestimatea2Dbinary
mask𝑀indicating the edited regions (introduced in Sec. 4). For
theinputlatentcode 𝑤,theoriginaltri-planerepresentation( 𝑝𝑥𝑦,
𝑝𝑥𝑧,𝑝𝑦𝑧) is synthesized based on the EG3D backbone along with a
generateddepthmap 𝐷.Basedonthedepthmap 𝐷,eachpixelin
𝑀is converted to its 3D location in the Euclidean space, forming a
setof 3Dpoints. Sincethefusionisessentially conductedonthetri-
planefeatures,suchasetof3Dpointsareprojectedintothetri-plane
space to synthesize three sets of 2D points. For the modified sketch
𝑆𝑒𝑑𝑖𝑡, anewtri-planerepresentation( 𝑝𝑠𝑥𝑦,𝑝𝑠𝑥𝑧,𝑝𝑠𝑦𝑧)issynthesized
bytheSketchTri-planePrediction netalongwithitsgenerateddepth
map, and another three sets of 2D points are obtained similarly.
By uniting two sets of 2D points on each plane, dilating them for
a smoother border, and connecting sufficiently close regions, wewhere  𝑆𝑟𝑎𝑤  represents  the  downsampled  sketch  of a high-resolution  
sketch  𝑆𝐺𝑇  . In our  experiments,  we  empirically  set  𝛼1 = 𝛼2 = 3.0 
and  𝛼3 = 𝛼4 = 2.0.

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:7
obtain the guiding 3D fusion mask, which is composed of three 2D
fusionmasks on eachplane, denotedas 𝑀𝑥𝑦,𝑀𝑥𝑧,𝑀𝑦𝑧.
Then, theoriginal andpredictedtri-planefeatures arefusedas:
𝑃𝑒
𝑥𝑦=𝑀𝑥𝑦·𝑝𝑠
𝑥𝑦+ (1−𝑀𝑥𝑦) ·𝑝𝑥𝑦. (7)
Hereweonlytake 𝑥𝑦asanexample,andoperationsontheother
two planes are the same. We denote the generated new fused tri-
planefeaturesas 𝑝𝑒𝑥𝑦,𝑝𝑒𝑥𝑧,𝑝𝑒𝑦𝑧.Itshouldbenoticedthatswapping
aregioninthetri-planefeaturesaffectsthewholeorthogonal3D
columnarspace,sothefusedtri-planefeaturescannotbedirectly
used for volume rendering. To solve this problem, we utilize the
same encoder 𝐸𝑝𝑟𝑜𝑗in Sec. 3.2 to further project the fused tri-plane
featureintothe W+space, as 𝑤𝑒𝑑𝑖𝑡=𝐸𝑝𝑟𝑜𝑗(𝑝𝑒𝑥𝑦,𝑝𝑒𝑥𝑧,𝑝𝑒𝑦𝑧).
3.3.3 Latent Code Optimization from Sketch. We further refine
𝑤𝑒𝑑𝑖𝑡toensurebettersketchfaithfulnessandoriginalfeaturereten-
tion in challenging cases. For example, the predicted hair structure
oreyeglassshapesmayhavesmallbiaseswithdrawnsketches,as
shown in Fig. 12. The background of the original faces is some-
times too sophisticated to reconstruct. Our optimization strategy is
designedto solvethese problemsin challenging editingsituations.
Editing Optimization. Inspired by [Liu et al .2022], we propose
an optimization approach to refine the predicted latent code 𝑤𝑒𝑑𝑖𝑡.
With the image rendering branch R𝑥and the sketch rendering
branchR𝑠, thefollowinglosstermsareoptimized:
L𝑒𝑑𝑖𝑡=L𝑉𝐺𝐺(R𝑠(𝑤𝑒𝑑𝑖𝑡) ⊙𝑀,𝑆⊙𝑀), (8)
L𝑖𝑚𝑔=L𝑉𝐺𝐺(R𝑥(𝑤𝑒𝑑𝑖𝑡) ⊙/tildewide𝑀,R𝑥(𝑤) ⊙/tildewide𝑀),(9)
where/tildewide𝑀referstotheuneditedregions,and ⊙denotespixel-wise
multiplication. These loss terms encourage the edited regions to
haveconsistentsketchesas 𝑆andremainingregionstobeunaltered
asmuch aspossible.
Utilizingtheabovelosstermsisadequatetosolvethe2Dimage
editingproblembutnotenoughfor3DfaceeditinginNeRF,sincethe
stereoscopic features should be maintained to ensure that unedited
regions are retained in arbitrary views. One possible approach is to
addmulti-viewlosses,butviewpointselectionandview-specific2D
mask transformation are difficult. So, we propose a novel space loss
term to measure the similarity of sample points’ features utilized in
volume rendering:
L𝑠𝑝𝑎𝑐𝑒=1
𝑁/tildewidest/summationtext.1/tildewide𝑀𝑟/summationtext.1𝑁
𝑖∥Φ(𝑤𝑒𝑑𝑖𝑡,𝑟(𝑖)) −Φ(𝑤,𝑟(𝑖))∥1,(10)
where𝑟(𝑖)is the𝑖th sample point along the rendering ray 𝑟in
unedited regions, 𝑁is the number of sample points, and Φde-
notesthepoint-wisefeaturecalculationprocess,includingtri-plane
projection, and feature decoding. Hierarchical volume sampling
[Mildenhall et al .2021] is used in NeRF rendering, while we only
calculate the space loss on coarse samples which have the same
positionfordifferentidentities.Theoveralllossfunctionforopti-
mizationis:
L(𝑤𝑒𝑑𝑖𝑡)=𝛾1L𝑒𝑑𝑖𝑡+𝛾2L𝑖𝑚𝑔+𝛾3L𝑠𝑝𝑎𝑐𝑒,(11)
where𝛾1,𝛾2, and𝛾3are hyper-parameters tuned by users. In our
experiments,theyaresetas 𝛾1=40,𝛾2=20,and𝛾3=0.2bydefault,
Fig. 3. The user interface of SketchFaceNeRF. The interface has two modes.
In the generation mode, the left window is used for creating freehand
sketches. In the editing mode, the left window is used for editing line
drawings from a 3D face. In both modes, the right window shows the
generatedface.Acontrolpanelatthetopoftheinterfacesupportsmany
essentialoperations,includingtheselectionofpenciloreraser,brushsize
control, and rotationof viewpoints.
andtheiterationstepsaresetas10withthebalanceofefficiency
andquality.
4 USER INTERFACE
AsshowninFig.3,wedesignauserinterfaceontopoftheproposed
pipelinetosupportsketch-basedfacialgenerationandmulti-step
editing in different views. To generate facial NeRFs from scratch,
users draw sketches on the left drawing canvas, and our system
thensynthesizesanddisplaysphoto-realistic3Dfacesintheright
window.Ourinterfacefurthersupportsdetailedfacialeditingvia
editing sketches synthesized from previously generated 3D faces
(Fig. 6), EG3D random samples, and real face images (Fig. 5). Users
can change the views with the sliderson the controlpanel, during
whichprocessthegenerated3Dfacesandcorrespondingsketches
are rotated simultaneously. Thanks to our free-view sketch gen-
eration approach, the synthesized sketches are consistent during
view changes, thus improving the user interaction experience. Dur-
ing editing, users may erase undesired lines and draw new lines
depicting desired structures. These operations provide adequate
information to infer the mask 𝑀representing the edited regions.
Specifically,wedilatethenewlydrawnlinesandunitethemwith
the erasing regions to generate an initial mask. Then, the small
holeswithintheeditedregionsarefilledbyconnectiondetection,
and the border is smoothed by polygonal curves. With the input
face,modifiedsketch,andinferredmask,ouralgorithmgenerates
new edited NeRF faces, which are rendered and shown in our UI
system.Aftereditinginasingleviewpoint,userscanrotatetheface
and continuously edit it in other views, supporting detailed and
expressivefacialmanipulation.

1:8 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
Fig.4. FacialNeRFgenerationresultsgivenhand-drawnsketches.Theinputsketchesandgeneratedgeometryareshowninthefirsttwocolumns.Photo-
realistic rendering results withfree viewpoints are shown in the following columns, with different appearance images in the top-left corner of each example.
Ourmethodgeneratesdetailedgeometrybysketchesandcontrolstheappearancewithreferenceimages.Sincewefocusonfacialregiongeneration,the
background of referenceimagesismasked.Thegeneratedimageshavesemi-random backgrounds entangledwithfaces in the EG3D space.
Original
 Edit
 Result
 Real Image
 Projection
 Edit
 Result
Fig.5. InteractiveFacialNeRFeditingresults.Userscaninteractivelyselectanarbitraryviewandthenedit3Dfacesbymodifyingtherenderedsketches.
Hand-drawnsketchesarelabeledinred,andthegrayregionsaretheinferredmasksoftheuserinterface.Someeditingexamplesareshowninthisfigure,e.g.,
hairstyle, glasses, beard, eyes, and expression. Our method edits the local regions while maintaining the global features of the original faces. Real images can
beprojectedandeditedtosynthesize free-view results.
of5𝑒−3andoneiterationcosts0.18sonourdevice.Moredetails
of the dataset and training settings can be found in supplementary
materials.
5.1 Results
Our method supports high-quality facial NeRF generation based on
single-viewsketchesandappearancereferenceimages.Wetreatthe
facialNeRFgenerationfromscratchasaspecialeditingsituation
where the predicted tri-plane features are directly projected into
thelatentspacewithoutmaskfusionandoptimization.Theview-
ing angles of hand-drawn sketches are estimated by [Chen et al .
2020; Deng et al .2019b]. As shown in Fig. 4, given the hand-drawn
sketches that represent the facial geometry details, including the
facialcomponentshapes,hairstructures,andbeard,ourapproach
generates high-quality 3D geometry models with good faithfulness
forsketches.Althoughthehand-drawnsketcheshavevariousdraw-
ingstylesthataredifferentfromthoseinthetrainingdataset,our
methodisrobustandcanstillgeneratehigh-qualityfacialmodels.5 EVALUATION
In this  section,  a series  of qualitative  and  quantitative  experiments  
are  conducted  to demonstrate  the  superiority  of our  framework.  In 
Sec.  5.1,  we  show  the  sketch-based  facial  NeRF  editing  and  genera-
tion  results  of our  method.  In Sec.  5.2,  the  qualitative  and  quanti-
tative  comparisons  with  state-of-the-art  methods  are  conducted  to 
demonstrate  the  better  performance  of our  approach.  In Sec.  5.3,  we  
conduct  an  ablation  study  to validate  the  effectiveness  of each  mod-
ule  and  network  design  in this  framework.  A user  study  is presented  
in Sec.  5.4  to further  prove  the  superiority  of our  approach.
Implementation  Details.  To  train  the  facial  NeRF  manipulation  
framework,  we  synthesize  a multi-view  dataset  based  on  EG3D  
with  110k  training  samples.  For  each  example,  25  rendered  images  
from  different  views  are  generated  while  the  tri-plane  features  are  
synthesized  on  the  fly  during  training.  Our  networks  are  trained  
and  tested  on  an  NVIDIA  RTX  3090  GPU.  During  optimization,  we  
use  the  ADAM  [Kingma  and  Ba  2014]  optimizer  with  a learning  rate

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:9
(a) Image
 (b) Sketch
 (c) Results
Fig.6. Sketch-basedgeneration(1strow)andediting(2ndrow)forfacial
NeRF design. The 1st row shows a sketch-based generation example based
onanappearanceimage(a)andahand-drawnsketch(b).Userscanfurther
applydetailededitingviaasynthesizedsketch(b)whilemaintainingthe
originalidentity characteristics.
Sketchesonlyprovidegeometryinformationbutlackcolorinforma-
tion,so ourfaceregressionnetworkcontrols theappearancewith
exampleimages.FacialNeRFmodelswithdifferentcolors,materi-
als,andlightingaresynthesized,andtheviewpointscanbefreely
controlled by users, as shown in Fig. 4. More generation results can
befound in thesupplementary materials.
With our carefully designed framework and user interface, users
caninteractivelyeditfacialdetailsviasketchesfromfreeviewpoints.
After the sketch modification using brush and eraser tools, the user
interface automatically infers the edited masks (colored as gray
in Fig. 5). As illustrated, the masks accurately label locally edited
regions. High-quality results are generated by our method with
various types of editing operations, including adding/removing
eyeglasses,changinghairstructures,andmodifyingfacialshapesor
expressions.Theeditedregionsshowgoodeditingeffects,whilethe
global features are well maintained. As shown in Fig. 7, our system
generates good multi-step editing results for a single example from
multiple views. The editing operations performed from different
viewsarealleffective.Theresultsshownodeteriorationwiththe
accumulationofeditingoperationsthankstoourtri-planeprojection
and optimization approaches. More editing results with different
drawing styles canbefound in oursupplementary materials.
5.2 Comparison
Sketch-basedFacialNeRF Generation. Sinceourmethodcangen-
eratehigh-qualityfacialNeRFsbasedonsingle-viewsketches,we
compare it with possible existing sketch-based facial NeRF genera-
tion approaches, with some adaptations. PixelNeRF [Yu et al .2021]
synthesizes NeRFs with the input of single-view images, which are
replacedwithsingle-viewsketchesinourexperimentstosupport
ourtask.As shownin Fig. 8, this approach cannotcontroltheface
appearanceandisnotrobustforhand-drawnsketcheswithfuzzy
details. DeepFaceEditing [Chen et al .2021] synthesizes face images
for hand-drawn sketches and controls the appearance accurately,
but thereare still artifactsaround the neck and hair regions. Since
(a) Original
 (b) Sketch
 (c) Result
 (d)ResultFront
Fig. 7. Results of multi-step editing (from top to bottom) in different views.
In (a), the topmost image is the original face, and the rest are previously
editedresultsforfurtherediting.Modifiedsketchesandgeneratedresults
areshownin(b)and(c),respectively.Theeditingmanipulationsareaddedin
differentviews.Ourmethodwellmaintainstheoriginalfeaturesinunedited
regions (d) and avoids deterioration even though the results are recursively
used.
thedata-drivenmanifoldprojectionisutilized,somelesscommon
appearances,likethebigcurlyhair(3rdrow)andbangs(4throw),
cannot be synthesized well. We further project the results of Deep-
FaceEditingintoEG3D’slatentspacetogenerateNeRFresults,as
shown in the 5th column in Fig. 8. Based on the pretrained gen-
erator, the projection results are more realistic but still have low
faithfulnesswithinputsketches,e.g.,themistakenhairstructures.
pSp [Richardson et al .2021] is a new image translation approach
based on 2D StyleGAN [Karras et al .2020]. For fair comparison,
wereplacetheStyleGANwiththeEG3Dgeneratorandutilizethe
style-mixing to swap the last 7 layers of latent codes to support ap-
pearancecontrol.Althoughthisapproachgeneratesgoodresultson
synthesized sketches (see supplementary material), it is not robust
forhand-drawnsketchesandhaspoorgeometryfaithfulness.The
style-mixingalsocannotcontroltheappearanceaccuratelyin3D
GAN because of the complicated rendering process. Our method is
thefirstapproachtosynthesizingfacialNeRFsfromsketchesand
hasbetter resultsthanpossiblebaselines.

1:10 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
(a) Sketch
 (b) PixelNeRF
 (c) Appearance
 (d)DFE
 (e)DFE-Projection
 (f) pSp-Ref
 (g) Ours
Fig. 8. Comparisons with state-of-the-art methods for hand-drawn sketches to facial NeRF translation. In each row, (a) is a user-drawn sketch, and (c) is an
appearance reference image. PixelNeRF (b) cannot control the appearance and generates blurry results. Other existing methods (d) ∼(f) generate results with
the input ofreference appearance but have poorfaithfulness withsketches(d,e) or appearance (f), while ourmethod (g) generates the best results. DFE and
DFE-projarethe abbreviationsof DeepFaceEditing [Chen etal.2021] and itsNeRFprojection version.
PixelNeRF DFE DFE-projpSp pSp-Ref Ours
FID↓189.30 77.63 97.94 94.15 80.69 72.63
KID↓16.25±0.21.98±0.22.99±0.24.52±0.22.56±0.22.06±0.2
Table 1. Quantitative results compared with sketch-based facial generation
approaches.WereporttheFIDandKIDmean×100 ±std.×100.Ourresults
have lower (i.e., better) values compared with other approaches, except
comparablevalueswithDeepFaceEditing(DFE),which,however,isdesigned
onlyfor2D imagesinstead ofNeRF.
forfaithfulness,fusethemwiththeoriginaltri-planefeaturesfor
consistency, and encode the fused tri-plane features back to the 3D-
aware generativeprior to improvethe quality. We also include the
optimizationprocesstoensurefaithfulnessandconsistencybetter.
Noticethatourmethodalsodoesnotrequirelaboriousmanualmask
drawing,similar to SketchEdit.
Quantitative Comparison. To measure the facial NeRF generation
and editing quality of the compared approaches, we report the
Fréchet Inception Distance (FID) [Heusel et al .2017] and Kernel
InceptionDistance(KID)[Binkowskietal .2018]inTables1and2.
For sketch-based facial generation, we collected 100 hand-drawn
sketches,whichweresharedbytheauthorsofDeepFaceDrawing
[Chen et al .2020]and collected withtheir online demo system. AsSketch-based  Facial  NeRF  Editing.  Our  method  supports  detailed  
sketch-based  editing  of 3D  human  faces  and  generates  high-quality  
editing  results  in given  views.  Thus,  we  compare  it with  existing  
sketch-based  face  editing  methods.  However,  due  to the  2D  nature  
of the  existing  methods,  they  are  not  3D-aware  as our  method  since  
we  are  the  first  to edit  3D  human  faces  by  sketch.  As  shown  in Fig.  9, 
given  original  facial  images  (a)  and  edited  sketches  (b),  DeepPS  [Yang  
et al. 2020]  dilates  the  sketches  to achieve  higher-quality  results  
than  those  without  dilation  but  compromises  the  faithfulness,  e.g.,  
failing  to turn  the  straight  hair  into  curly  hair  in the  second  row.  
Besides,  it has  obvious  artifacts  near  the  boundary  of the  masks  
due  to its  inpainting  fusion.  DeepFaceEditing  [Chen  et al. 2021]  
produces  reasonably  edited  results,  but  the  image  quality  degrades  
with  complex  editing  manipulations.  Its  results  also  exhibit  artifacts  
such  as darkened  areas  on  the  eyes  (1st  row)  and  forehead  (2nd  row)  
because  of the  local  appearance  disturbance  of the  original  images.  
SketchEdit  [Zeng  et al. 2022]  generates  unrealistic  edited  results  
for  glasses  removal  and  curly  hair,  despite  its  efforts  to  estimate  
the  masks  for  the  edited  regions.  In contrast,  our  method  produces  
high-quality  and  3D-aware  facial  images  rendered  from  different  
views.  Additionally,  our  method  is faithful  to the  edited  sketches  and  
preserves  the  untouched  regions  well.  The  reason  is that  we  carefully  
predict  the  edited  tri-plane  features  directly  from  the  edited  sketches

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:11
(a) Image
 (b) Sketch
 (c) DeepPS
 (d)DeepFaceEditing
 (e)SketchEdit
 (f) Ours
Fig.9. Comparisonswithstate-of-the-artmethodsforsketch-basedfacialediting.Givenoriginalimages(a)andmodifiedsketches(b),DeepPSgenerates
plausible results (c) in edited regions but has obvious artifacts on the editing region boundaries. DeepFaceEditing generates results (d) with the appearance of
input images (a), thus causing color bias in local regions when removing the hair and glasses. SketchEdit is not robust against hand-drawn sketches and
synthesizesblurryresults(e).Ourmethodnotonlygeneratesbetterresults(f)intheoriginalviewsthantheotherapproachesbutcanalsorenderrealistic
free-view results.
DeepPS DFE SketchEdit Ours
FID↓108.8 98.65 112.51 87.68
KID↓5.94±0.54.23±0.56.44±0.54.22±0.4
Table2. Quantitativeresultscomparedwithsketch-basedfaceeditingap-
proaches. We report the FID and KID mean×100 ± std.×100. Our results
have the lowest values among all the compared approaches, indicating the
bestimage quality.
shown in Table 1, our method outperforms the other approaches
except for the comparable KID compared with DeepFaceEditing
[Chenetal. 2021],whose resultsareonly2Dimages andhavelow
sketchfaithfulness,asshowninFig.8.Afterprojection,theartifacts
are accumulated and have worse value results. For sketch-based
facial editing, we collect 50 editing examples based on the user
interface(Fig. 3).AsshowninTable2, eventhoughourmethodis
designed explicitly for NeRF editing, it outperforms all the state-
of-the-artimageeditingmethodsatthemanipulationviewpoints.
Duringthe quantitativecalculation, thebackgroundismaskedout
becauseweonlyfocus on thequality of facialregions.
5.3 AblationStudy
We conduct ablation studies to prove the effectiveness of each com-
ponent in our framework. The key components of the Sketch Tri-
planePrediction netandMaskFusion modulearedisabledtoshow
theirimpacts.Then,thelosstermsinsketch-basedoptimizationare
evaluated respectively to prove their effectiveness. We also replace
thesketchgenerationapproach withother approachesto evaluate
theviewconsistency of our3Dsketches.
In theSketch Tri-plane Prediction net, the sketches are translated
intofeaturemapstosupplementcolorinformation.AsshowninFig.
10, the appearance is unable to control without such an appearance
transferprocess,inconsistentwiththeappearancereferenceimages.
(a) Sketch
 (b) Appear.
 (c) w/o Color
 (d) w/o Volume
 (e) Ours
Fig. 10. Ablation study of the Sketch Tri-plane Prediction net given hand-
drawn sketches (a) and reference appearance images (b). Without coloriza-
tion,theappearancecannotbecontrolled(c).Withoutthefeaturevolume
and directly predicting tri-planes features based on input sketches, the
results have lowfaithfulness (d) with the inputsketches. Our method gen-
erates the bestresults (e) basedonthe sketches and appearanceimages.
Additionally, the stereoscopic information is added by lifting the
2D feature maps into 3D feature volumes through space projection.
Withoutsuch alifting processto enhancethe3D information,the
encodedresultssufferfromlossoffaithfulness,especiallyforhair
andsmalldetailssuchaseyebrows,sinceitishardtodirectlyencode
latentcodesfor3Dmodelsfrom2Dinputs.Incontrast,ourfullmodel
generatesthebestresultsregardingbothgeometryfaithfulnesswith
theinputsketchesandappearancefaithfulnesswiththeappearance
reference images.
Astothe MaskFusion module,withoutnegativelyaffectingthe
editing effects, the fusion operation solves identity distortion in the
original view and preserves unedited regions in other views. As
shown in the first row in Fig. 11, the baseline without the fusion
strategy predicts images that exhibit subtle distortions on the back-
ground and facial shape in the original view (b). When we rotate
it into the front view, the hair is also totally changed (d). Although

1:12 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
Fig. 11. Ablation study of the Mask Fusion module. The original image and drawn sketch are shown in (a). The mouth is closed in this example. As shown in
the 1st row, without the Mask Fusion module, the predicted (b) and optimized images (c) have different backgrounds from the original images. When we
rotate into the front view, the predicted (d) and optimized faces (e) ∼(g) have different haircuts compared with the original faces. As shown in the 2nd row, our
method generates the same background (c) in the original view. The front-view faces well retain the original features with fewer optimization steps compared
withbaseline approaches.
(a) InputImage
 (b) Sketch
 (c) w/o Opt
 (d)w/o Prediction
 (e)w/o𝐿𝑖𝑚𝑔
 (f) w/o𝐿𝑒𝑑𝑖𝑡
 (g) Pix2PixHD
 (h)Ours
Fig. 12. Ablation study of optimization approaches proposed in Sec. 3.3.3. Input images (a) and modified sketches (b) are shown in the first two columns, with
thegrayregionsindicatingtheinferredmasksbyourmethod.Directpredictionresults(c)withoutoptimizationareacceptablebuthavealittlebiaswith
drawn sketches. Optimization started from the original latent (d), and without 𝐿𝑒𝑑𝑖𝑡(f) has limited effects, and removing 𝐿𝑖𝑚𝑔(e) changes unedited regions.
UtilizingPix2PixHD[Wangetal .2018]tocalculatesketchlosshassimilarresultsastheinitialpredictedresults,whileourapproachfurtherimprovesthe
faithfulness tosketches asseen in the hairpatternsin the firstrow and the shapeof glassesin the secondrow.
inthesecondrow.Weintroduceseverallosstermstoensurefaithful
andconsistenteditingeffects.Without Limg,thedetails,suchasthe
beardinthefirstrow,failtobepreserved.Without Ledittoguide
the desired shape, the optimized results do not follow the edited
sketches.
AsillustratedinFig.13,withoutthenovelspacesamplelossterm,
acceptable editing results are still achieved at the original views,
and the unedited regions are also well preserved. However, from
theperspectiveof3Dhumanfacesinsteadof2Dfacialimages,such
3D models are subject to substantial geometry changes, which can
bedetected from thefrontal views. Wealsotest thesketchgenera-
tion approach by replacing it with Pix2PixHD [Wang et al .2018] to
directly predict sketches from the rendered images. However, since
Pix2PixHD is quite heavy and not robust, the predicted sketches
have a relatively indirect connection with the underlying 3D hu-
man faces, making the optimized results have similar effects to the
initialpredictedones,asshowninFig.12.Incomparison,ourfullpart  of the  original  features  can  be  restored  with  the  optimization  
approach,  the  final  results  (c)&(g)  still  have  subtle  differences  from  
the  original  facial  NeRF  in unedited  regions,  such  as hair  details  and  
background  patterns.  Our  approach  can  well  preserve  the  original  
features  with  fewer  steps  compared  with  the  baseline  approaches,  
proving  the  effectiveness  of the  Mask  Fusion  module.
An  optimization  process  is included  to address  challenging  editing  
cases  and  enhance  the  correspondence  between  the  edited  sketches  
and  results  in terms  of details.  In Fig.  12,  it is obvious  that  without  
such  an  optimization  process,  the  encoded  results  are  acceptable  but  
differ  from  the  desired  sketches  in small  details,  such  as  the  area  of 
hair  in the  first  row,  and  the  shape  of eyeglasses  in the  second  row.  
However,  if we  remove  the  encoding  module,  i.e.,  by  directly  opti-
mizing  the  latent  codes  based  on  the  initial  latent  codes  and  edited  
sketches,  the  optimized  results  have  very  low  consistency  with  the  
desired  sketches  despite  long  optimization  steps.  For  example,  the  
hair  barely  changes  in the  first  row,  and  the  glasses  cannot  be  added

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:13
(a) Image
 (b) Sketch
 (c) w/o𝐿𝑠𝑝𝑎𝑐𝑒
 (d)Ours
 (e)Front Image
 (f) Front w/o 𝐿𝑠𝑝𝑎𝑐𝑒
 (g) Front Ours
Fig. 13. Ablation study of the space sample loss during optimization. In the 1st row, the double chins of a girl are removed. In the 2nd row, the nose bridge is
raised,andthehairbangsareadded.Themethodswithandwithout 𝐿𝑠𝑝𝑎𝑐𝑒bothgenerategoodresultsobservedfromtheeditingviews.However,when
observedfrom otherviews,theresultswithout 𝐿𝑠𝑝𝑎𝑐𝑒have undesirable changes in uneditedregions, such as thehair patterns in thefirst row andthe lefthair
in the secondrow.
Pix2PixHD UPDw/o𝐿𝑣Ours
Inconsistency ↓0.108 0.0840.0640.056
Table3. Consistencyevaluationof3Dsketches.Ourresultshavealower
inconsistencyscorethanthebaselineapproachesPix2PixHD[Wangetal .
2018] and UPD [Yi et al .2020], meaning that our sketches have the best
consistency across views.
optimizationsettingmakesupforthechallengingdetails,suchas
the height of hair and the shape of eyeglasses, while preserving the
uneditedregionsin 3Dspace.
We add a new sketch generation approach to synthesize 3D
sketches based on EG3D’s latent codes. As shown in Fig. 3, the
synthesized sketches are displayed in the UI system and rotated
withthesynthesizedfacialimagessimultaneously.So,sketchconsis-
tencyduringviewchangesaffectsusers’interactionexperience.We
replaceoursketchgenerationapproachwithtwoimage-to-sketch
translation methods, including Pix2PixHD and Unpaired Portrait
Drawing (UPD for short) [Yi et al .2020]. The regularization term
we used in the training stage cannot be applied to these methods
since they are not 3D-aware. A short-range consistency score is
measured as in [Huang et al .2021] to measure the inconsistency
duringviewchanges.Wedonotusealong-rangeconsistencyscore
since sketches are view-dependent and naturally vary with large
viewchanges.Werandomlygenerate30facestocalculatethemetric
andshowtheresultsinTable3.Itcanbeseenthatoursketcheshave
thebestviewconsistencycomparedwiththealternativeapproaches.
UPDgeneratessketcheswiththreedifferentstyles,sowereportthe
lowest value among them in Table3.
5.4 UserStudy
Giventheaboveextensivequalitativeandquantitativecomparisons,
wefinditbeneficialtoconductaperceptionstudytofullytestifyour
methodfromtheperspectiveofhumanviewers.Specifically,weeval-
uate our method on two tasks: facial generation from hand-drawn2D sketches, and facial editing by modifying the corresponding 2D
sketches.
For the facial generation, we compare our method against the
samesetof state-of-the-art methods in thequalitativecomparison
ofsketch-basedfacialNeRFgeneration,i.e.,PixelNeRF,DeepFaceEd-
iting(denotedasłDFEž),DeepFaceEditingfollowedbyNeRFprojec-
tion (denoted as łDFE-Projectionž), and pSp (denoted as łpSp-Refž).
Weprepare15casestocoverasmuchdiversity(suchasthedraw-
ingstyleandpersonalattributes,includingage,gender,hairstyle,
etc.) as possible, each of which consists of an input 2D hand-drawn
sketch,areferenceappearancefacialimageandthefacialimages
generatedbythecomparedmethods.Sincethesemethodsarenotall
3D-aware, we only display the results rendered from the viewpoint
oftheinputsketches,inrandomorder.However,itisnoteworthy
thatourmethodfurthersupports facerotationduetothe3D-aware
NeRF representation. Users are invited to rank the generated facial
images in order (the lower, the better) from: the perspective of real-
ism, geometry consistency with the input sketches, and appearance
consistencywiththereferenceappearanceimages.Thescoresare
obtained by averaging the received rankings for each method of
each case on each criterion. For each invited user, we randomly
select 5 cases from all the available cases to save his/her time. Thus,
we collect 5 ×3=15 answers from each user. In total, 39 people (28
malesand11femalesintheageof18 −40participatedinthisstudy.
Therefore, 39 ×15=585 answerswerecollected.
Fig. 14 (a) plots the statisticsof the evaluation results. Wefound
thesignificanteffectsforallthreecriteriathroughone-wayANOVA
tests: realism ( 𝐹(2,42)=80.33,𝑝<0.001), geometry consistency
(𝐹(2,42)=14.88,𝑝<0.001), and appearance consistency ( 𝐹(2,42)=
61.57,𝑝<0.001).Wealsoconductpairedt-teststoconfirmthesupe-
riorityintermsofgeometryconsistencyofourmethod(mean:2.00)
toPixelNeRF(mean:3.23;[ 𝑡=−6.42,𝑝<0.001]), DFE(mean:3.18;
[𝑡=−6.53,𝑝<0.001]), DFE-Projection (mean: 3.09; [ 𝑡=−6.13,𝑝<
0.001]), and pSp-Ref (mean: 3.48; [ 𝑡=−7.36,𝑝<0.001]). Besides its
superior performance in geometry consistency, our method is also
rated as one of the best in terms of realism, and it (mean: 1.87) is

1:14 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
(a) (b)Realism
Geometry Consistency
Appearance ConsistencyRealism
Retention
FaithfulnessPixelNeRF DFE DFE-
ProjectionpSp-Ref Ours
PixelNeRF DFE DFE-
ProjectionpSp-Ref Ours
PixelNeRF DFE DFE-
ProjectionpSp-Ref OursDeepPS DFE SketchEdit Ours
DeepPS DFE SketchEdit Ours
DeepPS DFE SketchEdit Ours
Fig. 14. Box plots of averaged perception rankings (the lower, the bet-
ter). (a) The comparisonof facial generation with five methods: PixelNeRF
[Yu et al.2021], DeepFaceEditing [Chen et al .2021] (denoted as łDFEž),
DeepFaceEditing-Projection(denotedasłDFE-Projectionž),pSp[Richard-
son et al.2021] (denoted as łpSp-Refž), and ours in terms of the realism,
geometryconsistency,andappearanceconsistency.(b)Thecomparisonof
facialeditingwithfourmethods:DeepPS[Yangetal .2020],DeepFaceEd-
iting [Chen et al .2021], SketchEdit [Zeng et al .2022], and ours in terms
ofrealism,retention,andfaithfulness.Theboxesaredrawnfromthefirst
quartile to the third quartile, with a middle horizontal line denoting the
median. The whiskers are the minimum and maximum values excluding
anyoutliers.
morepreferredthanPixelNeRF(mean:4.79;[ 𝑡=−17.46,𝑝<0.001]),
DFE (mean: 3.17; [ 𝑡=−6.21,𝑝<0.001]), and pSp-Ref (mean:
3.07; [𝑡=−11.77,𝑝<0.001]). In terms of appearance consis-
tency,it(mean:2.30)alsooutperformsPixelNeRF(mean:4.42;[ 𝑡=
−13.44,𝑝<0.001]) and pSp-Ref (mean: 3.74; [ 𝑡=−7.05,𝑝<0.001]).
However, since DFE-Projection projects the results back into the
latentspace ofour backbone, therealism of DFE-Projection (mean:
2.08;[𝑡=−1.00,𝑝=0.32])iscomparabletoours,asexpected.Dueto
thewell-disentangledpropertyofgeometryandappearanceofDFE,
the appearance consistency of DFE (mean: 2.28; [ 𝑡=0.11,𝑝=0.90])
and DFE-Projection (mean: 2.24; [ 𝑡=0.31,𝑝=0.75]) are again
comparable to ours.AstothefacialNeRFediting,wealsocompareourmethodagainst
thesamesetofstate-of-the-artmethodsinthequalitativecompar-
ison of sketch-based facial editing, i.e., DeepPS, DeepFaceEditing
(denotedasłDFEž),andSketchEdit.Weprepare15casestocover
varying personalattributes of the original identities, different edit-
ing regions (such as nose, hair, mouth, etc.), and different editing
angles (frontal or tilted). Each case consists of an original facial
image, a modified sketch where edited regions are emphasized, and
the edited facial images generated by each method. Again, we only
display the results from the viewpoints of the original facial images
since not all the compared methods are 3D-aware. For each invited
user,wealsorandomlyselect5casesfromalltheavailablecasesand
asktheusertoranktheeditedfacialimages(presentedinarandom
order) from the perspectiveof realism, retention of theunchanged
regions, and faithfulness to the edited sketches. Thus, we collect
5×3=15answers from eachuser.In total, 39people (28 malesand
11 females in the age of 18 −40 with normal vision) without any
special experience successfully participated in this study. Therefore
wecollected 39 ×15=585 answersin total.
Fig. 14 (b) plots the statistics of the evaluation results. We found
thesignificanteffectsforallthreecriteriathroughone-wayANOVA
tests: realism ( 𝐹(2,42)=70.49,𝑝<0.001), retention ( 𝐹(2,42)=
32.50,𝑝<0.001), and faithfulness ( 𝐹(2,42)=37.71,𝑝<0.001). We
also conduct paired t-tests to confirm the superiority in all three
criteria, i.e., realism, retention, and faithfulness in order, of our
method (mean: 1.22, 1.71, 1.55) over DeepPS (mean: 2.41, 2.35, 2.96;
[𝑡=−8.32,−4.33,−11.82,𝑝<0.001]), DFE (mean: 3.49, 3.24, 2.64;
[𝑡=−17.51,−12.69,−7.32,𝑝<0.001]),andSketchEdit(mean:2.86,
2.68, 2.83;[ 𝑡=−10.02,−5.76,−8.92,𝑝<0.001]).
6 APPLICATIONS
In this section, we propose two novel applications of our system,
namely,SemanticFacialNeRFEditingandLocalAppearanceCon-
trol.
Editing Propagation. As to the facial editing by sketches, since
wecarefullypreserveourgenerativepriorandachievethe editing
by modifying the latent codes, our system can be further utilized to
findeditingdirectionsforsemanticcontrols.Forexample,asshown
in Fig. 15, we close the mouth in thefirst row and shorten thehair
in the second row by editing the sketches. We subtract the latent
codes for the original facial images from the derived latent codes
for the edited facial images. Following this idea of latent vector
arithmetic to GANs [Radford et al .2016], the differences in the
latentspaceareviewed aseditingdirectionsfor closingthe mouth
and shortening the hair, and thus such editing directions can be
appliedtoothersmilingorlong-haircases.Itisclearthattheediting
directionsinferredbyoursystemcangeneralizetoothercaseswell,
as seen from (d) ∼(g). Another interesting phenomenon is that by
modifying the latent codes using our estimated semantic editing
directions, not only are unedited regions well-preserved with small
disturbances,suchasthoseonthebackgroundsbutalsothelighting
effects are changed correspondingly. Such effects are more evident
in thesecond caseof shorteningthe hair:the rightcheekbecomes
litsincetheocclusionisremovedby shorteningthe hair.

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:15
(a) Image
 (b) Sketch
 (c) Result
 (d)Face1
 (e)Propagation1
 (f) Face2
 (g) Propagation2
Fig.15. ResultsofEditingPropagation.In thefirstthreecolumns(from lefttoright),weshow theinputimages,editedsketches, andeditedresults.Mouth
closing and haircuttingare appliedby users.Itcanbeseen thatsimilarediting effectscanbepropagatedtoother persons.
(a) Image
 (b) Mask
 (c) Result1
 (d)Result2
Fig. 16. Resultsof localappearancecontrol.Given faces renderedin certain
views(a),users drawmaskstoindicatelocaleditingregions(b). Asshown
in (c) and (d), with the reference images on the top-left corner, the local
appearanceinhairandskinregionsischangedwhilethefeaturesinunedited
regionsaremaintained.
LocalAppearanceControl. Thankstothe3Dmaskestimationand
fusionstrategy,oursystemcanextendtheappearancecontrolby
referenceimagesfromglobalspacetolocalregions.Giventheorigi-
nal3Dface,asketchrepresentingitsfacialgeometryisrenderedby
thesketchgenerationapproach.Anewtri-planerepresentationthat
hasthesamegeometryastheoriginalfacebuthasanewappearance
isgeneratedbythe SketchTri-planePrediction netinSec.3.2.The
originalandnewtri-planefeaturesarefusedby3Dmasksestimated
fromthe2Dmasks 𝑀(drawnbyusers),similartothefunctionof
theMask Fusion module. The fused tri-plane feature is expected to
preservetheoriginalgeometryoftheentirefaceandappearanceon
untouchedregionsbutchangetheappearanceofthedrawnregions,
as shown in Fig. 16. During the optimization, we further utilize a
histogram lossto maintain theappearancefaithfulness:
L=Lℎ𝑖𝑠𝑡(H(𝐼′⊙𝑀),H(𝐼𝑟𝑒𝑓⊙𝑀))+
Lℎ𝑖𝑠𝑡(H(𝐼′⊙/tildewide𝑀),H(𝐼𝑔𝑒𝑜⊙/tildewide𝑀)), (12)
where H denotes the histogram features and Lℎ𝑖𝑠𝑡denotes the
featuredistanceasin[Afifietal .2021].𝐼𝑔𝑒𝑜,𝐼𝑟𝑒𝑓,and𝐼′representthe
originalimage,appearancereferenceimage,andgeneratedimage,
respectively, and /tildewide𝑀refers to unedited regions. As shown in Fig. 16,
local region appearance like hair and skin is modified effectively,
(a) Sketch
 (b) Result
 (c) Image
 (d)Sketch
 (e)Result
Fig.17. Failure cases.When the hand-drawnsketches (a) are tooabstract
and cartoonish, the generated faces(b) arestill of good qualitybut cannot
captureoverlyexaggeratedcharacteristics.Ourapproachalsocannothandle
uncommon personal accessories suchasthe hatin (e).
while the features in other regions are retained. Since the fusion
isconductedinthe3Dspace,ourresultscanfurtherberotatedto
other viewpointswith3Dconsistency.
7 CONCLUSIONS AND DISCUSSIONS
In this paper,wehavepresented the first novel sketch-based facial
NeRFgenerationandeditingmethod.The SketchTri-planePredic-
tionnet is designed to supplement the appearance and stereoscopic
informationinto2Dsketches,combinedwithapretrainedgenerator
tosynthesizehigh-qualityfacesNeRFs.Oursystemisrobustagainst
diverse drawing styles and allows appearance control. To preserve
unedited 3D regions during local editing, we further propose the
Mask Fusion module and a latent code optimization from sketch
strategy,whichcanbeperformedrepeatedlytosupport3D-aware
multi-step manipulations from different viewpoints. Our approach
outperforms existing sketch-based facial generation and editing ap-
proaches not only on faithfulness, and visual quality but also on 3D
view consistency. We also adapted our system for two applications:
semantic facialNeRF editingandlocalappearancecontrol.
Thankstothe SketchTri-planePrediction netandthepretrained
generator, our system is robust for hand-drawn sketches. However,
as shown in Fig. 17, when users draw too abstract or cartoonish
sketches,generated3Dfacesmightfailtocaptureoverlyexagger-
atedcharacteristics,thoughtheyarestillofgoodquality.Besides,
oursystemisdesignedtogenerate3Dfacesfromscratchwiththe
input of front-view sketches because it is hard for novice users

1:16 • Lin Gao,Feng-Lin Liu, Shu-Yu Chen,Kaiwen Jiang,Chunpeng Li, Yu-Kun Lai,andHongboFu
to draw free-view facial sketches, and the camera parameter pre-
dictionisverychallenging.Designingaspecificmethodtodetect
camera poses from hand-drawn sketches can partly solve this prob-
lem. Moreover, as shown in Fig. 17, our system cannot deal with
uncommon personal accessories such as hats since these examples
are rare in our training dataset. Designing a specific approach to
solvingthedataimbalanceoraugmentingthetrainingdatacould
alleviatethisproblem.Infuture,wewouldimplementtheSketch-
FaceNeRFinJittor[Huetal .2020],whichisafullyjust-in-time(JIT)
complieddeep framework.
EthicalDiscussion. Ourworkoriginatesfromandbenefitsposi-
tivereal-worldapplications,suchasdigitalcharacterdesign,virtual
meetings, and entertainment. However, the facial image genera-
tionandeditingworkshavelongsufferedfrompotentialharmful
abuses.Topreventmisuse,manyworks[Rossleretal .2019;Tolosana
et al.2020; Zhao et al .2021] in the fake detection community could
discriminate between the synthesized and real faces. Besides, the
generatedfree-viewfacialimagesofourmethodcanalsobeutilized
asa trainingdatasetto benefitthefakedetectionresearch.
ACKNOWLEDGMENTS
This work was supported by grants from the Beijing Municipal
NaturalScienceFoundationforDistinguishedYoungScholars(No.
JQ21013), the National Natural Science Foundation of China (No.
62061136007andNo.62102403),ChinaPostdoctoralScienceFoun-
dation(No.2022M713205),theResearchGrantsCounciloftheHong
KongSpecialAdministrativeRegion,China(No.CityU11212119),
Chow Sang Sang Group Research Fund (No. 9229119), and the Cen-
tre for Applied Computing and Interactive Media (ACIM) of School
of CreativeMedia, CityU.
REFERENCES
MahmoudAfifi,MarcusABrubaker,andMichaelSBrown.2021. Histogan:Controlling
colors of gan-generated and real images via color histograms. In Conference on
Computer Vision and Pattern Recognition .7941ś7950.
Autodesk,INC.2019. Maya. https:/autodesk.com/maya
PierreBénard,AaronHertzmann,etal .2019. Linedrawingsfrom3Dmodels:Atutorial.
FoundationsandTrends®in Computer Graphics and Vision 11, 1-2 (2019),1ś159.
AlexanderW.Bergman,PetrKellnhofer,WangYifan,EricR.Chan,DavidB.Lindell,
and Gordon Wetzstein. 2022. Generative Neural Articulated Radiance Fields. In
Advances in NeuralInformationProcessing Systems .
Mikolaj Binkowski, Danica J. Sutherland, Michael Arbel, and Arthur Gretton. 2018.
DemystifyingMMDGANs.In InternationalConferenceonLearningRepresentations .
John Canny. 1986. Acomputationalapproach toedge detection. IEEE Transactions on
pattern analysisand machineintelligence 6(1986),679ś698.
Eric R Chan, Connor Z Lin, Matthew A Chan, Koki Nagano, Boxiao Pan, Shalini
DeMello,OrazioGallo,LeonidasJGuibas,JonathanTremblay,SamehKhamis,etal .
2022. Efficient geometry-aware 3D generative adversarial networks. In Conference
on ComputerVision andPattern Recognition .16123ś16133.
Eric R Chan, Marco Monteiro, Petr Kellnhofer, Jiajun Wu, and Gordon Wetzstein.
2021. pi-gan: Periodic implicit generative adversarial networks for 3d-aware image
synthesis. In Conference on Computer Vision and Pattern Recognition .5799ś5809.
Shu-Yu Chen, Feng-LinLiu,Yu-Kun Lai,PaulL. Rosin,ChunpengLi, HongboFu, and
LinGao.2021. DeepFaceEditing:deepfacegenerationandeditingwithdisentangled
geometry and appearance control. ACM Trans. Graph. 40, 4, Article 90 (2021),
15pages.
Shu-Yu Chen, Wanchao Su, Lin Gao, Shihong Xia, and Hongbo Fu. 2020. DeepFace-
Drawing:Deepgeneration offaceimagesfromsketches. ACMTrans.Graph. 39,4,
Article 72(2020),16pages.
Yuedong Chen, Qianyi Wu, Chuanxia Zheng, Tat-Jen Cham, and Jianfei Cai. 2022.
Sem2NeRF:ConvertingSingle-ViewSemanticMaskstoNeuralRadianceFields.In
European Conference Computer Vision ,Vol. 13674.730ś748.Pei-Ze Chiang, Meng-Shiun Tsai, Hung-Yu Tseng, Wei-Sheng Lai, and Wei-Chen Chiu.
2022. Stylizing3D sceneviaimplicitrepresentationandHyperNetwork.In Winter
Conference on Applications of Computer Vision .1475ś1484.
Pinaki Nath Chowdhury, Tuanfeng Wang, Duygu Ceylan, Yi-Zhe Song, and Yulia
Gryaditskaya.2022. GarmentIdeation:IterativeView-AwareSketch-BasedGarment
Modeling. In InternationalConference on 3D Vision .22ś31.
Doug DeCarlo, Adam Finkelstein, Szymon Rusinkiewicz, and Anthony Santella. 2003.
Suggestive Contoursfor ConveyingShape. ACMTrans.Graph. 22, 3, 848ś855.
JiankangDeng,JiaGuo,NiannanXue,andStefanosZafeiriou.2019a. Arcface:Additive
angular margin loss for deep face recognition. In Conference on Computer Vision
and Pattern Recognition .4690ś4699.
YuDeng,JiaolongYang,JianfengXiang,andXinTong.2022. Gram:Generativeradiance
manifolds for 3d-aware image generation. In Conference on Computer Vision and
Pattern Recognition .10673ś10683.
Yu Deng, Jiaolong Yang, Sicheng Xu, Dong Chen, Yunde Jia, and Xin Tong. 2019b.
Accurate 3DFaceReconstructionWith Weakly-Supervised Learning:FromSingle
Image to Image Set. In Conference on Computer Vision and Pattern Recognition
Workshops .285ś295.
Dong Du, Xiaoguang Han, Hongbo Fu, Feiyang Wu, Yizhou Yu, Shuguang Cui, and
LigangLiu.2020. SAniHead:Sketchinganimal-like3Dcharacterheadsusingaview-
surface collaborative mesh generative network. IEEE Transactions on Visualization
and ComputerGraphics 28, 6(2020),2415ś2429.
LeonAGatys,AlexanderSEcker,andMatthiasBethge.2016. Imagestyletransferusing
convolutionalneuralnetworks.In WinterConferenceonApplicationsofComputer
Vision.2414ś2423.
JiataoGu,LingjieLiu,PengWang,andChristianTheobalt.2022. StyleNeRF:AStyle-
based3DAwareGeneratorforHigh-resolutionImageSynthesis.In International
Conference on Learning Representations .
Xiaoguang Han,ChangGao,and YizhouYu. 2017. DeepSketch2Face:a deep learning
based sketchingsystem for3D faceandcaricaturemodeling. ACM Trans. Graph. 36,
4, Article126 (2017),12pages.
Xiaoguang Han, Kangcheng Hou, Dong Du, Yuda Qiu, Shuguang Cui, Kun Zhou,
and Yizhou Yu. 2018. Caricatureshop: Personalized and photorealistic caricature
sketching. IEEETransactionsonVisualizationandComputerGraphics 26,7(2018),
2349ś2361.
Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp
Hochreiter. 2017. GANs trained by a two time-scale update rule converge to a local
Nashequilibrium.In AdvancesinNeuralInformationProcessingSystems .6626ś6637.
Shi-MinHu,DunLiang,Guo-YeYang,Guo-WeiYang,andWen-YangZhou.2020. Jittor:
a novel deep learning framework with meta-operators and unified graph execution.
Science ChinaInformation Sciences 63, 222103(2020),1ś21.
Shi-Min Hu, Fang-Lue Zhang, Miao Wang, Ralph R. Martin, and Jue Wang. 2013.
PatchNet:APatch-BasedImageRepresentationforInteractiveLibrary-DrivenImage
Editing.ACMTrans.Graph. 32, 6, Article 196 (2013),12pages.
Hsin-PingHuang,Hung-YuTseng,SaurabhSaini,ManeeshSingh,andMing-Hsuan
Yang.2021. LearningtoStylizeNovelViews.In InternationalConferenceonComputer
Vision.13849ś13858.
XunHuangandSergeBelongie.2017. Arbitrarystyletransferinreal-timewithadaptive
instancenormalization.In ConferenceonComputerVisionandPatternRecognition .
1501ś1510.
XinHuang,DongLiang,HongruiCai,JuyongZhang,andJinyuanJia.2022b.CariPainter:
Sketch Guided Interactive Caricature Generation. In ACM International Conference
on Multimedia .1232ś1240.
Yi-Hua Huang, Yue He, Yu-Jie Yuan, Yu-Kun Lai, and Lin Gao. 2022a. StylizedNeRF:
consistent 3D scene stylization as stylized NeRF via 2D-3D mutual learning. In
Conference on Computer Vision and Pattern Recognition .18342ś18352.
KaiwenJiang,Shu-YuChen,Feng-LinLiu,HongboFu,andLinGao.2022. NeRFFaceEd-
iting: Disentangled Face Editing in Neural Radiance Fields. In SIGGRAPH Asia 2022
Conference Papers .Association for ComputingMachinery,Article 31, 9pages.
Kyungmin Jo, Gyumin Shim, Sanghun Jung, Soyoung Yang, and Jaegul Choo. 2021. Cg-
nerf: Conditional generative neural radiance fields. arXiv preprint arXiv:2112.03517
(2021).
Youngjoo Jo and Jongyoul Park. 2019. Sc-fegan: Face editing generative adversarial
network with user’s sketch and color. In Conference on Computer Vision and Pattern
Recognition .1745ś1753.
TilkeJudd,FrédoDurand,andEdwardAdelson.2007.ApparentRidgesforLineDrawing.
InACMTrans.Graph. ,Vol. 26. 19.
TeroKarras,Samuli Laine,andTimoAila.2019. Astyle-basedgeneratorarchitecture
for generative adversarial networks.In Conference on Computer Vision and Pattern
Recognition .4401ś4410.
TeroKarras,SamuliLaine,MiikaAittala,JanneHellsten,JaakkoLehtinen,andTimo
Aila.2020. Analyzingandimprovingtheimagequalityofstylegan.In Conference
on ComputerVision andPattern Recognition .8110ś8119.
Diederik PKingma andJimmy Ba. 2014. Adam: A methodfor stochasticoptimization.
arXivpreprintarXiv:1412.6980 (2014).

SketchFaceNeRF: Sketch-based Facial Generation andEditing inNeural RadianceFields • 1:17
Tianye Li, Timo Bolkart, Michael.J. Black, HaoLi, and JavierRomero. 2017. Learning
a model of facial shape and expression from 4D scans. ACM Trans. Graph. 36, 6,
Article 194 (2017),17pages.
YuhangLi, Xuejin Chen,Feng Wu, andZheng-JunZha. 2019. Linestofacephoto: Face
photo generation from lines with conditional self-attention generative adversarial
networks. In ACMInternationalConference on Multimedia .2323ś2331.
Yuhang Li, Xuejin Chen, Binxin Yang, Zihan Chen, Zhihua Cheng, and Zheng-Jun
Zha.2020. Deepfacepencil:Creatingfaceimagesfromfreehandsketches.In ACM
InternationalConference on Multimedia .991ś999.
Jingwang Ling, Zhibo Wang, Ming Lu, Quan Wang, Chen Qian, and Feng Xu. 2022.
Structure-Aware Editable Morphable Model for 3D Facial Detail Animation and
Manipulation.In EuropeanConference on Computer Vision .249ś267.
Difan Liu, Mohamed Nabail, Aaron Hertzmann, and Evangelos Kalogerakis. 2020.
Neuralcontours:Learningtodrawlinesfrom3dshapes.In WinterConferenceon
Applications of Computer Vision .5428ś5436.
Feng-Lin Liu, Shu-Yu Chen, Yu-Kun Lai, Chunpeng Li, Yue-Ren Jiang, Hongbo Fu, and
Lin Gao. 2022. DeepFaceVideoEditing: sketch-based deep editing of face videos.
ACMTrans.Graph. 41, 4, Article 167 (2022),16pages.
HongyuLiu,ZiyuWan,WeiHuang,YibingSong,XintongHan,JingLiao,BinJiang,and
Wei Liu. 2021. DeFLOCNet: Deep Image Editing via Flexible Low-Level Controls. In
Conference on Computer Vision and Pattern Recognition .10765ś10774.
WilliamE.LorensenandHarveyE.Cline.1987. MarchingCubes:AHighResolution
3DSurfaceConstructionAlgorithm. SIGGRAPHComput.Graph. 21,4(aug1987),
163ś169.
Zhongjin Luo, Jie Zhou, Heming Zhu, Dong Du, Xiaoguang Han, and Hongbo Fu.
2021. SimpModeling: Sketching Implicit Field to Guide Mesh Modeling for 3D
Animalmorphic Head Design. In ACM Symposium on User Interface Software and
Technology .854ś863.
Ben Mildenhall, Pratul P Srinivasan, Matthew Tancik, Jonathan T Barron, Ravi Ra-
mamoorthi,andRenNg.2021. Nerf:Representingscenesasneuralradiancefields
for view synthesis. Commun. ACM 65, 1(2021),99ś106.
Thu Nguyen-Phuoc, Feng Liu, and Lei Xiao. 2022. SNeRF: Stylized Neural Implicit
Representationsfor3DScenes. ACMTrans.Graph. 41,4,Article142(2022),11pages.
Michael Niemeyer and Andreas Geiger. 2021. Giraffe: Representing scenes as composi-
tionalgenerativeneuralfeaturefields.In ConferenceonComputerVisionandPattern
Recognition .11453ś11464.
NVIDIA. 2023. NVIDIAOmniverse . https://www.nvidia.com/en-us/omniverse/
Yutaka Ohtake, Alexander Belyaev, and Hans-Peter Seidel. 2004. Ridge-valley lines on
meshes via implicit surfacefitting. Vol.23. 609ś612.
Roy Or-El, Xuan Luo, Mengyi Shan, Eli Shechtman, Jeong Joon Park, and Ira
Kemelmacher-Shlizerman. 2022. Stylesdf: High-resolution 3d-consistent image
andgeometrygeneration.In ConferenceonComputerVisionandPatternRecognition .
13503ś13513.
EthanPerez,FlorianStrub,HarmDeVries,VincentDumoulin,andAaronCourville.
2018. Film: Visual reasoning with a general conditioning layer. In Proceedings of the
AAAIConference on ArtificialIntelligence ,Vol. 32.
Pixologic.2023. ZBrush. http://pixologic.com/features/about-zbrush.php
TizianoPortenier,QiyangHu,AttilaSzabó,SiavashArjomandBigdeli,PaoloFavaro,
and Matthias Zwicker. 2018. Faceshop: deep sketch-based face image editing. ACM
Trans.Graph. 37, 4, Article 99(2018),13pages.
AlecRadford,LukeMetz,andSoumithChintala.2016. UnsupervisedRepresentation
LearningwithDeepConvolutionalGenerativeAdversarialNetworks.In Interna-
tional Conference on Learning Representations .
EladRichardson, YuvalAlaluf,OrPatashnik,YotamNitzan, YanivAzar,StavShapiro,
andDanielCohen-Or.2021.Encodinginstyle:astyleganencoderforimage-to-image
translation. In Conference on Computer Vision and Pattern Recognition .2287ś2296.
Andreas Rossler, Davide Cozzolino, Luisa Verdoliva, Christian Riess, Justus Thies, and
Matthias Nießner. 2019. Faceforensics++: Learning to detect manipulated facial
images. In InternationalConference on Computer Vision .1ś11.
KatjaSchwarz,YiyiLiao,MichaelNiemeyer,andAndreasGeiger.2020. Graf:Generative
radiance fields for 3d-aware image synthesis. Advances in Neural Information
Processing Systems 33(2020),20154ś20166.
Tianchang Shen, Jun Gao, Kangxue Yin, Ming-Yu Liu, and Sanja Fidler. 2021. Deep
marchingtetrahedra:ahybridrepresentationforhigh-resolution3dshapesynthesis.
Advances in NeuralInformationProcessing Systems (2021),6087ś6101.
Vincent Sitzmann, Julien Martel, Alexander Bergman, David Lindell, and Gordon
Wetzstein. 2020. Implicit neural representations with periodic activation functions.
Advances in NeuralInformationProcessing Systems 33(2020),7462ś7473.
WanchaoSu,HuiYe,Shu-YuChen,LinGao,andHongboFu.2022. DrawingInStyles:
Portrait Image Generation and Editing with Spatially Conditioned StyleGAN. IEEE
Transactions on Visualization andComputer Graphics (2022).
Jingxiang Sun, Xuan Wang, Yichun Shi, Lizhen Wang, Jue Wang, and Yebin Liu. 2022a.
IDE-3D:InteractiveDisentangledEditingforHigh-Resolution3D-AwarePortrait
Synthesis. ACMTrans.Graph. 41, 6, Article 270 (2022),10pages.
JingxiangSun,XuanWang,LizhenWang,XiaoyuLi,YongZhang,HongwenZhang,and
YebinLiu.2022b. Next3D:GenerativeNeuralTextureRasterizationfor3D-AwareHead Avatars. arXivpreprintarXiv:2211.11208 (2022).
JingxiangSun,XuanWang,YongZhang,XiaoyuLi,QiZhang,YebinLiu,andJueWang.
2022c. Fenerf: Face editing in neural radiance fields. In Conference on Computer
Vision andPattern Recognition .7672ś7682.
JunshuTang,BoZhang,BinxinYang,TingZhang,DongChen,LizhuangMa,andFang
Wen.2022. ExplicitlyControllable3D-AwarePortraitGeneration. arXivpreprint
arXiv:2209.05434 (2022).
RubenTolosana,RubenVera-Rodriguez,JulianFierrez,AythamiMorales,andJavier
Ortega-Garcia. 2020. Deepfakes and beyond: A survey of face manipulation and
fake detection. Information Fusion 64(2020),131ś148.
YaelVinker,EhsanPajouheshgar,JessicaY.Bo,RomanChristianBachmann,AmitHaim
Bermano, Daniel Cohen-Or, Amir Zamir, and Ariel Shamir. 2022. CLIPasso:
Semantically-AwareObjectSketching. ACMTrans.Graph. 41,4,Article86(2022),
11pages.
Ting-Chun Wang, Ming-Yu Liu, Jun-Yan Zhu, Andrew Tao, Jan Kautz, and Bryan
Catanzaro. 2018. High-resolution image synthesis and semantic manipulation with
conditionalgans.In ConferenceonComputerVisionandPatternRecognition .8798ś
8807.
Yue Wu, Yu Deng, Jiaolong Yang, Fangyun Wei, Chen Qifeng, and Xin Tong. 2022.
AniFaceGAN: Animatable 3D-Aware Face Image Generationfor Video Avatars.In
Advances in NeuralInformationProcessing Systems .
SainingXieandZhuowenTu.2015. Holistically-NestedEdgeDetection.In International
Conference on Computer Vision .1395ś1403.
Li Yang, Jing Wu, Jing Huo, Yu-Kun Lai, and Yang Gao. 2021b. Learning 3D face
reconstruction froma singlesketch. GraphicalModels 115 (2021),101102.
Shuai Yang, Zhangyang Wang, Jiaying Liu, and Zongming Guo. 2020. Deep plastic
surgery: Robust and controllable image editing with human-drawn sketches. In
European Conference on ComputerVision .601ś617.
ShuaiYang,ZhangyangWang,JiayingLiu,andZongmingGuo.2021a. Controllable
sketch-to-imagetranslationforrobustfacesynthesis. IEEETransactionsonImage
Processing 30(2021),8797ś8810.
Ran Yi, Yong-Jin Liu, Yu-Kun Lai, and PaulL. Rosin. 2020. Unpaired Portrait Drawing
Generation via Asymmetric Cycle Mapping.In Conference on Computer Vision and
Pattern Recognition .8214ś8222.
RanYi,Yong-JinLiu,Yu-KunLai,andPaulLRosin.2019. APDrawingGAN:Generating
artistic portrait drawings from face photos with hierarchical gans. In Conference on
Computer Vision and Pattern Recognition .10743ś10752.
AlexYu,VickieYe,MatthewTancik,andAngjooKanazawa.2021. pixelnerf:Neural
radiancefieldsfromoneorfewimages.In ConferenceonComputerVisionandPattern
Recognition .4578ś4587.
Jiahui Yu, Zhe Lin, Jimei Yang, Xiaohui Shen, Xin Lu, and Thomas S Huang. 2019.
Free-form image inpainting with gated convolution. In Conference on Computer
Vision andPattern Recognition .4471ś4480.
YuZeng,ZheLin,andVishalMPatel.2022. Sketchedit:Mask-freelocalimagemanipula-
tionwithpartialsketches.In ConferenceonComputerVisionandPatternRecognition .
5951ś5961.
Kai Zhang, Nicholas I. Kolkin, Sai Bi, Fujun Luan, Zexiang Xu, Eli Shechtman, and
NoahSnavely.2022. ARF:ArtisticRadianceFields.In EuropeanConferenceComputer
Vision,Vol. 13691.717ś733.
RichardZhang,PhillipIsola,AlexeiAEfros,EliShechtman,andOliverWang.2018. The
unreasonable effectiveness of deep features as a perceptual metric. In Conference on
Computer Vision and Pattern Recognition .586ś595.
Hanqing Zhao, Wenbo Zhou, Dongdong Chen, Tianyi Wei, Weiming Zhang, and Neng-
haiYu.2021. Multi-attentionaldeepfakedetection.In ConferenceonComputerVision
and Pattern Recognition .2185ś2194.