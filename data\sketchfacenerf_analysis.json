{"extracted_information": "SketchFaceNeRF es un método novedoso para la generación y edición facial 3D basada en bocetos en campos de radiancia neural (NeRFs). Permite generar imágenes fotorrealistas de vista libre a partir de bocetos 2D y manipular caras en espacio 3D. El método aborda desafíos como la escasez de información 3D en bocetos 2D, introduciendo una red de predicción de triplanos y un módulo de fusión de máscaras. Requiere hardware específico y dependencias de software para su ejecución.", "specifications": {"hardware": "GPU NVIDIA 3090Ti o posterior (el desarrollo y pruebas se realizaron en 3090Ti)", "platform": "Linux"}, "pricing": {}, "features": [{"name": "Generación facial 3D basada en bocetos", "description": "Genera caras 3D realistas en campos de radiancia neural (NeRFs) a partir de bocetos 2D."}, {"name": "Edición facial 3D basada en bocetos", "description": "Permite manipular componentes faciales en el espacio 3D utilizando bocetos como guía."}, {"name": "Generación de imágenes de vista libre", "description": "Produce imágenes fotorrealistas desde diferentes ángulos de visión."}, {"name": "Manipulación flexible en espacio 3D", "description": "Permite a los usuarios manipular caras desde diferentes puntos de vista en 3D."}], "statistics": {}, "temporal_info": {"publication_year": "2023 (ACM Transactions on Graphics)", "last_commit_date": "3 de agosto de 2023"}, "geographical_data": {}, "references": [{"type": "paper", "title": "SketchFaceNeRF: Sketch-Based Facial Generation and Editing in Neural Radiance Fields", "authors": ["<PERSON>, Lin", "<PERSON>, <PERSON><PERSON><PERSON>", "<PERSON>, Shu<PERSON>Yu", "Jiang, Kaiwen", "Li, Chun<PERSON>", "Lai, Yu-Kun", "Fu, Hongbo"], "journal": "ACM Transactions on Graphics (Proceedings of ACM SIGGRAPH 2023)", "year": "2023", "volume": "42", "pages": "159:1--159:17", "number": "4"}, {"type": "code_repository", "name": "EG3D", "url": "https://github.com/NVlabs/eg3d"}, {"type": "code_repository", "name": "Deep3DRecon", "url": "https://github.com/sicxu/Deep3DFaceRecon_pytorch"}, {"type": "code_repository", "name": "pSp", "url": "https://github.com/eladrich/pixel2style2pixel"}, {"type": "code_repository", "name": "face-parsing.PyTorch", "url": "https://github.com/zllrunning/face-parsing.PyTorch/tree/master"}, {"type": "code_repository", "name": "pixel_nerf", "url": "https://github.com/sxyu/pixel-nerf"}], "dependencies": [{"type": "software", "name": "CUDA toolkit", "version": "11.3 o posterior"}, {"type": "software", "name": "Python libraries", "details": "Listado en environment.yml"}, {"type": "pretrained_model", "name": "vgg16.pt", "location": "./checkpoints"}, {"type": "pretrained_model", "name": "79999_iter.pth", "location": "./parsing_model/checkpoint/"}, {"type": "pretrained_model", "name": "BFM directory", "source": "Deep3DFaceRecon_pytorch", "location": "./"}], "installation_process": ["Clonar el repositorio.", "Crear y activar un entorno Conda usando 'conda env create -f environment.yml' y 'conda activate skNerf'.", "Descargar los modelos preentrenados ('vgg16.pt', '79999_iter.pth', directorio 'BFM') y colocarlos en las rutas especificadas ('./checkpoints', './parsing_model/checkpoint/', './' respectivamente)."], "architecture": {"base": "Neural Radiance Fields (NeRFs)", "components": [{"name": "Sketch Tri-plane Prediction net", "description": "Inyecta apariencia en bocetos y genera características para triplanos 3D."}, {"name": "Triplanos 3D", "description": "Formato compacto para suplementar información 3D ausente en bocetos."}, {"name": "Mask Fusion module", "description": "Transforma máscaras 2D de vista libre (de operaciones de edición) en máscaras 3D en el espacio de triplanos para guiar la fusión."}, {"name": "Optimization approach", "description": "Con una novedosa pérdida espacial para mejorar la retención de identidad y la fidelidad de la edición."}]}, "limitations": "El contenido proporcionado no lista explícitamente las limitaciones del método SketchFaceNeRF, aunque describe los desafíos que busca abordar (incertidumbre 3D desde 2D, escasez de bocetos, inconsistencias en edición multivista)."}