{"sketch to face generation deep learning": [{"title": "Deep generation of face images from sketches", "snippet": "… novel deep learning framework for sketch-based face image … five feature descriptors from the face sketch data, namely, for “… deep learning sub-network for conditional image generation, …", "link": "https://arxiv.org/abs/2006.01047", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Fu - arXiv preprint arXiv:2006.01047, 2020 - arxiv.org", "year": 2006, "citedBy": 195, "pdfUrl": "https://arxiv.org/pdf/2006.01047"}, {"title": "Face sketch to photo translation using generative adversarial networks", "snippet": "… model to synthesize high-quality natural face … sketch. We train a network to map the facial features extracted from the input sketch to a vector in the latent space of the face generating …", "link": "https://arxiv.org/abs/2110.12290", "publicationInfo": "<PERSON><PERSON>, MS Fard, A Nickabadi - arXiv preprint arXiv:2110.12290, 2021 - arxiv.org", "year": 2110, "citedBy": 10, "pdfUrl": "https://arxiv.org/pdf/2110.12290"}, {"title": "Picture that sketch: Photorealistic image generation from abstract sketches", "snippet": "… best to deal with the abstraction gap between sketch and … in learning to generate from partial sketches, we introduce a smart augmentation strategy, where, we partially render the sketch …", "link": "http://openaccess.thecvf.com/content/CVPR2023/html/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> <PERSON> - Proceedings of the …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 53, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2023/papers/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.pdf"}, {"title": "A decision support system for face sketch synthesis using deep learning and artificial intelligence", "snippet": "… deep learning together for face sketch synthesis. Convolutional neural networks (CNNs) and other constructs of deep … [47] proposed a model to generate sketches from training photos …", "link": "https://www.mdpi.com/1424-8220/21/24/8178", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, H<PERSON> Yong - Sensors, 2021 - mdpi.com", "year": 2021, "citedBy": 13, "pdfUrl": "https://www.mdpi.com/1424-8220/21/24/8178/pdf"}, {"title": "Toward realistic face photo–sketch synthesis via composition-aided GANs", "snippet": "… of face sketch/photo and design a novel generator architecture … in the loop of learning a face photo–sketch synthesis model. 2) … Given a face photo X, we would like to generate a sketch …", "link": "https://ieeexplore.ieee.org/abstract/document/9025751/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE transactions on …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 143, "pdfUrl": "https://arxiv.org/pdf/1712.00899"}, {"title": "Deep learning for free-hand sketch: A survey", "snippet": "… sketch research and applications. This paper presents a comprehensive survey of the deep learning techniques oriented at freehand sketch … the existing deep sketch-photo generation …", "link": "https://ieeexplore.ieee.org/abstract/document/9706366/", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>… - … and machine …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 175, "pdfUrl": "https://arxiv.org/pdf/2001.02600"}, {"title": "Identity-aware CycleGAN for face photo-sketch synthesis and recognition", "snippet": "… to supervise the image generation network. It improves CycleGAN on photo-sketch synthesis by … Extensive experiments are performed on both photo-to-sketch and sketch-to-photo tasks …", "link": "https://www.sciencedirect.com/science/article/pii/S0031320320300558", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> Recognition, 2020 - Elsevier", "year": 2020, "citedBy": 137, "pdfUrl": "https://arxiv.org/pdf/2103.16019"}, {"title": "Sketch less for more: On-the-fly fine-grained sketch-based image retrieval", "snippet": "… We train the agent (sketchbranch) to deal with partial sketches. In this stage we finetune the … the complete episode of progressive sketch generation before updating the weights, thus …", "link": "http://openaccess.thecvf.com/content_CVPR_2020/html/Bhun<PERSON>_Sketch_Less_for_More_On-the-Fly_Fine-Grained_Sketch-Based_Image_Retrieval_CVPR_2020_paper", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON> - Proceedings of the …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 136, "pdfUrl": "http://openaccess.thecvf.com/content_CVPR_2020/papers/Bhunia_Sketch_Less_for_More_On-the-Fly_Fine-Grained_Sketch-Based_Image_Retrieval_CVPR_2020_paper.pdf"}, {"title": "Sketch your own gan", "snippet": "… We achieve it using a sketching interface and our cross-domain fine-tuning method. Similar to our … Our method overcomes the large domain gap between user sketches and generator …", "link": "http://openaccess.thecvf.com/content/ICCV2021/html/<PERSON>_Sketch_Your_Own_GAN_ICCV_2021_paper.html", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON> - Proceedings of the IEEE/CVF …, 2021 - openaccess.thecvf.com", "year": 2021, "citedBy": 92, "pdfUrl": "https://openaccess.thecvf.com/content/ICCV2021/papers/<PERSON>_Sketch_Your_Own_GAN_ICCV_2021_paper.pdf"}, {"title": "<PERSON><PERSON>: Text-guided diverse face image generation and manipulation", "snippet": "… Our proposed method can generate diverse and high-quality … multi-modal inputs, such as sketches or semantic labels with or … : Generating artistic portrait drawings from face photos with …", "link": "http://openaccess.thecvf.com/content/CVPR2021/html/<PERSON><PERSON>_TediGAN_Text-Guided_Diverse_Face_Image_Generation_and_Manipulation_CVPR_2021_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> - Proceedings of the IEEE …, 2021 - openaccess.thecvf.com", "year": 2021, "citedBy": 484, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2021/papers/Xia_TediGAN_Text-Guided_Diverse_Face_Image_Generation_and_Manipulation_CVPR_2021_paper.pdf"}, {"title": "Unsupervised sketch to photo synthesis", "snippet": "… To synthesize a photo from a sketch, we deal with these two aspects at separate stages: We first translate a … We generate distractive sketches by adding a random patch from a different …", "link": "https://link.springer.com/chapter/10.1007/978-3-030-58580-8_3", "publicationInfo": "<PERSON>, <PERSON>, S<PERSON> Yu - Computer Vision–ECCV 2020: 16th European …, 2020 - Springer", "year": 2020, "citedBy": 65, "pdfUrl": "https://arxiv.org/pdf/1909.08313"}, {"title": "Generative adversarial networks for face generation: A survey", "snippet": "… facial information helps the network remove geometrical artifacts in the face sketch and improve face quality and recognition rate. The application of cycle consistency, in addition to the …", "link": "https://dl.acm.org/doi/abs/10.1145/3527850", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - ACM Computing …, 2022 - dl.acm.org", "year": 2022, "citedBy": 173, "pdfUrl": "https://www.academia.edu/download/89946093/1122445.pdf"}, {"title": "Collaborative diffusion for multi-modal face generation and editing", "snippet": "… drawing the face shape (mask-driven). In this work, we present Collaborative Diffusion, where pre-trained uni-modal diffusion models collaborate to achieve multi-modal face generation …", "link": "http://openaccess.thecvf.com/content/CVPR2023/html/Huang_Collaborative_Diffusion_for_Multi-Modal_Face_Generation_and_Editing_CVPR_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON>… - Proceedings of the IEEE …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 159, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2023/papers/Huang_Collaborative_Diffusion_for_Multi-Modal_Face_Generation_and_Editing_CVPR_2023_paper.pdf"}, {"title": "Recent advances in deep learning techniques for face recognition", "snippet": "… Datasets are an essential factor in a machine learning system. DL … In conclusion, recent Deep Learning-based face recognition … Face aging technique to generate a future face in old age …", "link": "https://ieeexplore.ieee.org/abstract/document/9478893/", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, MAR Iftee, <PERSON> - IEEE …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 109, "pdfUrl": "https://ieeexplore.ieee.org/iel7/6287639/6514899/09478893.pdf"}, {"title": "Clipasso: Semantically-aware object sketching", "snippet": "… In contrast, we present an optimizationbased photo-to-sketch generation technique that … In each row, we show seven distinct sketches generated from a single input face, along with …", "link": "https://dl.acm.org/doi/abs/10.1145/3528223.3530068", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON> - ACM Transactions on …, 2022 - dl.acm.org", "year": 2022, "citedBy": 243, "pdfUrl": "https://arxiv.org/pdf/2202.05822"}], "facial sketch to photo synthesis": [{"title": "Denoising diffusion probabilistic model for face sketch-to-photo synthesis", "snippet": "… photographic face images from sketches. Nevertheless, challenges remain in synthesizing facial … This paper introduces a novel architecture for face sketch-to-photo synthesis, using …", "link": "https://ieeexplore.ieee.org/abstract/document/10547051/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - IEEE Transactions on …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 10, "pdfUrl": null}, {"title": "An identity-preserved model for face sketch-photo synthesis", "snippet": "… of the original sketch in translation, … face sketch databases with face recognition. The results demonstrate our translation method is superior to the existing methods in maintaining face …", "link": "https://ieeexplore.ieee.org/abstract/document/9126135/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> Cheng - IEEE Signal Processing Letters, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 33, "pdfUrl": null}, {"title": "Few-shot face sketch-to-photo synthesis via global-local asymmetric image-to-image translation", "snippet": "… face sketch-to-photo synthesis. To tackle this challenge, we propose a few-shot face sketch-to-photo synthesis … translation framework, where the sketch-to-photo process uses a …", "link": "https://dl.acm.org/doi/abs/10.1145/3672400", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - ACM Transactions on Multimedia …, 2024 - dl.acm.org", "year": 2024, "citedBy": 1, "pdfUrl": null}, {"title": "Colorizing Face Sketch Images for Face Photo Synthesis", "snippet": "… For the difficult task of facial photo-sketch synthesis, the … In this study, we convert face sketches into photo images … image synthesis studies that use a dataset of sketch-photo pairs. An …", "link": "https://ieeexplore.ieee.org/abstract/document/10182157/", "publicationInfo": "<PERSON><PERSON> <PERSON>, AM Myat - 2023 IEEE Conference on Computer …, 2023 - ieeexplore.ieee.org", "year": 2023, "citedBy": 2, "pdfUrl": null}, {"title": "Toward realistic face photo–sketch synthesis via composition-aided GANs", "snippet": "… The proposed method is capable of handling both sketch synthesis and photo synthesis, because these two procedures are symmetric. In this section, we take face sketch synthesis as …", "link": "https://ieeexplore.ieee.org/abstract/document/9025751/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE transactions on …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 143, "pdfUrl": "https://arxiv.org/pdf/1712.00899"}, {"title": "Unsupervised sketch to photo synthesis", "snippet": "… in sketch to photo synthesis when both shape and color translations are needed simultaneously. We consider learning sketch to photo synthesis from sketches … individual sketches and …", "link": "https://link.springer.com/chapter/10.1007/978-3-030-58580-8_3", "publicationInfo": "<PERSON>, <PERSON>, S<PERSON> Yu - Computer Vision–ECCV 2020: 16th European …, 2020 - Springer", "year": 2020, "citedBy": 65, "pdfUrl": "https://arxiv.org/pdf/1909.08313"}, {"title": "Face sketch to photo translation using generative adversarial networks", "snippet": "… In this paper, we proposed a novel sketch to photo synthesis framework based on GANs. Unlike most of the existing methods, the proposed method requires no paired data and no …", "link": "https://arxiv.org/abs/2110.12290", "publicationInfo": "<PERSON><PERSON>, MS Fard, A Nickabadi - arXiv preprint arXiv:2110.12290, 2021 - arxiv.org", "year": 2110, "citedBy": 10, "pdfUrl": "https://arxiv.org/pdf/2110.12290"}, {"title": "Controllable face sketch-photo synthesis with flexible generative priors", "snippet": "… The contributions of this work are as follows: 1) We address the limitations of existing methods for face sketchphoto synthesis, specifically in handling diverse datasets and the challenge …", "link": "https://dl.acm.org/doi/abs/10.1145/3581783.3611834", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the 31st …, 2023 - dl.acm.org", "year": 2023, "citedBy": 4, "pdfUrl": null}, {"title": "Quality guided sketch-to-photo image synthesis", "snippet": "… • We adopt a single generator for the task of sketchto-photo synthesis which generates a group of … High-fidelity face sketch-to-photo synthesis using generative adversarial network. 2019 …", "link": "http://openaccess.thecvf.com/content_CVPRW_2020/html/w48/Osahor_Quality_Guided_Sketch-to-Photo_Image_Synthesis_CVPRW_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 25, "pdfUrl": "http://openaccess.thecvf.com/content_CVPRW_2020/papers/w48/Osahor_Quality_Guided_Sketch-to-Photo_Image_Synthesis_CVPRW_2020_paper.pdf"}, {"title": "Unsupervised scene sketch to photo synthesis", "snippet": "… DeepFaceDrawing [2] enables user to sketch progressive for face image synthesis. Our work differs in that we allow users to directly edit strokes of a complicated scene …", "link": "https://link.springer.com/chapter/10.1007/978-3-031-25063-7_17", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - European Conference on …, 2022 - Springer", "year": 2022, "citedBy": 7, "pdfUrl": "https://arxiv.org/pdf/2209.02834"}, {"title": "Face photo-sketch synthesis via full-scale identity supervision", "snippet": "… Considering the face image translation is also a type of face image re-representation, … face recognition models to improve the synthesis performance. First, we applied existing synthesis …", "link": "https://www.sciencedirect.com/science/article/pii/S0031320321006221", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Gao - Pattern Recognition, 2022 - Elsevier", "year": 2022, "citedBy": 31, "pdfUrl": null}, {"title": "Identity-aware CycleGAN for face photo-sketch synthesis and recognition", "snippet": "… on photo-sketch synthesis by paying more attention to the synthesis of key facial regions, … Furthermore, we develop a mutual optimization procedure between the synthesis model and …", "link": "https://www.sciencedirect.com/science/article/pii/S0031320320300558", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> Recognition, 2020 - Elsevier", "year": 2020, "citedBy": 137, "pdfUrl": "https://arxiv.org/pdf/2103.16019"}, {"title": "Face photo–sketch synthesis via intra-domain enhancement", "snippet": "… -domain face generation stage, we apply a probabilistic graphical model which is capable of synthesizing the coarse face sketch from photos with low quality. In the intra-domain face …", "link": "https://www.sciencedirect.com/science/article/pii/S0950705122011194", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, X Gao - Knowledge-Based Systems, 2023 - Elsevier", "year": 2023, "citedBy": 10, "pdfUrl": null}, {"title": "A decision support system for face sketch synthesis using deep learning and artificial intelligence", "snippet": "… called dual transfer face sketch-photo synthesis (FSPS). It is … a sketch from the training pairs of photo-viewed sketches. <PERSON> … face-sketch formulation involving the identity of each subject …", "link": "https://www.mdpi.com/1424-8220/21/24/8178", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, H<PERSON> Yong - Sensors, 2021 - mdpi.com", "year": 2021, "citedBy": 13, "pdfUrl": "https://www.mdpi.com/1424-8220/21/24/8178/pdf"}, {"title": "Knowledge distillation for face photo–sketch synthesis", "snippet": "… [31] extended this work by proposing a multiple-representation-based face sketch–photo synthesis method that extracted multiple representations to represent an image patch instead of …", "link": "https://ieeexplore.ieee.org/abstract/document/9240973/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, X Gao - IEEE Transactions on Neural …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 58, "pdfUrl": null}], "ControlNet face generation from sketch": [{"title": "Sketch-guided scene image generation", "snippet": "… generation, we utilize ControlNet to generate corresponding images from independent sketch objects, effectively preserving the shape of sketches … as the generation of human face [7…", "link": "https://arxiv.org/abs/2407.06469", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv:2407.06469, 2024 - arxiv.org", "year": 2407, "citedBy": 2, "pdfUrl": "https://arxiv.org/pdf/2407.06469"}, {"title": "Controllable Text-to-Image Generation with Automatic Sketches", "snippet": "… ControlNet can generate images following the conditions. In this work, we use ControlNet as the base image generation … prompt GPT-4 to generate sketches in TikZ code following the …", "link": "https://openreview.net/forum?id=tMKz4IgSZQ", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Wang - openreview.net", "year": null, "citedBy": null, "pdfUrl": "https://openreview.net/pdf?id=tMKz4IgSZQ"}, {"title": "Textgaze: gaze-controllable face generation with natural language", "snippet": "… We use the data to train a conditional diffusion model for the gaze-controllable face generation. We use the ControlNet [42] to generate face images from facial sketches. We learn the …", "link": "https://dl.acm.org/doi/abs/10.1145/3664647.3681252", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> - Proceedings of the 32nd ACM …, 2024 - dl.acm.org", "year": 2024, "citedBy": 1, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/3664647.3681252"}, {"title": "Sketchdream: Sketch-based text-to-3d generation and editing", "snippet": "… multiple frontal faces), we build a 3D ControlNet based on … map generation diffusion model, which takes the sketch 𝑆 and … Since the success of 2D ControlNet comes from the spatial …", "link": "https://dl.acm.org/doi/abs/10.1145/3658120", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, L Gao - ACM Transactions on Graphics (TOG), 2024 - dl.acm.org", "year": 2024, "citedBy": 15, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/3658120"}, {"title": "AirSketch: Generative Motion to Sketch", "snippet": "… Can we generate sketches from hand motions without … , we aim to generate sketches from hand motions videos captured … the generated sketches between ControlNet and T2IAdapter, …", "link": "https://arxiv.org/abs/2407.08906", "publicationInfo": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, YS Rawat - arXiv preprint arXiv:2407.08906, 2024 - arxiv.org", "year": 2407, "citedBy": null, "pdfUrl": "https://arxiv.org/pdf/2407.08906"}, {"title": "It's All About Your Sketch: Democratising Sketch Control in Diffusion Models", "snippet": "… approach of existing sketchconditional diffusion models (eg, ControlNet [90], T2IAdapter [55], etc.), we take a parallel approach to “sketchcondition” the generation process via cross-…", "link": "http://openaccess.thecvf.com/content/CVPR2024/html/<PERSON><PERSON>_Its_All_About_Your_Sketch_Democratising_Sketch_Control_in_Diffusion_CVPR_2024_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2024 - openaccess.thecvf.com", "year": 2024, "citedBy": 22, "pdfUrl": "http://openaccess.thecvf.com/content/CVPR2024/papers/<PERSON><PERSON>_Its_All_About_Your_Sketch_Democratising_Sketch_Control_in_Diffusion_CVPR_2024_paper.pdf"}, {"title": "Sketch2Human: Deep Human Generation with Disentangled Geometry and Appearance Control", "snippet": "… Sketching offers such editing ability and has been adopted in various sketch-based face generation … Text2Human and ControlNet take parsing maps and canny maps as input, …", "link": "https://arxiv.org/abs/2404.15889", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Fu - arXiv preprint arXiv:2404.15889, 2024 - arxiv.org", "year": 2404, "citedBy": null, "pdfUrl": "https://arxiv.org/pdf/2404.15889?"}, {"title": "Sketch2NeRF: multi-view sketch-guided text-to-3D generation", "snippet": "… objects are limited to a few specific categories (eg, face [22]). By leveraging the pretained 2D … the Sketch-conditioned ControlNet guidance. To effectively optimize NeRF with ControlNet …", "link": "https://arxiv.org/abs/2401.14257", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv …, 2024 - arxiv.org", "year": 2024, "citedBy": 5, "pdfUrl": "https://arxiv.org/pdf/2401.14257"}, {"title": "Sketch-2-4D: Sketch driven dynamic 3D scene generation", "snippet": "… sketch-based controllability, we use a pre-trained ControlNet … Canny edge and hand-drawn sketch are types of sketches. … The Baseline-image method is able to generate the face in the …", "link": "https://www.sciencedirect.com/science/article/pii/S1524070324000195", "publicationInfo": "<PERSON><PERSON>, <PERSON><PERSON>, TJ Mu - Graphical Models, 2024 - Elsevier", "year": 2024, "citedBy": 3, "pdfUrl": null}, {"title": "Sketch3D: Style-Consistent Guidance for Sketch-to-3D Generation", "snippet": "… face challenges in generating realistic 3D assets. To accomplish generalizable 3D … a sketch image and a text prompt as input, we first generate a reference image 𝐼ref using ControlNet. …", "link": "https://dl.acm.org/doi/abs/10.1145/3664647.3680641", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2024 - dl.acm.org", "year": 2024, "citedBy": 1, "pdfUrl": "https://arxiv.org/pdf/2404.01843"}, {"title": "TexControl: Sketch-Based Two-Stage Fashion Image Generation Using Diffusion Model", "snippet": "… generate the fashion image corresponding to the sketch input. First, we adopt ControlNet to generate the fashion image from sketch … clothing generation diffusion models currently face a …", "link": "https://ieeexplore.ieee.org/abstract/document/10614465/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - 2024 Nicograph International …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 3, "pdfUrl": "https://arxiv.org/pdf/2405.04675"}, {"title": "Controlrm: Fast and controllable 3d generation via large reconstruction model", "snippet": "… (2) Sketch Condition: Given the 2D sketch image on the reference view, we use the … , the sketch extraction network provided by ControlNet [34] is employed to derive the sketch map from …", "link": "https://arxiv.org/abs/2410.09592", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>… - arXiv preprint arXiv …, 2024 - arxiv.org", "year": 2024, "citedBy": 2, "pdfUrl": "https://arxiv.org/pdf/2410.09592"}, {"title": "Face generation and editing with stylegan: A survey", "snippet": "… approaches connected with face restoration and producing … natives to StyleGAN for face generation and editing methods. … Similarly, ControlNet [116] allows to control the generation of …", "link": "https://ieeexplore.ieee.org/abstract/document/10399793/", "publicationInfo": "<PERSON>, <PERSON>… - … on pattern analysis …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 69, "pdfUrl": "https://ieeexplore.ieee.org/iel7/34/4359286/10399793.pdf"}, {"title": "Ecnet: Effective controllable text-to-image diffusion models", "snippet": "… ControlNet and HumanSD, our model ECNet exhibits superior capabilities and robustness in image generation … the effectiveness of the ECNet in the sketch control task. Initially, we …", "link": "https://arxiv.org/abs/2403.18417", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv …, 2024 - arxiv.org", "year": 2024, "citedBy": 9, "pdfUrl": "https://arxiv.org/pdf/2403.18417"}, {"title": "S2TD-Face: Reconstruct a Detailed 3D Face with Controllable Texture from a Single Sketch", "snippet": "… and detailed 3D faces from sketches, named S2TD-Face. S2TD-Face introduces a two-stage … framework across different sketch styles, we generate 5 different types of sketches for each …", "link": "https://dl.acm.org/doi/abs/10.1145/3664647.3681159", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the 32nd ACM …, 2024 - dl.acm.org", "year": 2024, "citedBy": 1, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/3664647.3681159"}], "StyleGAN face synthesis sketch conditioning": [{"title": "Face generation and editing with stylegan: A survey", "snippet": "… as a conditioning signal. The person’s identity is preserved in the generated videos, conditioned by a single image of a face or … is to employ StyleGAN to produce a synthetic dataset for a …", "link": "https://ieeexplore.ieee.org/abstract/document/10399793/", "publicationInfo": "<PERSON>, <PERSON>… - … on pattern analysis …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 69, "pdfUrl": "https://ieeexplore.ieee.org/iel7/34/4359286/10399793.pdf"}, {"title": "DrawingInStyles: Portrait image generation and editing with spatially conditioned StyleGAN", "snippet": "… network utilizes the pre-trained layers of the original StyleGAN synthesis network and takes … work, our SC-StyleGAN is not limited to faces. In fact, our conditioning idea can be applied to …", "link": "https://ieeexplore.ieee.org/abstract/document/9784910/", "publicationInfo": "<PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> - IEEE transactions on …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 25, "pdfUrl": "https://huiye19.github.io/documents/drawinginstyles.pdf"}, {"title": "Sketch-guided latent diffusion model for high-fidelity face image synthesis", "snippet": "… disentangled representation within StyleGAN’s w+ space, sketch-to-sketch translation has been … to facilitate denoising, we introduce a ‘‘Conditioning Module’’ by pretraining a Multi-AE …", "link": "https://ieeexplore.ieee.org/abstract/document/10373005/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE Access, 2023 - ieeexplore.ieee.org", "year": 2023, "citedBy": 11, "pdfUrl": "https://ieeexplore.ieee.org/iel7/6287639/6514899/10373005.pdf"}, {"title": "Styleflow: Attribute-conditioned exploration of stylegan-generated images using conditional continuous normalizing flows", "snippet": "… These architectures are especially strong in human face synthesis. Another strong system is BigGAN [<PERSON> et al. 2018], which produces excellent results on ImageNet [<PERSON><PERSON> et al. 2009]…", "link": "https://dl.acm.org/doi/abs/10.1145/3447648", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, P Wonka - ACM Transactions on Graphics …, 2021 - dl.acm.org", "year": 2021, "citedBy": 604, "pdfUrl": "https://repository.kaust.edu.sa/bitstreams/3b65e274-ab25-4a8a-97b0-f0ccffde9ec2/download"}, {"title": "Denoising diffusion probabilistic model for face sketch-to-photo synthesis", "snippet": "… The input signal xt is concatenated with the conditioned sketch image xsk and the coarse photo xco. E and D represent the UNet encoder and decoder, respectively. Located between …", "link": "https://ieeexplore.ieee.org/abstract/document/10547051/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - IEEE Transactions on …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 10, "pdfUrl": null}, {"title": "State‐of‐the‐Art in the Architecture, Methods and Applications of StyleGAN", "snippet": "… [BDS18] by conditioning the synthesis process on class … observed by the discriminator when conditioned on such a vector … the aforementioned sketch-to-face and semantic map-to-face. …", "link": "https://onlinelibrary.wiley.com/doi/abs/10.1111/cgf.14503", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> - Computer Graphics …, 2022 - Wiley Online Library", "year": 2022, "citedBy": 104, "pdfUrl": "https://arxiv.org/pdf/2202.14020"}, {"title": "Encoding in style: a stylegan encoder for image-to-image translation", "snippet": "… StyleGAN inversion, multi-modal conditional image synthesis… aims at generating photorealistic images conditioned on … Here, we evaluate using pSp for synthesizing face images from …", "link": "http://openaccess.thecvf.com/content/CVPR2021/html/Richardson_Encoding_in_Style_A_StyleGAN_Encoder_for_Image-to-Image_Translation_CVPR_2021_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2021 - openaccess.thecvf.com", "year": 2021, "citedBy": 1420, "pdfUrl": "http://openaccess.thecvf.com/content/CVPR2021/papers/Richardson_Encoding_in_Style_A_StyleGAN_Encoder_for_Image-to-Image_Translation_CVPR_2021_paper.pdf"}, {"title": "HCGAN: hierarchical contrast generative adversarial network for unpaired sketch face synthesis", "snippet": "… It utilizes pre-trained StyleGAN to extract rich semantic deep features, achieving satisfactory … Diffusion models is to find a noise map and a conditioning vector corresponding to a …", "link": "https://peerj.com/articles/cs-2184/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> <PERSON>eerJ Computer Science, 2024 - peerj.com", "year": 2024, "citedBy": null, "pdfUrl": "https://peerj.com/articles/cs-2184.pdf"}, {"title": "Dcface: Synthetic face generation with dual condition diffusion model", "snippet": "… producing diverse samples in text-conditioned image generation [50]. We find that in unconditional … In prior works such as StyleGAN, 1st and 2nd order statistics of a feature are shown …", "link": "http://openaccess.thecvf.com/content/CVPR2023/html/<PERSON>_<PERSON><PERSON><PERSON>_Synthetic_Face_Generation_With_Dual_Condition_Diffusion_Model_CVPR_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - … of the ieee/cvf conference on …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 151, "pdfUrl": "http://openaccess.thecvf.com/content/CVPR2023/papers/Kim_<PERSON>F<PERSON>_Synthetic_Face_Generation_With_Dual_Condition_Diffusion_Model_CVPR_2023_paper.pdf"}, {"title": "Difffacesketch: High-fidelity face image synthesis with sketch-guided latent diffusion model", "snippet": "… the disentangled representation of StyleGAN’s w+ space has developed strongly, sketch-to-… And based on our sketch-conditioning pairs, the training loss LSGLDM of the conditional …", "link": "https://arxiv.org/abs/2302.06908", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>ta - arXiv preprint arXiv …, 2023 - arxiv.org", "year": 2023, "citedBy": 21, "pdfUrl": "https://arxiv.org/pdf/2302.06908"}, {"title": "Text Guided Facial Image Synthesis Using StyleGAN and Variational Autoencoder Trained CLIP", "snippet": "… The authors are also successful in conditioning the transformer on different modalities. A simple … expansion of the application of this study to generate sketch drawings of faces as well. …", "link": "https://link.springer.com/chapter/10.1007/978-3-031-42508-0_8", "publicationInfo": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> <PERSON> - … Conference on Artificial …, 2023 - Springer", "year": 2023, "citedBy": null, "pdfUrl": null}, {"title": "Picture that sketch: Photorealistic image generation from abstract sketches", "snippet": "… of StyleGAN’s latent space and the varying levels of sketch … generated output be conditioned on the input sketch and to what … add synthetic noisy strokes [57] onto clean input sketches. …", "link": "http://openaccess.thecvf.com/content/CVPR2023/html/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> <PERSON> - Proceedings of the …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 53, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2023/papers/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.pdf"}, {"title": "Controllable face sketch-photo synthesis with flexible generative priors", "snippet": "… tuning the pre-trained StyleGAN generator on the source and … for face sketch-photo synthesis as compared to other face … in the target domain, conditioned on a source domain input. …", "link": "https://dl.acm.org/doi/abs/10.1145/3581783.3611834", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the 31st …, 2023 - dl.acm.org", "year": 2023, "citedBy": 4, "pdfUrl": null}, {"title": "Semanticstylegan: Learning compositional generative priors for controllable image synthesis and editing", "snippet": "… priors is the StyleGAN series [35–37], where each generated image is conditioned on a set … In spite of the experiments on face datasets so far, our method indeed does not include any …", "link": "http://openaccess.thecvf.com/content/CVPR2022/html/<PERSON>_<PERSON><PERSON>ticStyleGAN_Learning_Compositional_Generative_Priors_for_Controllable_Image_Synthesis_and_CVPR_2022_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE …, 2022 - openaccess.thecvf.com", "year": 2022, "citedBy": 118, "pdfUrl": "http://openaccess.thecvf.com/content/CVPR2022/papers/Shi_SemanticStyleGAN_Learning_Compositional_Generative_Priors_for_Controllable_Image_Synthesis_and_CVPR_2022_paper.pdf"}, {"title": "Text-guided sketch-to-photo image synthesis", "snippet": "… face image 175 into the extended StyleGAN latent space that comprises of 176 both text and sketch … [28] focused 182 on inverting StyleGAN models by exploiting the layer-wise 183 …", "link": "https://ieeexplore.ieee.org/abstract/document/9893101/", "publicationInfo": "<PERSON>, NM Nasrabadi - IEEE Access, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 13, "pdfUrl": "https://ieeexplore.ieee.org/iel7/6287639/9668973/09893101.pdf"}], "face hallucination from sketch": [{"title": "Towards lightweight pixel-wise hallucination for heterogeneous face recognition", "snippet": "… For testing, the sketch and photo images are used as the probe … face hallucination methods as our method. That is, the recognition backbone is fixed during training and the hallucination …", "link": "https://ieeexplore.ieee.org/abstract/document/9971748/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, R He - IEEE Transactions on Pattern …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 12, "pdfUrl": null}, {"title": "Cross-spectral face hallucination via disentangling independent factors", "snippet": "… , we further test our method on a sketch dataset CUHK Face Sketch (CUFS) [28]. As shown … , we observe satisfactory results on such a sketch dataset. The synthesized details, including …", "link": "http://openaccess.thecvf.com/content_CVPR_2020/html/<PERSON><PERSON>_Cross-Spectral_Face_Hallucination_via_Disentangling_Independent_Factors_CVPR_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 75, "pdfUrl": "http://openaccess.thecvf.com/content_CVPR_2020/papers/<PERSON><PERSON>_Cross-Spectral_Face_Hallucination_via_Disentangling_Independent_Factors_CVPR_2020_paper.pdf"}, {"title": "Face image-sketch synthesis via generative adversarial fusion", "snippet": "… raising, ie (1) if and what extent face features can be regenerated from sketches; (2) what extent face features extracted from face sketches can be inverted to obtain the real face image. …", "link": "https://www.sciencedirect.com/science/article/pii/S0893608022002696", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, G <PERSON>hong - Neural networks, 2022 - Elsevier", "year": 2022, "citedBy": 10, "pdfUrl": "http://eprints.bournemouth.ac.uk/37686/1/Face_image_sketch_synthesis_via_generative_adversarial_fusion_pp.pdf"}, {"title": "Hierarchical Multivariate Representation Learning for Face Sketch Recognition", "snippet": "… sketch face paired data and maximizing the distance between … sketch face unpaired data in which sketch is the anchor, the corresponding face image is the positive, and another face …", "link": "https://ieeexplore.ieee.org/abstract/document/10432989/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, D Wu - IEEE Transactions on …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 1, "pdfUrl": null}, {"title": "Face sketch to photo translation using generative adversarial networks", "snippet": "… synthesize high-quality natural face photos and employ an … sketch. We train a network to map the facial features extracted from the input sketch to a vector in the latent space of the face …", "link": "https://arxiv.org/abs/2110.12290", "publicationInfo": "<PERSON><PERSON>, MS Fard, A Nickabadi - arXiv preprint arXiv:2110.12290, 2021 - arxiv.org", "year": 2110, "citedBy": 10, "pdfUrl": "https://arxiv.org/pdf/2110.12290"}, {"title": "On hallucinating context and background pixels from a face mask using multi-scale gans", "snippet": "… In [60, 33], the inpainting process is guided by a rough sketch provided by the user. All … Adversarial loss (Ladv): To push our hallucinations towards the manifold of real face images, we …", "link": "http://openaccess.thecvf.com/content_WACV_2020/html/<PERSON><PERSON>_On_Hallucinating_Context_and_Background_Pixels_from_a_Face_Mask_WACV_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>… - Proceedings of the …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 31, "pdfUrl": "https://openaccess.thecvf.com/content_WACV_2020/papers/<PERSON><PERSON>_On_Hallucinating_Context_and_Background_Pixels_from_a_Face_Mask_WACV_2020_paper.pdf"}, {"title": "Escaping data scarcity for high-resolution heterogeneous face hallucination", "snippet": "… In this paper, we propose a new face hallucination paradigm for … Unlike existing methods that learn face synthesis entirely … domains such as TH, VIS or sketch. In this paper, we refer to …", "link": "http://openaccess.thecvf.com/content/CVPR2022/html/Mei_Escaping_Data_Scarcity_for_High-Resolution_Heterogeneous_Face_Hallucination_CVPR_2022_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON> - … of the IEEE/CVF conference on …, 2022 - openaccess.thecvf.com", "year": 2022, "citedBy": 12, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2022/papers/Mei_Escaping_Data_Scarcity_for_High-Resolution_Heterogeneous_Face_Hallucination_CVPR_2022_paper.pdf"}, {"title": "Picture that sketch: Photorealistic image generation from abstract sketches", "snippet": "… are used prior works can hallucinate high-quality photorealistic photos, whereas rather “peculiar” looking results are obtained when faced with amateur human sketches. This is be…", "link": "http://openaccess.thecvf.com/content/CVPR2023/html/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> <PERSON> - Proceedings of the …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 53, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2023/papers/<PERSON><PERSON>_Picture_That_Sketch_Photorealistic_Image_Generation_From_Abstract_Sketches_CVPR_2023_paper.pdf"}, {"title": "Toward realistic face photo–sketch synthesis via composition-aided GANs", "snippet": "… Similar ideas have also been proposed for face image hallucination [40], [41]. In contrast, we propose to employ facial composition information in the loop of learning to boost …", "link": "https://ieeexplore.ieee.org/abstract/document/9025751/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE transactions on …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 143, "pdfUrl": "https://arxiv.org/pdf/1712.00899"}, {"title": "Face Hallucination Methods—A Review", "snippet": "… , face hallucination for single frame, unviewed sketches, patch-based multitask deep NN and concluded with structured, linear models of coupled sparse support and correspondence-…", "link": "https://link.springer.com/chapter/10.1007/978-981-16-1773-7_31", "publicationInfo": "<PERSON><PERSON>, <PERSON><PERSON>… - Smart Technologies in …, 2021 - Springer", "year": 2021, "citedBy": null, "pdfUrl": null}, {"title": "Exemplar guided cross-spectral face hallucination via mutual information disentanglement", "snippet": "… face recognition still remains many obstacles which attract much attention. In general, heterogeneous face recognition refers to the process of matching faces … [2] and sketch to photo [3]. …", "link": "https://ieeexplore.ieee.org/abstract/document/9412141/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - 2020 25th International …, 2021 - ieeexplore.ieee.org", "year": 2020, "citedBy": 4, "pdfUrl": null}, {"title": "3d sketch-aware semantic scene completion via semi-supervised structure prior", "snippet": "… sketch-aware semantic scene completion network, which injects a 3D Sketch Hallucination Module to infer the full 3D sketch … from the hallucinated 3D sketch to guide the reconstruction …", "link": "http://openaccess.thecvf.com/content_CVPR_2020/html/Chen_3D_Sketch-Aware_Semantic_Scene_Completion_via_Semi-Supervised_Structure_Prior_CVPR_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 159, "pdfUrl": "https://openaccess.thecvf.com/content_CVPR_2020/papers/Chen_3D_Sketch-Aware_Semantic_Scene_Completion_via_Semi-Supervised_Structure_Prior_CVPR_2020_paper.pdf"}, {"title": "Face hallucination based on cluster consistent dictionary learning", "snippet": "… However, most of them assume the training face dataset is sufficiently … face hallucination method, based on cluster consistent dictionary learning with the assumption that human faces …", "link": "https://ietresearch.onlinelibrary.wiley.com/doi/abs/10.1049/ipr2.12269", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - IET Image Processing, 2021 - Wiley Online Library", "year": 2021, "citedBy": 1, "pdfUrl": "https://ietresearch.onlinelibrary.wiley.com/doi/pdfdirect/10.1049/ipr2.12269"}, {"title": "Face photo-sketch synthesis via full-scale identity supervision", "snippet": "… the face images much more precisely than before. Considering the face image translation is also a type of face image re-representation, we attempt to introduce face recognition models …", "link": "https://www.sciencedirect.com/science/article/pii/S0031320321006221", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Gao - Pattern Recognition, 2022 - Elsevier", "year": 2022, "citedBy": 31, "pdfUrl": null}, {"title": "Noise robust face hallucination based on smooth correntropy representation", "snippet": "… Unfortunately, the face images captured by camera sensors are … face image, a technology named face hallucination (or face … and residual learning for face sketch synthesis,” IEEE Trans. …", "link": "https://ieeexplore.ieee.org/abstract/document/9411738/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>… - IEEE Transactions on …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 26, "pdfUrl": null}], "local deployment face generation AI": [{"title": "Cloud versus edge deployment strategies of real-time face recognition inference", "snippet": "… on a large dataset of faces to generate embeddings of faces. … • Privacy: The use of artificial intelligence for surveillance and … to local processing for the face detection (MTCNN) and face …", "link": "https://ieeexplore.ieee.org/abstract/document/9350171/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - IEEE Transactions on …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 57, "pdfUrl": "http://www.cister.isep.ipp.pt/docs/cloud_versus_edge_deployment_strategies_of_real_time_face_recognition_inference/1706/view.pdf"}, {"title": "Designing automated deployment strategies of face recognition solutions in heterogeneous IoT platforms", "snippet": "… Instead of using DNNs for images processing, this work proposes classic Haar and Local Binary Pattern (LBP) features for face detection and identification. Although these methods use …", "link": "https://www.mdpi.com/2078-2489/12/12/532", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - Information, 2021 - mdpi.com", "year": 2021, "citedBy": 4, "pdfUrl": "https://www.mdpi.com/2078-2489/12/12/532/pdf"}, {"title": "Characterizing face recognition for resource efficient deployment on edge", "snippet": "… DC-CDN propose C-CDC convolution based on the CDC, which exploits sparse local … hardware developers for furthering Face Recognition & Edge AI. While the work focuses on …", "link": "https://openaccess.thecvf.com/content/ICCV2023W/RCV/html/Biswas_Characterizing_Face_Recognition_for_Resource_Efficient_Deployment_on_Edge_ICCVW_2023_paper.html", "publicationInfo": "<PERSON> <PERSON><PERSON><PERSON>, SA Patnaik, <PERSON><PERSON> - Proceedings of the …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": null, "pdfUrl": "https://openaccess.thecvf.com/content/ICCV2023W/RCV/papers/Biswas_Characterizing_Face_Recognition_for_Resource_Efficient_Deployment_on_Edge_ICCVW_2023_paper.pdf"}, {"title": "Finding AI-Generated Faces in the Wild", "snippet": "… AI-based image generation has continued to rapidly improve, producing increasingly more … GLFF: Global and local feature fusion for AI-synthesized image detection. IEEE Transactions …", "link": "https://openaccess.thecvf.com/content/CVPR2024W/WMF/html/Porcile_Finding_AI-Generated_Faces_in_the_Wild_CVPRW_2024_paper.html", "publicationInfo": "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2024 - openaccess.thecvf.com", "year": 2024, "citedBy": 5, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2024W/WMF/papers/Porcile_Finding_AI-Generated_Faces_in_the_Wild_CVPRW_2024_paper.pdf"}, {"title": "Hugginggpt: Solving ai tasks with chatgpt and its friends in hugging face", "snippet": "… , generation, interaction, and reasoning, we advocate that LLMs could act as a controller to manage existing AI models to solve complicated AI … have to deploy local inference endpoints, …", "link": "https://proceedings.neurips.cc/paper_files/paper/2023/hash/77c33e6a367922d003ff102ffb92b658-Abstract-Conference.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Advances in Neural …, 2023 - proceedings.neurips.cc", "year": 2023, "citedBy": 1250, "pdfUrl": "https://proceedings.neurips.cc/paper_files/paper/2023/file/77c33e6a367922d003ff102ffb92b658-Paper-Conference.pdf"}, {"title": "Edge-AI-driven framework with efficient mobile network design for facial expression recognition", "snippet": "… is used to generate it, to … locally when processing “easy” input images, or when faced with an unreliable network connection to the cloud; the optional attention modules are deployed in …", "link": "https://dl.acm.org/doi/abs/10.1145/3587038", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Wan - ACM Transactions on Embedded …, 2023 - dl.acm.org", "year": 2023, "citedBy": 66, "pdfUrl": "https://www.academia.edu/download/98833308/Edge_AI_Driven_Framework_with_Efficient_Mobile_Network_Design_for_Facial_Expression_Recognition_1_.pdf"}, {"title": "The Janus face of artificial intelligence feedback: Deployment versus disclosure effects on employee performance", "snippet": "… While deploying AI to generate … AI (9,994.130 in local currency), is 4.7% lower than that of Group 4, who are informed that the feedback is from a human manager (10,488.515 in local …", "link": "https://onlinelibrary.wiley.com/doi/abs/10.1002/smj.3322", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Strategic Management Journal, 2021 - Wiley Online Library", "year": 2021, "citedBy": 404, "pdfUrl": "https://onlinelibrary.wiley.com/doi/pdfdirect/10.1002/smj.3322"}, {"title": "Responsible urban innovation with local government artificial intelligence (AI): A conceptual framework and research agenda", "snippet": "… The urbanization problems we face may be alleviated using … , deploying and managing local government AI systems in order … generate new knowledge on the local government use of AI …", "link": "https://www.mdpi.com/2199-8531/7/1/71", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON> - Journal of Open …, 2021 - mdpi.com", "year": 2021, "citedBy": 277, "pdfUrl": "https://www.mdpi.com/2199-8531/7/1/71/pdf"}, {"title": "AI-enabled space-air-ground integrated networks: Management and optimization", "snippet": "… Specifically, we deploy hierarchical local controllers in … contained in the stored data is an essential challenge we face. … In addition, AI computing of big data will also generate unac…", "link": "https://ieeexplore.ieee.org/abstract/document/10103768/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - IEEE …, 2023 - ieeexplore.ieee.org", "year": 2023, "citedBy": 73, "pdfUrl": "https://shodhratna.thapar.edu:8443/jspui/bitstream/tiet/168/1/AI-Enabled_Space-Air-Ground_Integrated_Networks_Management_and_Optimization.pdf"}, {"title": "Edge AI: a survey", "snippet": "… the Edge AI approach to deploying AI algorithms and models … charge for the future generation of AI computing, which will … and techniques can be deployed on local devices or at the …", "link": "https://www.sciencedirect.com/science/article/pii/S2667345223000196", "publicationInfo": "<PERSON>, <PERSON> - Internet of Things and Cyber-Physical Systems, 2023 - Elsevier", "year": 2023, "citedBy": 318, "pdfUrl": null}, {"title": "Foundation models for generalist medical artificial intelligence", "snippet": "… applications that this new generation of models will enable. … , GMAI deployed in surgical settings will probably face … However, other GMAI models may need to be deployed locally in …", "link": "https://www.nature.com/articles/s41586-023-05881-4", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, HM Krumholz… - Nature, 2023 - nature.com", "year": 2023, "citedBy": 1310, "pdfUrl": "https://www.nature.com/articles/s41586-023-05881-4.pdf"}, {"title": "Toward trustworthy ai: Blockchain-based architecture design for accountability and fairness of federated learning systems", "snippet": "… In particular, federated learning systems face accountability and … device stops all the local training to deploy the last global … The broad use of AI of building next-generation applications […", "link": "https://ieeexplore.ieee.org/abstract/document/9686048/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE Internet of …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 133, "pdfUrl": null}, {"title": "Unleashing the power of edge-cloud generative AI in mobile networks: A survey of AIGC services", "snippet": "… , and privacy challenges of deploying mobile AIGC networks. Finally, … In the realm of image generation, generative AI models can … execute generative AI models and perform local AIGC …", "link": "https://ieeexplore.ieee.org/abstract/document/********/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - … Surveys & Tutorials, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 308, "pdfUrl": "https://arxiv.org/pdf/2303.16129"}, {"title": "Spectrum management in the 6G era: The role of regulation and spectrum sharing", "snippet": "… generation of mobile communication networks and will face … Artificial intelligence (AI) inspired algorithms for spectrum … to be complemented by local deployments of 5G networks by a …", "link": "https://ieeexplore.ieee.org/abstract/document/9083851/", "publicationInfo": "<PERSON>-<PERSON>, <PERSON> - 2020 2nd 6G wireless …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 133, "pdfUrl": "https://oulurepo.oulu.fi/bitstream/handle/10024/28798/nbnfi-fe2020050725614.pdf?sequence=1"}, {"title": "“Brilliant AI doctor” in rural clinics: challenges in AI-powered clinical decision support system deployment", "snippet": "… such as the misalignment with local context and workfow, the … parency of AI, that is, how AI-CDSS works to generate the … has shown that AI-CDSS applications may face diffculties when …", "link": "https://dl.acm.org/doi/abs/10.1145/3411764.3445432", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2021 - dl.acm.org", "year": 2021, "citedBy": 192, "pdfUrl": "https://arxiv.org/pdf/2101.01524"}], "open source sketch to photo face": [{"title": "Deepfacepencil: Creating face images from freehand sketches", "snippet": "… a novel sketch-based face image synthesis … in image translation frameworks, we propose a sketch-toface translation system that is robust to hand-drawn sketches with various drawing …", "link": "https://dl.acm.org/doi/abs/10.1145/3394171.3413684", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - Proceedings of the 28th …, 2020 - dl.acm.org", "year": 2020, "citedBy": 50, "pdfUrl": "https://arxiv.org/pdf/2008.13343"}, {"title": "Face sketch to photo translation using generative adversarial networks", "snippet": "… Translating face sketches to photo-realistic faces is an interesting and essential task in … the sketch and the real image such as the lack of color and details of the skin tissue in the sketch. …", "link": "https://arxiv.org/abs/2110.12290", "publicationInfo": "<PERSON><PERSON>, MS Fard, A Nickabadi - arXiv preprint arXiv:2110.12290, 2021 - arxiv.org", "year": 2110, "citedBy": 10, "pdfUrl": "https://arxiv.org/pdf/2110.12290"}, {"title": "Deep generation of face images from sketches", "snippet": "… To this end we present a novel deep learning framework for sketch-based face image synthesis, as illustrated in Figure 3. Our system consists of three main modules, namely, CE (…", "link": "https://arxiv.org/abs/2006.01047", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Fu - arXiv preprint arXiv:2006.01047, 2020 - arxiv.org", "year": 2006, "citedBy": 195, "pdfUrl": "https://arxiv.org/pdf/2006.01047"}, {"title": "Identity-aware CycleGAN for face photo-sketch synthesis and recognition", "snippet": "… to supervise the image generation network. It improves CycleGAN on photo-sketch synthesis by … Extensive experiments are performed on both photo-to-sketch and sketch-to-photo tasks …", "link": "https://www.sciencedirect.com/science/article/pii/S0031320320300558", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> Recognition, 2020 - Elsevier", "year": 2020, "citedBy": 137, "pdfUrl": "https://arxiv.org/pdf/2103.16019"}, {"title": "Deep learning for free-hand sketch: A survey", "snippet": "… Smiling faces, for example, are always recognized by humans (Fig. 1). … For example, a near ‘realistic’ (close to photo edge-map) sketch image could be portrayed in different ways as …", "link": "https://ieeexplore.ieee.org/abstract/document/9706366/", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>… - IEEE transactions on …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 175, "pdfUrl": "https://arxiv.org/pdf/2001.02600"}, {"title": "Unsupervised sketch to photo synthesis", "snippet": "… envision a realistic photo given a free-hand sketch that is not … sketch to photo synthesis for the first time, learning from unpaired sketch and photo data where the target photo for a sketch …", "link": "https://link.springer.com/chapter/10.1007/978-3-030-58580-8_3", "publicationInfo": "<PERSON>, <PERSON>, S<PERSON> Yu - Computer Vision–ECCV 2020: 16th European …, 2020 - Springer", "year": 2020, "citedBy": 65, "pdfUrl": "https://arxiv.org/pdf/1909.08313"}, {"title": "Freedom: Training-free energy-guided conditional diffusion model", "snippet": "… face editing applications with training-free guidance. (a) We use the segmentation map, sketch, landmarks, and face … two conditions: the structure information from the source image and …", "link": "http://openaccess.thecvf.com/content/ICCV2023/html/Yu_FreeDoM_Training-Free_Energy-Guided_Conditional_Diffusion_Model_ICCV_2023_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - Proceedings of the …, 2023 - openaccess.thecvf.com", "year": 2023, "citedBy": 165, "pdfUrl": "http://openaccess.thecvf.com/content/ICCV2023/papers/Yu_FreeDoM_Training-Free_Energy-Guided_Conditional_Diffusion_Model_ICCV_2023_paper.pdf"}, {"title": "The ImageJ ecosystem: Open‐source software for image visualization, processing, and analysis", "snippet": "… , image segmentation, face recognition, and … Open-source access to code repositories allows developers to adapt existing solutions to increasingly advanced image datasets, drawing …", "link": "https://onlinelibrary.wiley.com/doi/abs/10.1002/pro.3993", "publicationInfo": "<PERSON>, <PERSON><PERSON>, CT <PERSON>den… - Protein …, 2021 - Wiley Online Library", "year": 2021, "citedBy": 300, "pdfUrl": "https://onlinelibrary.wiley.com/doi/pdf/10.1002/pro.3993"}, {"title": "Masked face recognition for secure authentication", "snippet": "… taking new pictures for authentication. We present an opensource tool, MaskTheFace to mask faces … For this purpose, we use the following variations of the LFW dataset to draw effective …", "link": "https://arxiv.org/abs/2008.11104", "publicationInfo": "<PERSON>, A Raychowdhury - arXiv preprint arXiv:2008.11104, 2020 - arxiv.org", "year": 2008, "citedBy": 277, "pdfUrl": "https://arxiv.org/pdf/2008.11104"}, {"title": "Image quilting for texture synthesis and transfer", "snippet": "… constraint, yielding a rendered image where the face image appears to be rendered in rice. … to render a photograph using the line drawing texture of a particular source drawing; or to …", "link": "https://dl.acm.org/doi/abs/10.1145/3596711.3596771", "publicationInfo": "<PERSON> Ef<PERSON>, <PERSON><PERSON> Freeman - … Papers: Pushing the Boundaries, Volume 2, 2023 - dl.acm.org", "year": 2023, "citedBy": 3786, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/383259.383296"}, {"title": "Deepfakes generation and detection: State-of-the-art, open challenges, countermeasures, and way forward", "snippet": "… In face-swap deepfakes, the face of the source person is replaced with the … Face synthesis and attribute manipulation involve the generation of photo-realistic face images as well as …", "link": "https://link.springer.com/article/10.1007/s10489-022-03766-z", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - Applied …, 2023 - Springer", "year": 2023, "citedBy": 514, "pdfUrl": "https://arxiv.org/pdf/2103.00484"}, {"title": "The creativity of text-to-image generation", "snippet": "… A growing number of generative systems are available as open source in Jupy<PERSON> … sketch with Stable Diffusion [52], run the output through a model that was trained on improving faces (…", "link": "https://dl.acm.org/doi/abs/10.1145/3569219.3569352", "publicationInfo": "<PERSON> - Proceedings of the 25th international academic …, 2022 - dl.acm.org", "year": 2022, "citedBy": 364, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/3569219.3569352"}, {"title": "Diffusiondb: A large-scale prompt gallery dataset for text-to-image generative models", "snippet": "… predicts the probabilities of five image types: drawing, hentai, … CC0 1.0 license and open source all collection and analysis … We also extend our appreciation to Hugging Face for hosting …", "link": "https://arxiv.org/abs/2210.14896", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv …, 2022 - arxiv.org", "year": 2022, "citedBy": 355, "pdfUrl": "https://arxiv.org/pdf/2210.14896"}, {"title": "RhizoVision Explorer: open-source software for root image analysis and measurement standardization", "snippet": "… open-source software designed to enable researchers interested in roots by providing an easy-to-use interface, fast image … data using a new copper wire image set. In comparison, the …", "link": "https://academic.oup.com/aobpla/article-abstract/13/6/plab056/6366354", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - AoB plants, 2021 - academic.oup.com", "year": 2021, "citedBy": 268, "pdfUrl": "https://academic.oup.com/aobpla/article-pdf/13/6/plab056/41139147/plab056.pdf"}, {"title": "Sdedit: Guided image synthesis and editing with stochastic differential equations", "snippet": "… 1) a human face with limited detail for a CelebA-HQ model, 2) a human face with spikes for a … 2019) where we use extra sketch together with stroke as the input guide (see Fig. 14). We …", "link": "https://arxiv.org/abs/2108.01073", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> - arXiv preprint arXiv …, 2021 - arxiv.org", "year": 2021, "citedBy": 1745, "pdfUrl": "https://arxiv.org/pdf/2108.01073"}], "lightweight face generation models": [{"title": "A lightweight deep learning model for real‐time face recognition", "snippet": "… lightweight face recognition models, this study evaluated its performance on a small self-collected dataset consisting of 12 subjects and 240 images for model … dimensions for generating …", "link": "https://ietresearch.onlinelibrary.wiley.com/doi/abs/10.1049/ipr2.12903", "publicationInfo": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, HC Li - IET Image Processing, 2023 - Wiley Online Library", "year": 2023, "citedBy": 12, "pdfUrl": "https://ietresearch.onlinelibrary.wiley.com/doi/pdf/10.1049/ipr2.12903"}, {"title": "DGFaceNet: Lightweight and efficient face recognition", "snippet": "… of ghost modules in face recognition leads to significant degradation of model performance. … feature channels are output in GDFaceNet to generate models of various complexity. Unlike …", "link": "https://www.sciencedirect.com/science/article/pii/S0952197623006978", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Engineering Applications of Artificial …, 2023 - Elsevier", "year": 2023, "citedBy": 4, "pdfUrl": null}, {"title": "Ghostfacenets: Lightweight face recognition model from cheap operations", "snippet": "The development of deep learning-based biometric models … for a group of lightweight face recognition models called … reveals that these models offer superior performance while …", "link": "https://ieeexplore.ieee.org/abstract/document/10098610/", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>… - IEEE …, 2023 - ieeexplore.ieee.org", "year": 2023, "citedBy": 91, "pdfUrl": "https://ieeexplore.ieee.org/iel7/6287639/6514899/10098610.pdf"}, {"title": "Benchmarking lightweight face architectures on specific face recognition scenarios", "snippet": "… of lightweight face models on real applications. Lightweight architectures proposed for face … recent lightweight architectures on five face recognition scenarios: image and video based …", "link": "https://link.springer.com/article/10.1007/s10462-021-09974-2", "publicationInfo": "<PERSON>, <PERSON>… - Artificial Intelligence …, 2021 - <PERSON>", "year": 2021, "citedBy": 60, "pdfUrl": null}, {"title": "Deep generation of face images from sketches", "snippet": "… techniques allow fast generation of face images from … to implicitly model the shape space of plausible face images … For easy comparison we overlay input sketches (in light blue) …", "link": "https://arxiv.org/abs/2006.01047", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Fu - arXiv preprint arXiv:2006.01047, 2020 - arxiv.org", "year": 2006, "citedBy": 195, "pdfUrl": "https://arxiv.org/pdf/2006.01047"}, {"title": "Blendgan: Implicitly gan blending for arbitrary stylized face generation", "snippet": "… Therefore, the stylized-face images generated by our model have equally good performance for both light-skinned and darker-skinned faces (as shown in Figures 1, 6 and 7), which …", "link": "https://proceedings.neurips.cc/paper/2021/hash/f8417d04a0a2d5e1fb5c5253a365643c-Abstract.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Advances in Neural …, 2021 - proceedings.neurips.cc", "year": 2021, "citedBy": 68, "pdfUrl": "https://proceedings.neurips.cc/paper_files/paper/2021/file/f8417d04a0a2d5e1fb5c5253a365643c-Paper.pdf"}, {"title": "Robust lightweight facial expression recognition network with label distribution training", "snippet": "… lightweight FER model in the spatial level, namely designing of the lightweight static FER model. … to recognize facial expression and the Label Distribution Generator (LDG) employed to …", "link": "https://ojs.aaai.org/index.php/aaai/article/view/16465", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Proceedings of the AAAI conference on artificial …, 2021 - ojs.aaai.org", "year": 2021, "citedBy": 269, "pdfUrl": "https://ojs.aaai.org/index.php/AAAI/article/view/16465/16272"}, {"title": "Pose-controllable talking face generation by implicitly modularized audio-visual representation", "snippet": "… cases such as large pose or low-light conditions. In this work, we … of our model to handle extreme views and achieving talking face … Our model, on the other hand, not only can generate …", "link": "http://openaccess.thecvf.com/content/CVPR2021/html/<PERSON>_<PERSON><PERSON>-Controllable_Talking_Face_Generation_by_Implicitly_Modularized_Audio-Visual_Representation_CVPR_2021_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the …, 2021 - openaccess.thecvf.com", "year": 2021, "citedBy": 446, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2021/papers/<PERSON>_<PERSON>se-Controllable_Talking_Face_Generation_by_Implicitly_Modularized_Audio-Visual_Representation_CVPR_2021_paper.pdf"}, {"title": "Toward fast, flexible, and robust low-light image enhancement", "snippet": "… Applications on low-light face detection and nighttime … a progressive perspective to model this task, the basic unit is written as … mation block in this work) that is cascaded to generate the …", "link": "https://openaccess.thecvf.com/content/CVPR2022/html/Ma_Toward_Fast_Flexible_and_Robust_Low-Light_Image_Enhancement_CVPR_2022_paper.html?ref=https://githubhelp.com", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE …, 2022 - openaccess.thecvf.com", "year": 2022, "citedBy": 872, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2022/papers/Ma_Toward_Fast_Flexible_and_Robust_Low-Light_Image_Enhancement_CVPR_2022_paper.pdf"}, {"title": "Emu: Enhancing image generation models using photogenic needles in a haystack", "snippet": "… generation of a wide range of visual concepts from text. However, these pre-trained models often face challenges when it comes to generating … as excessively dim or overexposed light. …", "link": "https://arxiv.org/abs/2309.15807", "publicationInfo": "<PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv …, 2023 - arxiv.org", "year": 2023, "citedBy": 212, "pdfUrl": "https://arxiv.org/pdf/2309.15807"}, {"title": "Eyes tell all: Irregular pupil shapes reveal gan-generated faces", "snippet": "… distributions of the facial landmarks. The work of [18] analyzes the light source directions from … Since StyleGAN2 [3] 2 is currently the state-of-the-art GAN face generation model with the …", "link": "https://ieeexplore.ieee.org/abstract/document/9746597/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - ICASSP 2022-2022 …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 109, "pdfUrl": "https://arxiv.org/pdf/2109.00162"}, {"title": "A comparison between stereophotogrammetry and smartphone structured light technology for three-dimensional face scanning", "snippet": "… facial surface, whereas the processing time was the time required by the mobile application or software to generate the 3D model … distance between the two models, the following two …", "link": "https://angle-orthodontist.kglmeridian.com/view/journals/angl/92/3/article-p358.xml", "publicationInfo": "<PERSON>, <PERSON>… - The Angle …, 2022 - angle-orthodontist.kglmeridian.com", "year": 2022, "citedBy": 71, "pdfUrl": "https://meridian.allenpress.com/angle-orthodontist/article-pdf/92/3/358/3045233/i1945-7103-92-3-358.pdf"}, {"title": "Adversarial light projection attacks on face recognition systems: A feasibility study", "snippet": "… attacks with ℓp norm, and models adversarial pattern generation as a linear approximation problem, and SparseFool [15] that aims to generate adversarial patterns by modifying a …", "link": "http://openaccess.thecvf.com/content_CVPRW_2020/html/w48/<PERSON><PERSON><PERSON>_Adversarial_Light_Projection_Attacks_on_Face_Recognition_Systems_A_Feasibility_CVPRW_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 103, "pdfUrl": "http://openaccess.thecvf.com/content_CVPRW_2020/papers/w48/<PERSON><PERSON>en_Adversarial_Light_Projection_Attacks_on_Face_Recognition_Systems_A_Feasibility_CVPRW_2020_paper.pdf"}, {"title": "Memories are one-to-many mapping alleviators in talking face generation", "snippet": "… renders the appearance of the target person with a light-weight neural rendering technique. <PERSON> et … model, we project the 3D face onto the 2D image plane through a perspective camera …", "link": "https://ieeexplore.ieee.org/abstract/document/10547422/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - … on Pattern Analysis …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 21, "pdfUrl": "https://arxiv.org/pdf/2212.05005"}, {"title": "Deepfakes generation and detection: State-of-the-art, open challenges, countermeasures, and way forward", "snippet": "… Face synthesis and attribute manipulation involve the generation of photo-realistic face images as well as facial … Lastly, audio deepfakes focus on the generation of the target speaker’s …", "link": "https://link.springer.com/article/10.1007/s10489-022-03766-z", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - Applied …, 2023 - Springer", "year": 2023, "citedBy": 514, "pdfUrl": "https://arxiv.org/pdf/2103.00484"}], "neural face synthesis from drawing": [{"title": "Line drawings for face portraits from photos using global and local structure based GANs", "snippet": "… and notable success of neural style transfer, it … face photos taken from ten face datasets [35], [36], [37], [38], [39], [40], [41], [42], [43], [44]. For each photo, we generate a synthetic drawing …", "link": "https://ieeexplore.ieee.org/abstract/document/9069416/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>… - IEEE Transactions on …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 49, "pdfUrl": "https://orca.cardiff.ac.uk/id/eprint/130961/1/APDrawingGAN++_TPAMI.pdf"}, {"title": "Neural contours: Learning to draw lines from 3d shapes", "snippet": "… To perform our evaluation, we compare synthesized line drawings with ones drawn by humans for reference shapes. Below, we describe our test datasets, evaluation measures, and …", "link": "http://openaccess.thecvf.com/content_CVPR_2020/html/Liu_Neural_Contours_Learning_to_Draw_Lines_From_3D_Shapes_CVPR_2020_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>… - Proceedings of the …, 2020 - openaccess.thecvf.com", "year": 2020, "citedBy": 45, "pdfUrl": "http://openaccess.thecvf.com/content_CVPR_2020/papers/Liu_Neural_Contours_Learning_to_Draw_Lines_From_3D_Shapes_CVPR_2020_paper.pdf"}, {"title": "Deepfacepencil: Creating face images from freehand sketches", "snippet": "… novel sketch-based face image synthesis framework that is robust to hand-drawn sketches. A new … In this paper, we present DeepFacePencil, a novel deep neural network which allows …", "link": "https://dl.acm.org/doi/abs/10.1145/3394171.3413684", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>… - Proceedings of the 28th …, 2020 - dl.acm.org", "year": 2020, "citedBy": 50, "pdfUrl": "https://arxiv.org/pdf/2008.13343"}, {"title": "Deep generation of face images from sketches", "snippet": "… We also propose another deep neural network to learn the mapping from the embedded … Our work is related to existing works for drawing assistance and conditional face generation…", "link": "https://arxiv.org/abs/2006.01047", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> Fu - arXiv preprint arXiv:2006.01047, 2020 - arxiv.org", "year": 2006, "citedBy": 195, "pdfUrl": "https://arxiv.org/pdf/2006.01047"}, {"title": "Clipdraw: Exploring text-to-drawing synthesis through language-image encoders", "snippet": "… Text-to-Image Synthesis. This work greatly draws from the field of text-to-image synthesis, … , CLIPDraw?”, the synthesized drawing contains a smiling face followed by text resembling …", "link": "https://proceedings.neurips.cc/paper_files/paper/2022/hash/21f76686538a5f06dc431efea5f475f5-Abstract-Conference.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Advances in Neural …, 2022 - proceedings.neurips.cc", "year": 2022, "citedBy": 228, "pdfUrl": "https://proceedings.neurips.cc/paper_files/paper/2022/file/21f76686538a5f06dc431efea5f475f5-Paper-Conference.pdf"}, {"title": "Toward realistic face photo–sketch synthesis via composition-aided GANs", "snippet": "… In this section, we take face sketch synthesis as an example to introduce our method. Our … Recall the quantitative evaluations shown in Table IV, we can safely draw the conclusion that …", "link": "https://ieeexplore.ieee.org/abstract/document/9025751/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE transactions on …, 2020 - ieeexplore.ieee.org", "year": 2020, "citedBy": 143, "pdfUrl": "https://arxiv.org/pdf/1712.00899"}, {"title": "dualface: Two-stage drawing guidance for freehand portrait sketching", "snippet": "… drawing interface to assist users with different levels of drawing skills to complete recognizable and authentic face … with other neural rendering approaches or a larger face database. We …", "link": "https://link.springer.com/article/10.1007/s41095-021-0227-7", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - Computational Visual …, 2022 - Springer", "year": 2022, "citedBy": 46, "pdfUrl": "https://link.springer.com/content/pdf/10.1007/s41095-021-0227-7.pdf"}, {"title": "3d-aware image synthesis via learning structural and textural representations", "snippet": "… synthesis draws wide attention recently [3,30,35]. An emerging solution is to integrate a Neural … [44] confirm that a face synthesis model is aware of the landmark positions of the output …", "link": "http://openaccess.thecvf.com/content/CVPR2022/html/Xu_3D-Aware_Image_Synthesis_via_Learning_Structural_and_Textural_Representations_CVPR_2022_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>… - Proceedings of the …, 2022 - openaccess.thecvf.com", "year": 2022, "citedBy": 144, "pdfUrl": "https://openaccess.thecvf.com/content/CVPR2022/papers/Xu_3D-Aware_Image_Synthesis_via_Learning_Structural_and_Textural_Representations_CVPR_2022_paper.pdf"}, {"title": "Learning dynamic facial radiance fields for few-shot talking head synthesis", "snippet": "… Additionally, for better modeling of the facial deformations, we propose a differentiable face … and draw the corresponding pixel information to guide the following synthesis and rendering. …", "link": "https://link.springer.com/chapter/10.1007/978-3-031-19775-8_39", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, J <PERSON> - European conference on …, 2022 - Springer", "year": 2022, "citedBy": 131, "pdfUrl": "https://arxiv.org/pdf/2207.11770"}, {"title": "DeepFaceEditing: Deep face generation and editing with disentangled geometry and appearance control", "snippet": "… , including neural face image synthesis, neural face image editing, neural image disentan… The editing module in our system enables users to draw faces via coarse or fine sketches. …", "link": "https://arxiv.org/abs/2105.08935", "publicationInfo": "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> - arXiv preprint arXiv …, 2021 - arxiv.org", "year": 2021, "citedBy": 74, "pdfUrl": "https://arxiv.org/pdf/2105.08935"}, {"title": "Learning to generate line drawings that convey geometry and semantics", "snippet": "… Furthermore, most methods draw lines in only one style, although Neural Stroke<PERSON> [54] … Apdrawinggan: Generating artistic portrait drawings from face photos with hierarchical gans. In …", "link": "http://openaccess.thecvf.com/content/CVPR2022/html/Chan_Learning_To_Generate_Line_Drawings_That_Convey_Geometry_and_Semantics_CVPR_2022_paper.html", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Proceedings of the IEEE/CVF …, 2022 - openaccess.thecvf.com", "year": 2022, "citedBy": 118, "pdfUrl": "http://openaccess.thecvf.com/content/CVPR2022/papers/Chan_Learning_To_Generate_Line_Drawings_That_Convey_Geometry_and_Semantics_CVPR_2022_paper.pdf"}, {"title": "Local and global perception generative adversarial network for facial expression synthesis", "snippet": "… face synthesis tasks without considering the characteristics of facial expressions, which are not appropriate to synthesize … synthesis have shown impressive results and drawn prevalent …", "link": "https://ieeexplore.ieee.org/abstract/document/9406832/", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - IEEE Transactions on …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 67, "pdfUrl": "https://researchportal.port.ac.uk/files/27474595/Local_and_Global_FE_Synthesis_TCSVT2021.pdf"}, {"title": "Optimal deep learning based convolution neural network for digital forensics face sketch synthesis in internet of things (IoT)", "snippet": "… Then, the ODL-CNN model draws the sketches of the input images following which it undergoes similarity assessment, with professional sketch being drawn as per the directions from …", "link": "https://link.springer.com/article/10.1007/s13042-020-01168-6", "publicationInfo": "<PERSON>, <PERSON><PERSON>, <PERSON> - International Journal of Machine …, 2021 - Springer", "year": 2021, "citedBy": 81, "pdfUrl": null}, {"title": "Deep neural network augmentation: Generating faces for affect analysis", "snippet": "… by drawing) that the synthesized sequence should follow. Then, we retrieve the mean faces of … We use each of these mean faces as the affect to be added on the 3D faces reconstructed …", "link": "https://link.springer.com/article/10.1007/s11263-020-01304-3", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>ia… - International Journal of …, 2020 - Springer", "year": 2020, "citedBy": 136, "pdfUrl": "https://link.springer.com/content/pdf/10.1007/s11263-020-01304-3.pdf"}, {"title": "Ide-3d: Interactive disentangled editing for high-resolution 3d-aware portrait synthesis", "snippet": "… -aware 3D face generator which supports interactive 3D face synthesis and local editing. … Interactive neural 3D face drawing and editing. IDE-3D supports interactive 3D face drawing …", "link": "https://dl.acm.org/doi/abs/10.1145/3550454.3555506", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> - ACM Transactions on …, 2022 - dl.acm.org", "year": 2022, "citedBy": 161, "pdfUrl": "https://dl.acm.org/doi/pdf/10.1145/3550454.3555506"}], "forensic sketch to photo AI": [{"title": "Crime investigation using DCGAN by forensic sketch-to-face transformation (STF)-a review", "snippet": "… technique of Artificial Intelligence for digitally ascertaining a criminal through facial recognition system by converting forensic sketch into a real photo using Deep Convolutional …", "link": "https://ieeexplore.ieee.org/abstract/document/9418417/", "publicationInfo": "<PERSON><PERSON> Bush<PERSON>, KU Maheswari - 2021 5th International Conference …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 24, "pdfUrl": "https://www.researchgate.net/profile/Nikkath-Bushra-2/publication/351377689_Crime_Investigation_using_DCGAN_by_Forensic_Sketch-to-Face_Transformation_STF-_A_Review/links/62fde4bbaa4b1206fabad575/Crime-Investigation-using-DCGAN-by-Forensic-Sketch-to-Face-Transformation-STF-A-Review.pdf"}, {"title": "Forensic Sketch to Real Image", "snippet": "… Deep Sketch-to-Photo Matching Enforcing Realistic Photo Generation: This paper introduces an end-to-end approach for matching sketches to photos … artificial intelligence for forensic …", "link": "https://www.researchgate.net/profile/<PERSON><PERSON><PERSON>-Kute-3/publication/381584580_Forensic_Sketch_to_Real_Image/links/66755c981dec0c3c6f986f68/Forensic-Sketch-to-Real-Image.pdf", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, A <PERSON>hagat - 2024 - researchgate.net", "year": 2024, "citedBy": 1, "pdfUrl": "https://www.researchgate.net/profile/<PERSON><PERSON><PERSON>-Kute-3/publication/381584580_Forensic_Sketch_to_Real_Image/links/66755c981dec0c3c6f986f68/Forensic-Sketch-to-Real-Image.pdf"}, {"title": "Deep Learning Model for Digital Forensics Face Sketch Synthesis", "snippet": "… sketch drawings based on retrieved similar photos However, the considerable disparity between sketches and photos… future The integration of explainable AI techniques is envisioned to …", "link": "https://www.taylorfrancis.com/chapters/edit/10.1201/9781003207573-9/deep-learning-model-digital-forensics-face-sketch-synthesis-eshita-badwa-sun<PERSON>-singh-su<PERSON><PERSON>-kumar-ayu<PERSON>-vanshi<PERSON>-chilkoti-varsha-arya-kwok-tai-chui", "publicationInfo": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Digital Forensics and …, 2024 - taylorfrancis.com", "year": 2024, "citedBy": 1, "pdfUrl": null}, {"title": "Everyone is A Forensic Artist: Sketch-to-Photo Transformation for Human Face", "snippet": "… propose the Forensic GAN, a network of performing the sketch-to-photo transformation and … The performance of the Forensic GAN was tested, and its potential for the real forensic task …", "link": "https://ieeexplore.ieee.org/abstract/document/9574719/", "publicationInfo": "<PERSON>, BS Lin - 2021 IEEE 4th International Conference on …, 2021 - ieeexplore.ieee.org", "year": 2021, "citedBy": 1, "pdfUrl": null}, {"title": "AI-Powered Face Sketching for Criminal Identification", "snippet": "… While the CelebA dataset offers a plethora of photos with … to our user-friendly Criminal Sketch Generator. By providing … in AI-driven facial design to support forensic technology and …", "link": "https://link.springer.com/chapter/10.1007/978-***********-3_35", "publicationInfo": "<PERSON>, <PERSON>, <PERSON><PERSON>… - … on Intelligent Computing …, 2024 - Springer", "year": 2024, "citedBy": null, "pdfUrl": null}, {"title": "Forensic sketch to real image using dcgan", "snippet": "… AI-based facial recognition is a tearing technology that can keep up with … Photos demonstrating our method have proven to be more useful in enhancing the accuracy of forensic sketch …", "link": "https://www.sciencedirect.com/science/article/pii/S1877050923001394", "publicationInfo": "<PERSON>, G Sarath - Procedia Computer Science, 2023 - Elsevier", "year": 2023, "citedBy": 14, "pdfUrl": "https://www.sciencedirect.com/science/article/pii/S1877050923001394/pdf?md5=ce39ccb5069da16655681702fea52203&pid=1-s2.0-S1877050923001394-main.pdf"}, {"title": "Integration of Generative Adversarial Networks (GAN) and AI Drawing in Criminal Sketches—Applied in Crime Scene Investigation by Law Enforcement", "snippet": "… Forensic GAN, integrating CycleGAN to transform facial sketches into realistic photos and … All these approaches require pre-existing photo data for operations. Limited research has …", "link": "https://link.springer.com/chapter/10.1007/978-***********-3_33", "publicationInfo": "<PERSON><PERSON>, J <PERSON> - International Conference on Innovative Computing, 2024 - Springer", "year": 2024, "citedBy": null, "pdfUrl": null}, {"title": "Translation from sketch to realistic photo based on CycleGAN", "snippet": "… Forensic sketches serve as crucial tools for law enforcement agencies in identifying … To solve this problem, many methods that use AI to generate pictures that work better than …", "link": "https://pdfs.semanticscholar.org/b9ce/b9181e69abe41a5585f1cdc4354f3e222881.pdf", "publicationInfo": "X Yuan - Applied and Computational Engineering, 2024 - pdfs.semanticscholar.org", "year": 2024, "citedBy": null, "pdfUrl": "https://pdfs.semanticscholar.org/b9ce/b9181e69abe41a5585f1cdc4354f3e222881.pdf"}, {"title": "AI-DRIVEN CRIMINAL SUSPECT IDENTIFICATION AND SKETCH GENERATION", "snippet": "… sketch creation, 3D modeling, and real-time collaboration. The chosen solution, Alternative 3 - AI-powered Criminal Suspect Identification and Sketch … Examples demonstrate how the AI …", "link": "http://repository.president.ac.id/handle/123456789/12921", "publicationInfo": "GRH Sevaca - 2024 - repository.president.ac.id", "year": 2024, "citedBy": null, "pdfUrl": "http://repository.president.ac.id/bitstream/handle/123456789/12921/cover_001202100058.pdf?sequence=1&isAllowed=y"}, {"title": "Photo-Synthesis: Attributed controlled sketch to image translation using Generative Adversarial Networks for assisting criminal identification", "snippet": "… forensics domain. For example, the Clearview AI system is able to match an uploaded photo … On the contrary, Photo-synthesis generates images from forensic sketches without any text …", "link": "https://assets-eu.researchsquare.com/files/rs-3913145/v1_covered_26ca4024-c7c8-41bd-a142-245ae46bebe2.pdf", "publicationInfo": "S Arora, N Bhatia, HS Pannu, S Setia - 2024 - assets-eu.researchsquare.com", "year": 2024, "citedBy": null, "pdfUrl": "https://assets-eu.researchsquare.com/files/rs-3913145/v1_covered_26ca4024-c7c8-41bd-a142-245ae46bebe2.pdf"}, {"title": "Detection of human face from sketches using deep learning networks", "snippet": "… 2.1 AI and deep learning The field of Artificial Intelligence (AI) is basically when machines or … to retrieval photos using hand-made sketches. Based on the collection of 188 pairs of …", "link": "https://core.ac.uk/download/pdf/573844421.pdf", "publicationInfo": "M Abbey - 2020 - core.ac.uk", "year": 2020, "citedBy": null, "pdfUrl": "https://core.ac.uk/download/pdf/573844421.pdf"}, {"title": "Forensic sketch-to-photo transformation with improved Generative Adversarial Network (GAN)", "snippet": "… In this paper, the discriminator inside a GAN for face sketch to photo translation employs … -drawn sketch-photo pairings available in the CUHK dataset and constructed 1000 sketch-photo …", "link": "https://ieeexplore.ieee.org/abstract/document/10029068/", "publicationInfo": "<PERSON>, <PERSON> Umar - 2022 5th International Conference on …, 2022 - ieeexplore.ieee.org", "year": 2022, "citedBy": 1, "pdfUrl": null}, {"title": "Investigating the Efficacy of Forensic Facial Reconstructions: A Dual Approach Using Sketches and CCTV Images", "snippet": "… science, driven by advancements in artificial intelligence and … advancements in matching forensic sketches to photographs… in transforming forensic sketches into high-resolution photos, …", "link": "https://ieeexplore.ieee.org/abstract/document/10910684/", "publicationInfo": "<PERSON><PERSON>, SS Kadagadkai - 2024 IEEE 3rd International …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": null, "pdfUrl": null}, {"title": "An investigation of crime detection using artificial intelligence and face sketch synthesis", "snippet": "… forensic sketch artists as experts in sketch analysis is limited by the need for an expert to create the sketch… Six hundred and six pairs of face photo–sketch samples are included in the …", "link": "https://www.tandfonline.com/doi/abs/10.1080/19361610.2024.2302237", "publicationInfo": "<PERSON>, <PERSON>, <PERSON> - Journal of Applied …, 2024 - Taylor & Francis", "year": 2024, "citedBy": 5, "pdfUrl": null}, {"title": "SketchScan: Face Sketch Illustration and Identification using Deep Learning", "snippet": "… in forensic investigations, especially to turn forensic sketches … generate realistic photo-like illustrations from facial sketches. … the gap between sketch and photo modalities, facilitating …", "link": "https://ieeexplore.ieee.org/abstract/document/10687391/", "publicationInfo": "<PERSON> <PERSON>, <PERSON>, <PERSON>… - … (OTCON) on Smart …, 2024 - ieeexplore.ieee.org", "year": 2024, "citedBy": 1, "pdfUrl": null}]}