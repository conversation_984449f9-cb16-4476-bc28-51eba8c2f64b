import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  User, 
  Badge,
  FileText,
  Brain,
  Download,
  Share,
  ZoomIn,
  ArrowLeftRight,
  Target,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  Activity,
  Phone,
  CreditCard,
  Settings,
  Eye
} from 'lucide-react'
import { Caso } from '../App'

interface CasoDetalleProps {
  casos: Caso[]
}

export default function CasoDetalle({ casos }: CasoDetalleProps) {
  const { id } = useParams<{ id: string }>()
  const [imageModalOpen, setImageModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [modoComparacion, setModoComparacion] = useState(false)

  const caso = casos.find(c => c.id === id)

  if (!caso) {
    return (
      <div className="container mx-auto px-6 py-8 text-center">
        <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">Caso No Encontrado</h2>
        <p className="text-gray-400 mb-6">El caso con ID {id} no existe en el sistema.</p>
        <Link
          to="/galeria"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md inline-flex items-center space-x-2 transition-colors"
        >
          <ArrowLeft size={20} />
          <span>Volver a Galería</span>
        </Link>
      </div>
    )
  }

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'activo': return <Activity className="w-5 h-5" />
      case 'pendiente': return <Clock className="w-5 h-5" />
      case 'resuelto': return <CheckCircle className="w-5 h-5" />
      default: return <FileText className="w-5 h-5" />
    }
  }

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'activo': return 'text-green-400 bg-green-900/30 border-green-700'
      case 'pendiente': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'resuelto': return 'text-blue-400 bg-blue-900/30 border-blue-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const getPrioridadColor = (prioridad: string) => {
    switch (prioridad) {
      case 'muy_alta': return 'text-red-400 bg-red-900/30 border-red-700'
      case 'alta': return 'text-orange-400 bg-orange-900/30 border-orange-700'
      case 'media': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'baja': return 'text-green-400 bg-green-900/30 border-green-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-PE', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFechaCorta = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-PE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleImageClick = (imageUrl: string) => {
    setSelectedImage(imageUrl)
    setImageModalOpen(true)
  }

  const compartirCaso = () => {
    if (navigator.share) {
      navigator.share({
        title: `Caso ${caso.id} - ${caso.titulo}`,
        text: caso.descripcion,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      alert('URL copiada al portapapeles')
    }
  }

  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/galeria"
            className="p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-md transition-colors"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center space-x-3">
              <span>Caso {caso.id}</span>
            </h1>
            <p className="text-gray-400">{caso.titulo}</p>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={compartirCaso}
            className="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
          >
            <Share size={20} />
            <span>Compartir</span>
          </button>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
            <Download size={20} />
            <span>Exportar</span>
          </button>
        </div>
      </div>

      {/* Estado y Prioridad */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <span className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium border ${getEstadoColor(caso.estado)}`}>
            {getEstadoIcon(caso.estado)}
            <span className="ml-2">{caso.estado.toUpperCase()}</span>
          </span>
          <span className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium border ${getPrioridadColor(caso.prioridad)}`}>
            <AlertTriangle className="w-4 h-4 mr-2" />
            PRIORIDAD {caso.prioridad.replace('_', ' ').toUpperCase()}
          </span>
          <span className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium border text-purple-400 bg-purple-900/30 border-purple-700">
            <Brain className="w-4 h-4 mr-2" />
            PROCESADO CON IA
          </span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Información del Caso */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-blue-400" />
              Información del Caso
            </h3>
            <div className="space-y-3 text-gray-300">
              <div className="flex items-start">
                <Calendar className="w-5 h-5 mr-3 mt-0.5 text-blue-400" />
                <div>
                  <p className="font-medium">Fecha del Incidente</p>
                  <p className="text-sm text-gray-400">{formatFecha(caso.fecha)}</p>
                </div>
              </div>
              <div className="flex items-start">
                <MapPin className="w-5 h-5 mr-3 mt-0.5 text-green-400" />
                <div>
                  <p className="font-medium">Ubicación</p>
                  <p className="text-sm text-gray-400">{caso.ubicacion}</p>
                  <p className="text-xs text-gray-500">{caso.ubicacionExacta}</p>
                </div>
              </div>
              <div className="flex items-start">
                <Badge className="w-5 h-5 mr-3 mt-0.5 text-yellow-400" />
                <div>
                  <p className="font-medium">Tipo de Delito</p>
                  <p className="text-sm text-gray-400">{caso.tipoDelito.replace('_', ' ').toUpperCase()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Personal Asignado */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <User className="w-5 h-5 mr-2 text-green-400" />
              Personal Asignado
            </h3>
            <div className="space-y-4">
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Oficial a Cargo</h4>
                <div className="space-y-1 text-sm">
                  <p className="text-gray-300">{caso.oficial.nombre}</p>
                  <p className="text-gray-400">{caso.oficial.rango} - Placa {caso.oficial.placa}</p>
                  <p className="text-gray-400">{caso.oficial.unidad}</p>
                  <p className="text-gray-400 flex items-center">
                    <Phone className="w-3 h-3 mr-1" />
                    {caso.oficial.telefono}
                  </p>
                </div>
              </div>
              <div className="bg-slate-700/50 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Analista Forense</h4>
                <div className="space-y-1 text-sm">
                  <p className="text-gray-300">{caso.analista.nombre}</p>
                  <p className="text-gray-400">{caso.analista.especialidad}</p>
                  <p className="text-gray-400">Código: {caso.analista.codigo}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Descripción */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <h3 className="text-lg font-semibold text-white mb-4">Descripción del Incidente</h3>
        <p className="text-gray-300 leading-relaxed">{caso.descripcion}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Comparación de Imágenes */}
        <div className="lg:col-span-2">
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <Eye className="w-5 h-5 mr-2 text-purple-400" />
                Comparación: Boceto vs IA Fotorrealista
              </h3>
              <button
                onClick={() => setModoComparacion(!modoComparacion)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
              >
                <ArrowLeftRight size={16} />
                <span>Alternar Vista</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Boceto Original */}
              <div className="space-y-3">
                <h4 className="text-white font-medium flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-blue-400" />
                  Boceto Original
                </h4>
                <div className="relative group">
                  <img
                    src={caso.imagenOriginal}
                    alt="Boceto original"
                    className="w-full rounded-lg border border-slate-600 cursor-pointer transition-transform group-hover:scale-105"
                    onClick={() => handleImageClick(caso.imagenOriginal)}
                  />
                  <button
                    onClick={() => handleImageClick(caso.imagenOriginal)}
                    className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 rounded-lg"
                  >
                    <ZoomIn className="w-8 h-8 text-white" />
                  </button>
                </div>
                <p className="text-sm text-gray-400">Identikit elaborado por testigo</p>
              </div>

              {/* Imagen Procesada por IA */}
              <div className="space-y-3">
                <h4 className="text-white font-medium flex items-center">
                  <Brain className="w-4 h-4 mr-2 text-purple-400" />
                  Imagen Fotorrealista IA
                </h4>
                <div className="relative group">
                  <img
                    src={caso.imagenProcesada}
                    alt="Imagen procesada por IA"
                    className="w-full rounded-lg border border-slate-600 cursor-pointer transition-transform group-hover:scale-105"
                    onClick={() => handleImageClick(caso.imagenProcesada)}
                  />
                  <button
                    onClick={() => handleImageClick(caso.imagenProcesada)}
                    className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 rounded-lg"
                  >
                    <ZoomIn className="w-8 h-8 text-white" />
                  </button>
                  <div className="absolute top-3 right-3 bg-purple-600/90 backdrop-blur-sm rounded-lg px-2 py-1">
                    <span className="text-white text-xs font-bold">
                      {caso.parametrosIA.precision}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-400">Procesada con {caso.parametrosIA.modelo}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Panel Lateral */}
        <div className="space-y-6">
          {/* Parámetros de IA */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2 text-purple-400" />
              Parámetros IA
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Modelo:</span>
                <span className="text-white font-medium">{caso.parametrosIA.modelo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Calidad:</span>
                <span className="text-white font-medium">{caso.parametrosIA.calidad}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Resolución:</span>
                <span className="text-white font-medium">{caso.parametrosIA.resolution}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Steps:</span>
                <span className="text-white font-medium">{caso.parametrosIA.steps}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Guidance:</span>
                <span className="text-white font-medium">{caso.parametrosIA.guidance}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Seed:</span>
                <span className="text-white font-medium">{caso.parametrosIA.seed}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-slate-600">
                <span className="text-gray-400 flex items-center">
                  <Target className="w-3 h-3 mr-1" />
                  Precisión:
                </span>
                <span className="text-purple-400 font-bold">{caso.parametrosIA.precision}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400 flex items-center">
                  <Zap className="w-3 h-3 mr-1" />
                  Tiempo:
                </span>
                <span className="text-yellow-400 font-medium">{caso.parametrosIA.tiempoProcesamiento}</span>
              </div>
            </div>
          </div>

          {/* Información del Testigo */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <CreditCard className="w-5 h-5 mr-2 text-green-400" />
              Testigo
            </h3>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-gray-400">Nombre:</span>
                <p className="text-white font-medium">{caso.testigo.nombre}</p>
              </div>
              <div>
                <span className="text-gray-400">Edad:</span>
                <p className="text-gray-300">{caso.testigo.edad} años</p>
              </div>
              <div>
                <span className="text-gray-400">DNI:</span>
                <p className="text-gray-300 font-mono">{caso.testigo.dni}</p>
              </div>
              <div>
                <span className="text-gray-400">Ocupación:</span>
                <p className="text-gray-300">{caso.testigo.ocupacion}</p>
              </div>
              <div>
                <span className="text-gray-400">Teléfono:</span>
                <p className="text-gray-300 font-mono">{caso.testigo.telefono}</p>
              </div>
            </div>
          </div>

          {/* Descripción del Sospechoso */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-semibold text-white mb-4">Descripción del Sospechoso</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              {caso.testigo.descripcionSospechoso}
            </p>
          </div>
        </div>
      </div>

      {/* Timeline */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-blue-400" />
          Timeline del Caso
        </h3>
        <div className="space-y-4">
          {caso.timeline.map((evento, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">{index + 1}</span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3 mb-1">
                  <h4 className="text-white font-medium">{evento.evento}</h4>
                  <span className="text-gray-400 text-sm">{formatFechaCorta(evento.fecha)}</span>
                </div>
                <p className="text-gray-300 text-sm">{evento.descripcion}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Evidencias y Seguimiento */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
          <h3 className="text-lg font-semibold text-white mb-4">Evidencias</h3>
          <ul className="space-y-2">
            {caso.evidencias.map((evidencia, index) => (
              <li key={index} className="flex items-center text-gray-300 text-sm">
                <CheckCircle className="w-4 h-4 mr-3 text-green-400 flex-shrink-0" />
                {evidencia}
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
          <h3 className="text-lg font-semibold text-white mb-4">Estado del Seguimiento</h3>
          <p className="text-gray-300 text-sm leading-relaxed">
            {caso.seguimiento}
          </p>
        </div>
      </div>

      {/* Modal de Imagen */}
      {imageModalOpen && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setImageModalOpen(false)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <span className="text-2xl">✕</span>
            </button>
            <img
              src={selectedImage}
              alt="Imagen ampliada"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}
    </div>
  )
}
