{"version": 3, "names": ["_OverloadYield", "require", "_wrapAsyncGenerator", "fn", "AsyncGenerator", "apply", "arguments", "gen", "front", "back", "send", "key", "arg", "Promise", "resolve", "reject", "request", "next", "resume", "result", "value", "overloaded", "OverloadYield", "v", "then", "<PERSON><PERSON><PERSON>", "k", "done", "settle", "err", "type", "_invoke", "undefined", "prototype", "Symbol", "asyncIterator"], "sources": ["../../src/helpers/wrapAsyncGenerator.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport <PERSON>load<PERSON><PERSON> from \"./OverloadYield.ts\";\n\nexport default function _wrapAsyncGenerator(fn: GeneratorFunction) {\n  return function (this: any) {\n    // Use \"arguments\" here for better compatibility and smaller bundle size\n    return new AsyncGenerator(fn.apply(this, arguments as any));\n  };\n}\n\n/* == The implementation of the AsyncGenerator class == */\n\ntype AsyncIteratorMethod = \"next\" | \"throw\" | \"return\";\n\ndeclare class AsyncGenerator<T = unknown, TReturn = any, TNext = unknown>\n  implements globalThis.AsyncGenerator<T, TReturn, TNext>\n{\n  _invoke: (\n    key: AsyncIteratorMethod,\n    arg: IteratorResult<T>,\n  ) => Promise<IteratorResult<T, TReturn>>;\n\n  constructor(gen: Generator<T, TReturn, TNext>);\n\n  next(...args: [] | [TNext]): Promise<IteratorResult<T, TReturn>>;\n  return(\n    value: TReturn | PromiseLike<TReturn>,\n  ): Promise<IteratorResult<T, TReturn>>;\n  throw(e: any): Promise<IteratorResult<T, TReturn>>;\n  [Symbol.asyncIterator](): AsyncGenerator<T, TReturn, TNext>;\n  [Symbol.asyncDispose](): Promise<void>;\n}\n\ninterface AsyncGeneratorRequest<T = unknown, TReturn = any, TNext = unknown> {\n  key: AsyncIteratorMethod;\n  arg: IteratorResult<T>;\n  resolve: (value: IteratorResult<T, TReturn>) => void;\n  reject: (error: any) => void;\n  next: AsyncGeneratorRequest<T, TReturn, TNext> | null;\n}\n\nfunction AsyncGenerator<T = unknown, TReturn = any, TNext = unknown>(\n  this: AsyncGenerator<T, TReturn, TNext>,\n  gen: Generator<T, TReturn, TNext>,\n) {\n  var front: AsyncGeneratorRequest<T, TReturn, TNext> | null,\n    back: AsyncGeneratorRequest<T, TReturn, TNext> | null;\n\n  function send(key: AsyncIteratorMethod, arg: IteratorResult<T>) {\n    return new Promise<IteratorResult<T, TReturn>>(function (resolve, reject) {\n      var request: AsyncGeneratorRequest<T, TReturn, TNext> = {\n        key: key,\n        arg: arg,\n        resolve: resolve,\n        reject: reject,\n        next: null,\n      };\n\n      if (back) {\n        back = back.next = request;\n      } else {\n        front = back = request;\n        resume(key, arg);\n      }\n    });\n  }\n\n  function resume(key: AsyncIteratorMethod, arg: IteratorResult<T, TReturn>) {\n    try {\n      var result = gen[key](arg);\n      var value = result.value;\n      var overloaded = value instanceof OverloadYield;\n\n      Promise.resolve(\n        overloaded ? (value as OverloadYield<T | TReturn>).v : value,\n      ).then(\n        function (arg: any) {\n          if (overloaded) {\n            // Overloaded yield requires calling into the generator twice:\n            //  - first we get the iterator result wrapped in a promise\n            //    (the gen[key](arg) call above)\n            //  - then we await it (the Promise.resolve call above)\n            //  - then we give the result back to the iterator, so that it can:\n            //    * if it was an await, use its result\n            //    * if it was a yield*, possibly return the `done: true` signal\n            //      so that yield* knows that the iterator is finished.\n            //      This needs to happen in the second call, because in the\n            //      first one `done: true` was hidden in the promise and thus\n            //      not visible to the (sync) yield*.\n            //      The other part of this implementation is in asyncGeneratorDelegate.\n            var nextKey: \"return\" | \"next\" =\n              key === \"return\" ? \"return\" : \"next\";\n            if (\n              !(value as OverloadYield<IteratorReturnResult<T>>).k ||\n              arg.done\n            ) {\n              // await or end of yield*\n              // eslint-disable-next-line @typescript-eslint/no-confusing-void-expression -- smaller bundle size\n              return resume(nextKey, arg);\n            } else {\n              // yield*, not done\n              arg = gen[nextKey](arg).value;\n            }\n          }\n\n          settle(result.done ? \"return\" : \"normal\", arg);\n        },\n        function (err) {\n          resume(\"throw\", err);\n        },\n      );\n    } catch (err) {\n      settle(\"throw\", err);\n    }\n  }\n\n  function settle(type: AsyncIteratorMethod | \"normal\", value: any) {\n    switch (type) {\n      case \"return\":\n        front!.resolve({ value: value, done: true });\n        break;\n      case \"throw\":\n        front!.reject(value);\n        break;\n      default:\n        front!.resolve({ value: value, done: false });\n        break;\n    }\n\n    front = front!.next;\n    if (front) {\n      resume(front.key, front.arg);\n    } else {\n      back = null;\n    }\n  }\n\n  this._invoke = send;\n\n  // Hide \"return\" method if generator return is not supported\n  if (typeof gen[\"return\"] !== \"function\") {\n    // @ts-expect-error -- intentionally remove \"return\" when not supported\n    this[\"return\"] = undefined;\n  }\n}\n\nAsyncGenerator.prototype[\n  ((typeof Symbol === \"function\" && Symbol.asyncIterator) ||\n    \"@@asyncIterator\") as typeof Symbol.asyncIterator\n] = function () {\n  return this;\n};\n\nAsyncGenerator.prototype.next = function (arg: IteratorResult<any>) {\n  return this._invoke(\"next\", arg);\n};\nAsyncGenerator.prototype[\"throw\"] = function (arg: IteratorResult<any>) {\n  return this._invoke(\"throw\", arg);\n};\nAsyncGenerator.prototype[\"return\"] = function (arg: IteratorResult<any>) {\n  return this._invoke(\"return\", arg);\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAEe,SAASC,mBAAmBA,CAACC,EAAqB,EAAE;EACjE,OAAO,YAAqB;IAE1B,OAAO,IAAIC,cAAc,CAACD,EAAE,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAgB,CAAC,CAAC;EAC7D,CAAC;AACH;AAiCA,SAASF,cAAcA,CAErBG,GAAiC,EACjC;EACA,IAAIC,KAAsD,EACxDC,IAAqD;EAEvD,SAASC,IAAIA,CAACC,GAAwB,EAAEC,GAAsB,EAAE;IAC9D,OAAO,IAAIC,OAAO,CAA6B,UAAUC,OAAO,EAAEC,MAAM,EAAE;MACxE,IAAIC,OAAiD,GAAG;QACtDL,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA,GAAG;QACRE,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA,MAAM;QACdE,IAAI,EAAE;MACR,CAAC;MAED,IAAIR,IAAI,EAAE;QACRA,IAAI,GAAGA,IAAI,CAACQ,IAAI,GAAGD,OAAO;MAC5B,CAAC,MAAM;QACLR,KAAK,GAAGC,IAAI,GAAGO,OAAO;QACtBE,MAAM,CAACP,GAAG,EAAEC,GAAG,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;EAEA,SAASM,MAAMA,CAACP,GAAwB,EAAEC,GAA+B,EAAE;IACzE,IAAI;MACF,IAAIO,MAAM,GAAGZ,GAAG,CAACI,GAAG,CAAC,CAACC,GAAG,CAAC;MAC1B,IAAIQ,KAAK,GAAGD,MAAM,CAACC,KAAK;MACxB,IAAIC,UAAU,GAAGD,KAAK,YAAYE,sBAAa;MAE/CT,OAAO,CAACC,OAAO,CACbO,UAAU,GAAID,KAAK,CAAgCG,CAAC,GAAGH,KACzD,CAAC,CAACI,IAAI,CACJ,UAAUZ,GAAQ,EAAE;QAClB,IAAIS,UAAU,EAAE;UAad,IAAII,OAA0B,GAC5Bd,GAAG,KAAK,QAAQ,GAAG,QAAQ,GAAG,MAAM;UACtC,IACE,CAAES,KAAK,CAA4CM,CAAC,IACpDd,GAAG,CAACe,IAAI,EACR;YAGA,OAAOT,MAAM,CAACO,OAAO,EAAEb,GAAG,CAAC;UAC7B,CAAC,MAAM;YAELA,GAAG,GAAGL,GAAG,CAACkB,OAAO,CAAC,CAACb,GAAG,CAAC,CAACQ,KAAK;UAC/B;QACF;QAEAQ,MAAM,CAACT,MAAM,CAACQ,IAAI,GAAG,QAAQ,GAAG,QAAQ,EAAEf,GAAG,CAAC;MAChD,CAAC,EACD,UAAUiB,GAAG,EAAE;QACbX,MAAM,CAAC,OAAO,EAAEW,GAAG,CAAC;MACtB,CACF,CAAC;IACH,CAAC,CAAC,OAAOA,GAAG,EAAE;MACZD,MAAM,CAAC,OAAO,EAAEC,GAAG,CAAC;IACtB;EACF;EAEA,SAASD,MAAMA,CAACE,IAAoC,EAAEV,KAAU,EAAE;IAChE,QAAQU,IAAI;MACV,KAAK,QAAQ;QACXtB,KAAK,CAAEM,OAAO,CAAC;UAAEM,KAAK,EAAEA,KAAK;UAAEO,IAAI,EAAE;QAAK,CAAC,CAAC;QAC5C;MACF,KAAK,OAAO;QACVnB,KAAK,CAAEO,MAAM,CAACK,KAAK,CAAC;QACpB;MACF;QACEZ,KAAK,CAAEM,OAAO,CAAC;UAAEM,KAAK,EAAEA,KAAK;UAAEO,IAAI,EAAE;QAAM,CAAC,CAAC;QAC7C;IACJ;IAEAnB,KAAK,GAAGA,KAAK,CAAES,IAAI;IACnB,IAAIT,KAAK,EAAE;MACTU,MAAM,CAACV,KAAK,CAACG,GAAG,EAAEH,KAAK,CAACI,GAAG,CAAC;IAC9B,CAAC,MAAM;MACLH,IAAI,GAAG,IAAI;IACb;EACF;EAEA,IAAI,CAACsB,OAAO,GAAGrB,IAAI;EAGnB,IAAI,OAAOH,GAAG,CAAC,QAAQ,CAAC,KAAK,UAAU,EAAE;IAEvC,IAAI,CAAC,QAAQ,CAAC,GAAGyB,SAAS;EAC5B;AACF;AAEA5B,cAAc,CAAC6B,SAAS,CACpB,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,aAAa,IACpD,iBAAiB,CACpB,GAAG,YAAY;EACd,OAAO,IAAI;AACb,CAAC;AAED/B,cAAc,CAAC6B,SAAS,CAAChB,IAAI,GAAG,UAAUL,GAAwB,EAAE;EAClE,OAAO,IAAI,CAACmB,OAAO,CAAC,MAAM,EAAEnB,GAAG,CAAC;AAClC,CAAC;AACDR,cAAc,CAAC6B,SAAS,CAAC,OAAO,CAAC,GAAG,UAAUrB,GAAwB,EAAE;EACtE,OAAO,IAAI,CAACmB,OAAO,CAAC,OAAO,EAAEnB,GAAG,CAAC;AACnC,CAAC;AACDR,cAAc,CAAC6B,SAAS,CAAC,QAAQ,CAAC,GAAG,UAAUrB,GAAwB,EAAE;EACvE,OAAO,IAAI,CAACmB,OAAO,CAAC,QAAQ,EAAEnB,GAAG,CAAC;AACpC,CAAC", "ignoreList": []}