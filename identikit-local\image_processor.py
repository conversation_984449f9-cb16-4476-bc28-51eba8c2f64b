"""
Módulo de procesamiento de imágenes para Sistema Identikit Local
Algoritmos offline para mejora y conversión de bocetos
"""

import os
import numpy as np
from PIL import Image, ImageFilter, ImageEnhance, ImageOps, ImageDraw
import cv2

def process_sketch_to_photo(input_path, output_path):
    """
    Procesar boceto y convertir a imagen fotorrealista usando técnicas offline
    
    Args:
        input_path (str): Ruta del boceto original
        output_path (str): Ruta donde guardar la imagen procesada
    
    Returns:
        bool: True si el procesamiento fue exitoso
    """
    try:
        # Cargar imagen
        image = Image.open(input_path)
        
        # Convertir a RGB si es necesario
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Redimensionar a tamaño estándar
        target_size = (512, 512)
        image = image.resize(target_size, Image.Resampling.LANCZOS)
        
        # Aplicar pipeline de procesamiento
        processed_image = enhance_sketch_pipeline(image)
        
        # Guardar resultado
        processed_image.save(output_path, 'JPEG', quality=95)
        
        print(f"✅ Imagen procesada exitosamente: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error procesando imagen: {e}")
        return False

def enhance_sketch_pipeline(image):
    """
    Pipeline completo de mejora de bocetos
    
    Args:
        image (PIL.Image): Imagen de entrada
    
    Returns:
        PIL.Image: Imagen mejorada
    """
    # 1. Normalizar contraste
    image = normalize_contrast(image)
    
    # 2. Reducir ruido
    image = reduce_noise(image)
    
    # 3. Mejorar bordes
    image = enhance_edges(image)
    
    # 4. Aplicar suavizado selectivo
    image = selective_smoothing(image)
    
    # 5. Ajustar tonos para apariencia más fotorrealista
    image = adjust_photorealistic_tones(image)
    
    # 6. Aplicar filtro de acabado
    image = apply_finishing_filter(image)
    
    return image

def normalize_contrast(image):
    """Normalizar contraste de la imagen"""
    # Convertir a array numpy
    img_array = np.array(image)
    
    # Aplicar ecualización de histograma por canal
    img_yuv = cv2.cvtColor(img_array, cv2.COLOR_RGB2YUV)
    img_yuv[:,:,0] = cv2.equalizeHist(img_yuv[:,:,0])
    img_rgb = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2RGB)
    
    return Image.fromarray(img_rgb)

def reduce_noise(image):
    """Reducir ruido manteniendo detalles importantes"""
    # Aplicar filtro bilateral para reducir ruido preservando bordes
    img_array = np.array(image)
    denoised = cv2.bilateralFilter(img_array, 9, 75, 75)
    
    return Image.fromarray(denoised)

def enhance_edges(image):
    """Mejorar definición de bordes"""
    # Detectar bordes con Canny
    img_gray = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2GRAY)
    edges = cv2.Canny(img_gray, 50, 150)
    
    # Crear máscara de bordes
    edges_colored = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
    
    # Combinar con imagen original
    img_array = np.array(image)
    enhanced = cv2.addWeighted(img_array, 0.8, edges_colored, 0.2, 0)
    
    return Image.fromarray(enhanced)

def selective_smoothing(image):
    """Aplicar suavizado selectivo para apariencia más natural"""
    # Convertir a PIL para filtros
    # Aplicar filtro gaussiano suave
    smoothed = image.filter(ImageFilter.GaussianBlur(radius=0.5))
    
    # Mezclar con original
    return Image.blend(image, smoothed, 0.3)

def adjust_photorealistic_tones(image):
    """Ajustar tonos para apariencia más fotorrealista"""
    # Mejorar brillo y contraste
    enhancer = ImageEnhance.Brightness(image)
    image = enhancer.enhance(1.1)
    
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(1.2)
    
    # Ajustar saturación ligeramente
    enhancer = ImageEnhance.Color(image)
    image = enhancer.enhance(1.1)
    
    return image

def apply_finishing_filter(image):
    """Aplicar filtro de acabado final"""
    # Aplicar un ligero desenfoque y nitidez para suavizar
    image = image.filter(ImageFilter.UnsharpMask(radius=1, percent=120, threshold=3))
    
    return image

def enhance_image(input_path, output_path, enhancement_type='auto'):
    """
    Mejorar imagen con diferentes tipos de procesamiento
    
    Args:
        input_path (str): Ruta de imagen original
        output_path (str): Ruta de salida
        enhancement_type (str): Tipo de mejora ('auto', 'contrast', 'brightness', 'sharpness')
    
    Returns:
        bool: True si fue exitoso
    """
    try:
        image = Image.open(input_path)
        
        if enhancement_type == 'auto':
            # Mejora automática
            enhanced = auto_enhance(image)
        elif enhancement_type == 'contrast':
            enhancer = ImageEnhance.Contrast(image)
            enhanced = enhancer.enhance(1.5)
        elif enhancement_type == 'brightness':
            enhancer = ImageEnhance.Brightness(image)
            enhanced = enhancer.enhance(1.2)
        elif enhancement_type == 'sharpness':
            enhancer = ImageEnhance.Sharpness(image)
            enhanced = enhancer.enhance(1.3)
        else:
            enhanced = image
        
        enhanced.save(output_path, quality=95)
        return True
        
    except Exception as e:
        print(f"Error mejorando imagen: {e}")
        return False

def auto_enhance(image):
    """Mejora automática de imagen"""
    # Aplicar mejoras automáticas
    enhanced = ImageOps.autocontrast(image)
    enhanced = ImageOps.equalize(enhanced)
    
    # Aplicar filtro de nitidez suave
    enhanced = enhanced.filter(ImageFilter.UnsharpMask(radius=1, percent=100, threshold=3))
    
    return enhanced

def create_comparison_image(original_path, processed_path, output_path):
    """
    Crear imagen de comparación lado a lado
    
    Args:
        original_path (str): Ruta imagen original
        processed_path (str): Ruta imagen procesada
        output_path (str): Ruta imagen de comparación
    
    Returns:
        bool: True si fue exitoso
    """
    try:
        original = Image.open(original_path)
        processed = Image.open(processed_path)
        
        # Redimensionar a mismo tamaño
        size = (400, 400)
        original = original.resize(size, Image.Resampling.LANCZOS)
        processed = processed.resize(size, Image.Resampling.LANCZOS)
        
        # Crear imagen combinada
        comparison = Image.new('RGB', (820, 400), 'white')
        comparison.paste(original, (10, 0))
        comparison.paste(processed, (410, 0))
        
        # Agregar etiquetas
        draw = ImageDraw.Draw(comparison)
        draw.text((10, 10), "Original", fill='black')
        draw.text((410, 10), "Procesado", fill='black')
        
        comparison.save(output_path, quality=95)
        return True
        
    except Exception as e:
        print(f"Error creando comparación: {e}")
        return False

def get_image_info(image_path):
    """
    Obtener información de una imagen
    
    Args:
        image_path (str): Ruta de la imagen
    
    Returns:
        dict: Información de la imagen
    """
    try:
        image = Image.open(image_path)
        
        return {
            'size': image.size,
            'mode': image.mode,
            'format': image.format,
            'file_size': os.path.getsize(image_path)
        }
    except Exception as e:
        print(f"Error obteniendo info de imagen: {e}")
        return None

if __name__ == '__main__':
    # Pruebas del módulo
    print("🧪 Módulo de procesamiento de imágenes cargado")
    print("✅ Funciones disponibles:")
    print("  - process_sketch_to_photo()")
    print("  - enhance_image()")
    print("  - create_comparison_image()")
    print("  - get_image_info()")
