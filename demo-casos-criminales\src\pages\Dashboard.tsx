import { Link } from 'react-router-dom'
import { 
  <PERSON><PERSON><PERSON>t, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Brain,
  TrendingUp,
  Calendar,
  MapPin,
  User,
  Zap,
  Target,
  Activity
} from 'lucide-react'
import { Caso, Estadisticas } from '../App'

interface DashboardProps {
  casos: Caso[]
  estadisticas: Estadisticas | null
}

export default function Dashboard({ casos, estadisticas }: DashboardProps) {
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'activo': return 'text-green-400 bg-green-900/30 border-green-700'
      case 'pendiente': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'resuelto': return 'text-blue-400 bg-blue-900/30 border-blue-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const getPrioridadColor = (prioridad: string) => {
    switch (prioridad) {
      case 'muy_alta': return 'text-red-400 bg-red-900/30 border-red-700'
      case 'alta': return 'text-orange-400 bg-orange-900/30 border-orange-700'
      case 'media': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'baja': return 'text-green-400 bg-green-900/30 border-green-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-PE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const casosRecientes = casos.slice(0, 5)

  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">
          Sistema Forense - Identikit IA
        </h1>
        <p className="text-xl text-gray-300 mb-2">
          Demo con 9 Casos Criminales Reales Procesados
        </p>
        <p className="text-gray-400">
          Conversión de bocetos a imágenes fotorrealistas usando ControlNet + Stable Diffusion
        </p>
      </div>

      {/* Estadísticas Principales */}
      {estadisticas && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm font-medium">Total de Casos</p>
                <p className="text-3xl font-bold text-white">{estadisticas.totalCasos}</p>
              </div>
              <div className="p-3 bg-blue-600/20 rounded-lg">
                <FileText className="w-8 h-8 text-blue-400" />
              </div>
            </div>
            <p className="text-gray-500 text-sm mt-2">
              <TrendingUp className="inline w-4 h-4 mr-1" />
              Procesados con IA
            </p>
          </div>

          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm font-medium">Casos Activos</p>
                <p className="text-3xl font-bold text-green-400">{estadisticas.activos}</p>
              </div>
              <div className="p-3 bg-green-600/20 rounded-lg">
                <Activity className="w-8 h-8 text-green-400" />
              </div>
            </div>
            <p className="text-gray-500 text-sm mt-2">
              <AlertTriangle className="inline w-4 h-4 mr-1" />
              En investigación
            </p>
          </div>

          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm font-medium">Precisión IA</p>
                <p className="text-3xl font-bold text-purple-400">{estadisticas.precisionPromedio}</p>
              </div>
              <div className="p-3 bg-purple-600/20 rounded-lg">
                <Target className="w-8 h-8 text-purple-400" />
              </div>
            </div>
            <p className="text-gray-500 text-sm mt-2">
              <Brain className="inline w-4 h-4 mr-1" />
              Promedio del sistema
            </p>
          </div>

          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm font-medium">Tiempo Promedio</p>
                <p className="text-3xl font-bold text-yellow-400">{estadisticas.tiempoProcesamientoPromedio}</p>
              </div>
              <div className="p-3 bg-yellow-600/20 rounded-lg">
                <Zap className="w-8 h-8 text-yellow-400" />
              </div>
            </div>
            <p className="text-gray-500 text-sm mt-2">
              <Clock className="inline w-4 h-4 mr-1" />
              Procesamiento IA
            </p>
          </div>
        </div>
      )}

      {/* Casos Destacados */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <AlertTriangle className="w-6 h-6 mr-3 text-red-400" />
              Casos de Máxima Prioridad
            </h2>
            <div className="space-y-4">
              {casos.filter(caso => caso.prioridad === 'muy_alta').map((caso) => (
                <div key={caso.id} className="bg-slate-700/50 border border-red-700/50 rounded-lg p-4 hover:bg-slate-700/70 transition-colors">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="font-mono text-blue-400 font-bold text-lg">{caso.id}</span>
                        <span className={`px-3 py-1 rounded text-xs font-medium border ${getEstadoColor(caso.estado)}`}>
                          {caso.estado.toUpperCase()}
                        </span>
                        <span className={`px-3 py-1 rounded text-xs font-medium border ${getPrioridadColor(caso.prioridad)}`}>
                          PRIORIDAD MUY ALTA
                        </span>
                      </div>
                      <h3 className="text-white font-bold text-lg mb-2">{caso.titulo}</h3>
                      <p className="text-gray-300 mb-3">{caso.descripcion}</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-400">
                        <span className="flex items-center">
                          <MapPin className="w-4 h-4 mr-2" />
                          {caso.ubicacion}
                        </span>
                        <span className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          {caso.oficial.nombre}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="w-4 h-4 mr-2" />
                          {formatFecha(caso.fecha)}
                        </span>
                        <span className="flex items-center">
                          <Brain className="w-4 h-4 mr-2" />
                          Precisión IA: {caso.parametrosIA.precision}
                        </span>
                      </div>
                    </div>
                    <Link
                      to={`/caso/${caso.id}`}
                      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      <span>Ver Caso</span>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Tecnología IA */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-bold text-white mb-4 flex items-center">
              <Brain className="w-5 h-5 mr-2 text-purple-400" />
              Tecnología IA
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Modelo</span>
                <span className="text-white font-medium">ControlNet + SD</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">GPU</span>
                <span className="text-green-400 font-medium">RTX 4090 24GB</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Resolución Max</span>
                <span className="text-blue-400 font-medium">1024x1024</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Casos Exitosos</span>
                <span className="text-yellow-400 font-medium">8/9 (88.9%)</span>
              </div>
            </div>
          </div>

          {/* Acciones Rápidas */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-bold text-white mb-4">Acciones Rápidas</h3>
            <div className="space-y-3">
              <Link
                to="/galeria"
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200"
              >
                <Brain size={20} />
                <span>Ver Galería Completa</span>
              </Link>
              <button className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200">
                <FileText size={20} />
                <span>Generar Reporte</span>
              </button>
              <button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200">
                <Activity size={20} />
                <span>Estadísticas Avanzadas</span>
              </button>
            </div>
          </div>

          {/* Estado del Sistema */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
            <h3 className="text-lg font-bold text-white mb-4">Estado del Sistema</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Sistema IA</span>
                <span className="text-green-400 font-medium">● ACTIVO</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Base de Datos</span>
                <span className="text-green-400 font-medium">● CONECTADA</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Procesamiento</span>
                <span className="text-green-400 font-medium">● DISPONIBLE</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Última Actualización</span>
                <span className="text-blue-400 font-medium">HOY</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Casos Recientes */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
          <Clock className="w-6 h-6 mr-3 text-blue-400" />
          Casos Recientes Procesados
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {casosRecientes.map((caso) => (
            <div key={caso.id} className="bg-slate-700/50 border border-slate-600 rounded-lg p-4 hover:bg-slate-700/70 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <span className="font-mono text-blue-400 font-bold">{caso.id}</span>
                <span className={`px-2 py-1 rounded text-xs font-medium border ${getEstadoColor(caso.estado)}`}>
                  {caso.estado.toUpperCase()}
                </span>
                <span className={`px-2 py-1 rounded text-xs font-medium border ${getPrioridadColor(caso.prioridad)}`}>
                  {caso.prioridad.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              <h3 className="text-white font-semibold mb-2">{caso.titulo}</h3>
              <p className="text-gray-400 text-sm mb-3 line-clamp-2">{caso.descripcion}</p>
              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  <p>Precisión IA: {caso.parametrosIA.precision}</p>
                  <p>{formatFecha(caso.fechaProcesamientoIA)}</p>
                </div>
                <Link
                  to={`/caso/${caso.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  Ver
                </Link>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-6 text-center">
          <Link
            to="/galeria"
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-md font-medium transition-all duration-200 inline-flex items-center space-x-2"
          >
            <span>Ver Todos los Casos</span>
            <TrendingUp size={20} />
          </Link>
        </div>
      </div>
    </div>
  )
}
