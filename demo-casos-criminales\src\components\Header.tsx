import { Link, useLocation } from 'react-router-dom'
import { Shield, Home, Grid3X3, Eye, Activity, Database, Search } from 'lucide-react'

export default function Header() {
  const location = useLocation()

  const isActive = (path: string) => location.pathname === path

  return (
    <header className="bg-slate-800/90 backdrop-blur-sm border-b border-slate-700 shadow-xl sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo y Branding */}
          <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">
                SISTEMA FORENSE DEMO
              </h1>
              <p className="text-sm text-gray-400 font-mono">
                IDENTIKIT IA - CASOS REALES PROCESADOS
              </p>
            </div>
          </Link>

          {/* Navegación */}
          <nav className="flex items-center space-x-6">
            <Link
              to="/"
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
                isActive('/') 
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg' 
                  : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
              }`}
            >
              <Home size={20} />
              <span className="font-medium">Dashboard</span>
            </Link>

            <Link
              to="/galeria"
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
                isActive('/galeria') 
                  ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg' 
                  : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
              }`}
            >
              <Grid3X3 size={20} />
              <span className="font-medium">Galería</span>
            </Link>

            {/* Indicadores de Estado */}
            <div className="flex items-center space-x-4 border-l border-slate-600 pl-6">
              <div className="flex items-center space-x-2">
                <Activity className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400 font-medium">IA ACTIVA</span>
              </div>
              <div className="flex items-center space-x-2">
                <Database className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-blue-400 font-medium">9 CASOS</span>
              </div>
              <div className="flex items-center space-x-2">
                <Eye className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-yellow-400 font-medium">DEMO</span>
              </div>
            </div>
          </nav>

          {/* Búsqueda */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Buscar casos..."
              className="bg-slate-700 text-white pl-10 pr-4 py-2 rounded-md border border-slate-600 focus:border-blue-500 focus:outline-none w-64"
            />
          </div>
        </div>
      </div>
    </header>
  )
}
