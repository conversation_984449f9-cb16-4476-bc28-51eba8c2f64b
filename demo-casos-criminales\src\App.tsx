import './App.css'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { useState, useEffect } from 'react'
import Header from './components/Header'
import Dashboard from './pages/Dashboard'
import CasoDetalle from './pages/CasoDetalle'
import Galeria from './pages/Galeria'

export interface Caso {
  id: string
  titulo: string
  fecha: string
  fechaProcesamientoIA: string
  estado: 'activo' | 'resuelto' | 'pendiente'
  prioridad: 'muy_alta' | 'alta' | 'media' | 'baja'
  tipoDelito: string
  ubicacion: string
  ubicacionExacta: string
  descripcion: string
  testigo: {
    nombre: string
    edad: number
    dni: string
    telefono: string
    ocupacion: string
    descripcionSospechoso: string
  }
  oficial: {
    nombre: string
    rango: string
    placa: string
    unidad: string
    telefono: string
  }
  analista: {
    nombre: string
    especialidad: string
    codigo: string
  }
  imagenOriginal: string
  imagenProcesada: string
  parametrosIA: {
    modelo: string
    calidad: string
    resolution: string
    steps: number
    guidance: number
    seed: number
    tiempoProcesamiento: string
    precision: string
  }
  timeline: Array<{
    fecha: string
    evento: string
    descripcion: string
  }>
  evidencias: string[]
  seguimiento: string
}

export interface Estadisticas {
  totalCasos: number
  activos: number
  resueltos: number
  pendientes: number
  prioridadMuyAlta: number
  prioridadAlta: number
  prioridadMedia: number
  prioridadBaja: number
  precisionPromedio: string
  tiempoProcesamientoPromedio: string
  efectividadIdentificacion: string
}

function App() {
  const [casos, setCasos] = useState<Caso[]>([])
  const [estadisticas, setEstadisticas] = useState<Estadisticas | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const cargarDatos = async () => {
      try {
        const response = await fetch('/data/casos-criminales.json')
        const data = await response.json()
        setCasos(data.casos)
        setEstadisticas(data.estadisticas)
      } catch (error) {
        console.error('Error cargando datos:', error)
      } finally {
        setLoading(false)
      }
    }

    cargarDatos()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-xl">Cargando Sistema Criminalístico...</p>
          <p className="text-gray-400 text-sm mt-2">Procesando casos con IA</p>
        </div>
      </div>
    )
  }

  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Dashboard casos={casos} estadisticas={estadisticas} />} />
            <Route path="/galeria" element={<Galeria casos={casos} />} />
            <Route path="/caso/:id" element={<CasoDetalle casos={casos} />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
