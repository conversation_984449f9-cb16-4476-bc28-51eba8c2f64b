{% extends "base.html" %}

{% block title %}{{ caso.titulo }} - Sistema Identikit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-folder-open"></i>
        Detalle del Caso
    </h1>
    <div>
        <a href="{{ url_for('casos') }}" class="btn btn-outline-secondary me-2">
            <i class="bi bi-arrow-left"></i>
            Volver a Casos
        </a>
        {% if user.rol in ['admin', 'investigador'] %}
        <a href="{{ url_for('generar_ia', caso_id=caso.id) }}" class="btn btn-success">
            <i class="bi bi-robot"></i>
            Generar Identikit
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Información principal del caso -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {{ caso.titulo }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>ID del Caso:</strong></td>
                                <td><code>{{ caso.id[:8] }}...</code></td>
                            </tr>
                            <tr>
                                <td><strong>Estado:</strong></td>
                                <td>
                                    {% if caso.estado == 'activo' %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="bi bi-exclamation-circle"></i> Activo
                                        </span>
                                    {% elif caso.estado == 'resuelto' %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> Resuelto
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-archive"></i> Archivado
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Ubicación:</strong></td>
                                <td>{{ caso.ubicacion }}</td>
                            </tr>
                            <tr>
                                <td><strong>Fecha del Incidente:</strong></td>
                                <td>{{ caso.fecha_incidente }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Fecha de Creación:</strong></td>
                                <td>{{ caso.fecha_creacion[:10] if caso.fecha_creacion else 'N/A' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Investigador:</strong></td>
                                <td>
                                    {% if caso.investigador_id %}
                                        <span class="badge bg-primary">Asignado</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Sin asignar</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Identikits:</strong></td>
                                <td>
                                    <span class="badge bg-info">{{ identikits|length }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Última Actualización:</strong></td>
                                <td>{{ caso.fecha_actualizacion[:10] if caso.fecha_actualizacion else 'N/A' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if caso.descripcion %}
                <div class="mt-3">
                    <h6><i class="bi bi-card-text"></i> Descripción del Caso</h6>
                    <div class="border rounded p-3 bg-light">
                        {{ caso.descripcion }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Identikits generados -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-images"></i>
                    Identikits Generados ({{ identikits|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if identikits %}
                <div class="row">
                    {% for identikit in identikits %}
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    Identikit #{{ loop.index }}
                                    <small class="text-muted">{{ identikit.fecha_creacion[:10] }}</small>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <h6 class="text-center">Original</h6>
                                        <img src="{{ url_for('uploaded_file', filename=identikit.imagen_original) }}" 
                                             class="img-fluid rounded" alt="Boceto original">
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-center">Procesado</h6>
                                        {% if identikit.imagen_procesada %}
                                        <img src="{{ url_for('generated_file', filename=identikit.imagen_procesada) }}" 
                                             class="img-fluid rounded" alt="Identikit procesado">
                                        {% else %}
                                        <div class="text-center text-muted">
                                            <i class="bi bi-hourglass-split"></i>
                                            <p>Procesando...</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="btn-group w-100" role="group">
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewFullSize('{{ identikit.id }}')">
                                            <i class="bi bi-zoom-in"></i>
                                            Ver Completo
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" 
                                                onclick="downloadIdentikit('{{ identikit.imagen_procesada }}')">
                                            <i class="bi bi-download"></i>
                                            Descargar
                                        </button>
                                        {% if user.rol == 'admin' %}
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteIdentikit('{{ identikit.id }}')">
                                            <i class="bi bi-trash"></i>
                                            Eliminar
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                {% if identikit.parametros %}
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="bi bi-gear"></i>
                                        Parámetros: {{ identikit.parametros }}
                                    </small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-image" style="font-size: 3rem; color: #cbd5e0;"></i>
                    <h5 class="text-muted mt-2">No hay identikits generados</h5>
                    <p class="text-muted">Comienza generando tu primer identikit para este caso</p>
                    {% if user.rol in ['admin', 'investigador'] %}
                    <a href="{{ url_for('generar_ia', caso_id=caso.id) }}" class="btn btn-primary">
                        <i class="bi bi-robot"></i>
                        Generar Primer Identikit
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Panel lateral -->
    <div class="col-lg-4">
        <!-- Acciones rápidas -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-charge"></i>
                    Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if user.rol in ['admin', 'investigador'] %}
                    <a href="{{ url_for('generar_ia', caso_id=caso.id) }}" class="btn btn-success">
                        <i class="bi bi-robot"></i>
                        Generar Identikit
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-primary" onclick="generateReport()">
                        <i class="bi bi-file-earmark-pdf"></i>
                        Generar Reporte
                    </button>
                    
                    {% if user.rol in ['admin', 'investigador'] %}
                    <button class="btn btn-outline-warning" onclick="updateStatus()">
                        <i class="bi bi-pencil-square"></i>
                        Actualizar Estado
                    </button>
                    {% endif %}
                    
                    <button class="btn btn-outline-info" onclick="shareCase()">
                        <i class="bi bi-share"></i>
                        Compartir Caso
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Estadísticas del caso -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    Estadísticas
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ identikits|length }}</h4>
                            <small class="text-muted">Identikits</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">0</h4>
                        <small class="text-muted">Evidencias</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-success">
                                {% if caso.fecha_creacion %}
                                    {{ ((caso.fecha_creacion | length) > 0) | int }}
                                {% else %}
                                    0
                                {% endif %}
                            </h4>
                            <small class="text-muted">Días Activo</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">0</h4>
                        <small class="text-muted">Actualizaciones</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Información adicional -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-square"></i>
                    Información Adicional
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> Consejos</h6>
                    <ul class="mb-0 small">
                        <li>Genera múltiples identikits para mejor precisión</li>
                        <li>Usa diferentes parámetros de procesamiento</li>
                        <li>Documenta todos los cambios importantes</li>
                    </ul>
                </div>
                
                <h6><i class="bi bi-clock-history"></i> Historial Reciente</h6>
                <ul class="list-unstyled small">
                    <li class="mb-1">
                        <i class="bi bi-circle-fill text-success" style="font-size: 0.5rem;"></i>
                        Caso creado - {{ caso.fecha_creacion[:10] if caso.fecha_creacion else 'N/A' }}
                    </li>
                    {% for identikit in identikits[:3] %}
                    <li class="mb-1">
                        <i class="bi bi-circle-fill text-primary" style="font-size: 0.5rem;"></i>
                        Identikit generado - {{ identikit.fecha_creacion[:10] }}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Modal para vista completa -->
<div class="modal fade" id="fullSizeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vista Completa del Identikit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="fullSizeImage" class="img-fluid" alt="Identikit completo">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewFullSize(identikitId) {
    // Aquí podrías cargar la imagen específica del identikit
    const modal = new bootstrap.Modal(document.getElementById('fullSizeModal'));
    modal.show();
}

function downloadIdentikit(filename) {
    if (filename) {
        const link = document.createElement('a');
        link.href = `/generated/${filename}`;
        link.download = filename;
        link.click();
    }
}

function deleteIdentikit(identikitId) {
    if (confirm('¿Estás seguro de que deseas eliminar este identikit?')) {
        // Aquí harías la llamada AJAX para eliminar
        fetch(`/api/identikits/${identikitId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error al eliminar el identikit');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar el identikit');
        });
    }
}

function generateReport() {
    alert('Generando reporte PDF del caso...');
}

function updateStatus() {
    const newStatus = prompt('Nuevo estado del caso (activo/resuelto/archivado):');
    if (newStatus && ['activo', 'resuelto', 'archivado'].includes(newStatus)) {
        alert(`Estado actualizado a: ${newStatus}`);
        location.reload();
    } else if (newStatus) {
        alert('Estado no válido. Use: activo, resuelto o archivado');
    }
}

function shareCase() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
        alert('Enlace del caso copiado al portapapeles');
    }).catch(() => {
        alert('No se pudo copiar el enlace');
    });
}
</script>
{% endblock %}
