#!/usr/bin/env python3
"""
Análisis en profundidad de tecnologías de IA generativa para sketch-to-face
Procesa todos los datos recopilados y genera insights técnicos
"""

import json
import os
from collections import defaultdict, Counter
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def setup_matplotlib_for_plotting():
    """
    Setup matplotlib y seaborn para plotting con configuración apropiada.
    """
    import warnings
    warnings.filterwarnings('default')
    
    plt.switch_backend("Agg")
    plt.style.use("seaborn-v0_8")
    sns.set_palette("husl")
    
    plt.rcParams["font.sans-serif"] = ["Noto Sans CJK SC", "WenQuanYi Zen Hei", "PingFang SC", "Arial Unicode MS", "Hiragino Sans GB"]
    plt.rcParams["axes.unicode_minus"] = False

def load_json_data(filepath):
    """Cargar datos JSON de manera segura"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Archivo no encontrado: {filepath}")
        return {}
    except json.JSONDecodeError:
        print(f"Error al decodificar JSON: {filepath}")
        return {}

def analyze_academic_papers():
    """Analizar papers académicos recopilados"""
    print("=== ANÁLISIS DE PAPERS ACADÉMICOS ===")
    
    papers_data = load_json_data('/workspace/data/academic_papers_sketch_to_face.json')
    
    # Estadísticas por query
    papers_by_query = {}
    total_papers = 0
    year_distribution = Counter()
    citation_counts = []
    
    for query, papers in papers_data.items():
        papers_by_query[query] = len(papers)
        total_papers += len(papers)
        
        # Analizar años y citaciones
        for paper in papers:
            if 'year' in paper and paper['year']:
                try:
                    year = int(paper['year'])
                    if 1990 <= year <= 2024:  # Filtrar años válidos
                        year_distribution[year] += 1
                except:
                    pass
            
            if 'citedBy' in paper and paper['citedBy']:
                try:
                    # Extraer número de citaciones
                    cited_str = paper['citedBy'].replace('Cited by ', '').replace(',', '')
                    if cited_str.isdigit():
                        citation_counts.append(int(cited_str))
                except:
                    pass
    
    print(f"Total de papers encontrados: {total_papers}")
    print(f"Papers por query:")
    for query, count in papers_by_query.items():
        print(f"  - {query}: {count} papers")
    
    # Crear visualización de distribución por años
    if year_distribution:
        setup_matplotlib_for_plotting()
        
        years = sorted(year_distribution.keys())
        counts = [year_distribution[year] for year in years]
        
        plt.figure(figsize=(12, 6))
        plt.bar(years, counts, alpha=0.7)
        plt.title('Distribución de Papers por Año')
        plt.xlabel('Año')
        plt.ylabel('Número de Papers')
        plt.xticks(years, rotation=45)
        plt.tight_layout()
        plt.savefig('/workspace/charts/papers_by_year.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Años más productivos:")
        for year, count in sorted(year_distribution.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  - {year}: {count} papers")
    
    # Estadísticas de citaciones
    if citation_counts:
        avg_citations = sum(citation_counts) / len(citation_counts)
        max_citations = max(citation_counts)
        print(f"Estadísticas de citaciones:")
        print(f"  - Promedio: {avg_citations:.1f}")
        print(f"  - Máximo: {max_citations}")
        print(f"  - Papers con datos de citación: {len(citation_counts)}")
    
    return papers_by_query, year_distribution

def analyze_github_repositories():
    """Analizar repositorios de GitHub"""
    print("\n=== ANÁLISIS DE REPOSITORIOS GITHUB ===")
    
    repositories = {
        'SketchFaceNeRF': '/workspace/data/sketchfacenerf_analysis.json',
        'ControlNet': '/workspace/data/controlnet_analysis.json',
        'GFPGAN': '/workspace/data/gfpgan_detailed_analysis.json',
        'GAN Sketch-Face': '/workspace/data/gan_sketch_face_analysis.json',
        'Multi-GAN': '/workspace/data/multi_gan_analysis.json',
        'Lightweight Face Detector': '/workspace/data/lightweight_face_detector_analysis.json'
    }
    
    repo_analysis = {}
    
    for repo_name, filepath in repositories.items():
        data = load_json_data(filepath)
        
        analysis = {
            'name': repo_name,
            'hardware_req': 'No especificado',
            'maturity': 'Desconocido',
            'deployment_complexity': 'Media',
            'local_viability': 'Media'
        }
        
        # Analizar requerimientos específicos por repositorio
        if repo_name == 'SketchFaceNeRF':
            if 'specifications' in data and 'hardware' in data['specifications']:
                analysis['hardware_req'] = data['specifications']['hardware']
            analysis['deployment_complexity'] = 'Muy Alta'
            analysis['local_viability'] = 'Baja'
            analysis['maturity'] = 'Experimental'
            
        elif repo_name == 'ControlNet':
            if 'specifications' in data and 'hardware_requirements' in data['specifications']:
                vram = data['specifications']['hardware_requirements'].get('vram', '')
                analysis['hardware_req'] = f"8GB+ VRAM ({vram})"
            analysis['deployment_complexity'] = 'Media'
            analysis['local_viability'] = 'Alta'
            analysis['maturity'] = 'Maduro'
            
        elif repo_name == 'GFPGAN':
            analysis['hardware_req'] = 'GPU opcional, funciona en CPU'
            analysis['deployment_complexity'] = 'Baja'
            analysis['local_viability'] = 'Muy Alta'
            analysis['maturity'] = 'Maduro'
            
        elif repo_name == 'Lightweight Face Detector':
            if 'specifications' in data and 'model_size' in data['specifications']:
                size = data['specifications']['model_size']
                analysis['hardware_req'] = f"CPU optimizado, modelo {size.get('fp32', '1MB')}"
            analysis['deployment_complexity'] = 'Muy Baja'
            analysis['local_viability'] = 'Muy Alta'
            analysis['maturity'] = 'Maduro'
        
        repo_analysis[repo_name] = analysis
        
        print(f"\n{repo_name}:")
        print(f"  - Hardware: {analysis['hardware_req']}")
        print(f"  - Complejidad de deployment: {analysis['deployment_complexity']}")
        print(f"  - Viabilidad local: {analysis['local_viability']}")
        print(f"  - Madurez: {analysis['maturity']}")
    
    return repo_analysis

def create_technology_comparison():
    """Crear comparación detallada de tecnologías"""
    print("\n=== COMPARACIÓN DE TECNOLOGÍAS ===")
    
    technologies = {
        'SketchFaceNeRF': {
            'tipo': 'NeRF 3D',
            'calidad_expected': 'Muy Alta',
            'hardware_min': 'RTX 3090Ti+',
            'vram_gb': 24,
            'cpu_only': False,
            'complexity': 5,
            'maturity': 2,
            'local_viability': 1,
            'forensic_suitability': 4
        },
        'ControlNet + Stable Diffusion': {
            'tipo': 'Diffusion Model',
            'calidad_expected': 'Alta',
            'hardware_min': 'GTX 1660 Ti+',
            'vram_gb': 8,
            'cpu_only': False,
            'complexity': 3,
            'maturity': 5,
            'local_viability': 4,
            'forensic_suitability': 4
        },
        'StyleGAN + Conditioning': {
            'tipo': 'GAN',
            'calidad_expected': 'Alta',
            'hardware_min': 'GTX 1060+',
            'vram_gb': 6,
            'cpu_only': False,
            'complexity': 4,
            'maturity': 4,
            'local_viability': 3,
            'forensic_suitability': 3
        },
        'GFPGAN + Pre-processing': {
            'tipo': 'Face Restoration',
            'calidad_expected': 'Media-Alta',
            'hardware_min': 'CPU Intel i5+',
            'vram_gb': 0,
            'cpu_only': True,
            'complexity': 2,
            'maturity': 5,
            'local_viability': 5,
            'forensic_suitability': 3
        },
        'Multi-GAN Pipeline': {
            'tipo': 'Hybrid GAN',
            'calidad_expected': 'Media',
            'hardware_min': 'GTX 1050+',
            'vram_gb': 4,
            'cpu_only': False,
            'complexity': 4,
            'maturity': 2,
            'local_viability': 2,
            'forensic_suitability': 2
        }
    }
    
    # Crear DataFrame para análisis
    df = pd.DataFrame(technologies).T
    
    # Convertir columnas numéricas a float
    numeric_columns = ['vram_gb', 'complexity', 'maturity', 'local_viability', 'forensic_suitability']
    for col in numeric_columns:
        df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Crear visualizaciones comparativas
    setup_matplotlib_for_plotting()
    
    # Gráfico de radar comparativo
    metrics = ['complexity', 'maturity', 'local_viability', 'forensic_suitability']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. VRAM Requirements
    vram_data = df[df['vram_gb'] > 0]['vram_gb'].sort_values()
    axes[0,0].barh(vram_data.index, vram_data.values)
    axes[0,0].set_title('Requerimientos de VRAM (GB)')
    axes[0,0].set_xlabel('VRAM (GB)')
    
    # 2. Complexity vs Local Viability
    axes[0,1].scatter(df['complexity'], df['local_viability'], s=100, alpha=0.7)
    for i, tech in enumerate(df.index):
        axes[0,1].annotate(tech, (df.loc[tech, 'complexity'], df.loc[tech, 'local_viability']),
                          xytext=(5, 5), textcoords='offset points', fontsize=8)
    axes[0,1].set_xlabel('Complejidad (1-5)')
    axes[0,1].set_ylabel('Viabilidad Local (1-5)')
    axes[0,1].set_title('Complejidad vs Viabilidad Local')
    
    # 3. Maturity vs Forensic Suitability
    axes[1,0].scatter(df['maturity'], df['forensic_suitability'], s=100, alpha=0.7)
    for i, tech in enumerate(df.index):
        axes[1,0].annotate(tech, (df.loc[tech, 'maturity'], df.loc[tech, 'forensic_suitability']),
                          xytext=(5, 5), textcoords='offset points', fontsize=8)
    axes[1,0].set_xlabel('Madurez (1-5)')
    axes[1,0].set_ylabel('Aptitud Forense (1-5)')
    axes[1,0].set_title('Madurez vs Aptitud Forense')
    
    # 4. Overall Score
    df['overall_score'] = (df['maturity'] + df['local_viability'] + df['forensic_suitability'] - df['complexity']) / 4
    overall_scores = df['overall_score'].sort_values(ascending=True)
    axes[1,1].barh(range(len(overall_scores)), overall_scores.values)
    axes[1,1].set_yticks(range(len(overall_scores)))
    axes[1,1].set_yticklabels(overall_scores.index)
    axes[1,1].set_title('Puntuación Global (Mayor es Mejor)')
    axes[1,1].set_xlabel('Puntuación')
    
    plt.tight_layout()
    plt.savefig('/workspace/charts/technology_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # Imprimir recomendaciones
    print("\nRECOMENDACIONES POR CATEGORÍA:")
    
    top_overall = df.nlargest(3, 'overall_score')
    print(f"\nMejor puntuación global:")
    for tech, score in zip(top_overall.index, top_overall['overall_score']):
        print(f"  1. {tech}: {score:.2f}")
    
    best_local = df.nlargest(3, 'local_viability')
    print(f"\nMejor viabilidad local:")
    for tech, score in zip(best_local.index, best_local['local_viability']):
        print(f"  - {tech}: {score}/5")
    
    lowest_complexity = df.nsmallest(3, 'complexity')
    print(f"\nMenor complejidad:")
    for tech, score in zip(lowest_complexity.index, lowest_complexity['complexity']):
        print(f"  - {tech}: {score}/5")
    
    cpu_compatible = df[df['cpu_only'] == True]
    print(f"\nCompatibles solo con CPU:")
    for tech in cpu_compatible.index:
        print(f"  - {tech}")
    
    return df, technologies

def generate_implementation_recommendations():
    """Generar recomendaciones específicas de implementación"""
    print("\n=== RECOMENDACIONES DE IMPLEMENTACIÓN ===")
    
    recommendations = {
        'Beginner (Prototipo Rápido)': {
            'tech': 'GFPGAN + Simple Sketch Processing',
            'hardware': 'CPU Intel i5+ o AMD equivalente',
            'memory': '8GB RAM',
            'storage': '2GB para modelos',
            'setup_time': '2-4 horas',
            'complexity': 'Baja',
            'expected_quality': 'Media',
            'pros': ['Fácil setup', 'No GPU requerida', 'Estable'],
            'cons': ['Calidad limitada', 'No sketch-to-face directo']
        },
        'Intermediate (Producción Básica)': {
            'tech': 'ControlNet + Stable Diffusion',
            'hardware': 'GTX 1660 Ti / RTX 3060',
            'memory': '16GB RAM',
            'storage': '10GB para modelos',
            'setup_time': '1-2 días',
            'complexity': 'Media',
            'expected_quality': 'Alta',
            'pros': ['Balance calidad/complejidad', 'Bien documentado', 'Activo desarrollo'],
            'cons': ['Requiere GPU', 'Configuración inicial compleja']
        },
        'Advanced (Máxima Calidad)': {
            'tech': 'ControlNet + Fine-tuning + Post-processing',
            'hardware': 'RTX 4070 / RTX 3080+',
            'memory': '32GB RAM',
            'storage': '50GB+ para modelos y datasets',
            'setup_time': '1-2 semanas',
            'complexity': 'Alta',
            'expected_quality': 'Muy Alta',
            'pros': ['Máxima calidad', 'Personalizable', 'Escalable'],
            'cons': ['Costoso hardware', 'Expertise técnico requerido']
        },
        'Research (Experimental)': {
            'tech': 'SketchFaceNeRF + Custom Training',
            'hardware': 'RTX 4090 / A100',
            'memory': '64GB+ RAM',
            'storage': '100GB+ para modelos y datasets',
            'setup_time': '2-4 semanas',
            'complexity': 'Muy Alta',
            'expected_quality': 'Experimental (Potencialmente Muy Alta)',
            'pros': ['Estado del arte', 'Control 3D', 'Innovador'],
            'cons': ['Muy complejo', 'Hardware costoso', 'Experimental']
        }
    }
    
    for level, rec in recommendations.items():
        print(f"\n{level}:")
        print(f"  Tecnología: {rec['tech']}")
        print(f"  Hardware: {rec['hardware']}")
        print(f"  Memoria: {rec['memory']}")
        print(f"  Almacenamiento: {rec['storage']}")
        print(f"  Tiempo setup: {rec['setup_time']}")
        print(f"  Complejidad: {rec['complexity']}")
        print(f"  Calidad esperada: {rec['expected_quality']}")
        print(f"  Pros: {', '.join(rec['pros'])}")
        print(f"  Contras: {', '.join(rec['cons'])}")
    
    return recommendations

def main():
    """Función principal del análisis"""
    print("ANÁLISIS COMPLETO DE TECNOLOGÍAS SKETCH-TO-FACE")
    print("=" * 60)
    
    # Crear directorios necesarios
    os.makedirs('/workspace/charts', exist_ok=True)
    
    # Ejecutar análisis
    papers_data, year_dist = analyze_academic_papers()
    repo_analysis = analyze_github_repositories()
    tech_comparison, technologies = create_technology_comparison()
    recommendations = generate_implementation_recommendations()
    
    # Guardar resultados del análisis
    analysis_results = {
        'papers_analysis': {
            'total_papers': sum(papers_data.values()),
            'papers_by_query': papers_data,
            'year_distribution': dict(year_dist)
        },
        'repositories_analysis': repo_analysis,
        'technology_comparison': technologies,
        'implementation_recommendations': recommendations,
        'summary': {
            'recommended_for_beginners': 'GFPGAN + Simple Processing',
            'recommended_for_production': 'ControlNet + Stable Diffusion',
            'most_mature': 'ControlNet',
            'lowest_hardware_req': 'GFPGAN',
            'highest_quality_potential': 'SketchFaceNeRF'
        }
    }
    
    with open('/workspace/data/complete_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== ANÁLISIS COMPLETADO ===")
    print(f"Resultados guardados en:")
    print(f"  - /workspace/data/complete_analysis_results.json")
    print(f"  - /workspace/charts/papers_by_year.png")
    print(f"  - /workspace/charts/technology_comparison.png")
    
    return analysis_results

if __name__ == "__main__":
    results = main()
