#!/usr/bin/env python3
"""
Sistema Identikit Fotorrealista - Versión Optimizada Local
Aplicación Flask ligera para procesamiento de identikits offline
"""

import os
import sys
import json
import uuid
from datetime import datetime
from pathlib import Path
import base64
from PIL import Image, ImageFilter, ImageEnhance, ImageOps
import io

# Flask imports
from flask import Flask, request, jsonify, send_file, render_template, redirect, url_for, session, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Módulos locales
from database import init_database, get_db_connection
from image_processor import process_sketch_to_photo, enhance_image

print("🚀 Iniciando Sistema Identikit Local...")
print("📁 Configurando directorios...")

# Configuración
UPLOAD_FOLDER = 'uploads'
GENERATED_FOLDER = 'generated'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'gif'}

# Crear directorios necesarios
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(GENERATED_FOLDER, exist_ok=True)

# Configurar Flask
app = Flask(__name__)
app.secret_key = 'identikit_local_secret_key_2025'
CORS(app)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['GENERATED_FOLDER'] = GENERATED_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB

def allowed_file(filename):
    """Verificar si el archivo tiene una extensión permitida"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_current_user():
    """Obtener usuario actual de la sesión"""
    if 'user_id' in session:
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM usuarios WHERE id = ?', (session['user_id'],)
        ).fetchone()
        conn.close()
        return dict(user) if user else None
    return None

# Rutas principales
@app.route('/')
def index():
    """Página principal - Dashboard"""
    user = get_current_user()
    if not user:
        return redirect(url_for('login'))
    
    # Obtener estadísticas
    conn = get_db_connection()
    stats = {
        'total_casos': conn.execute('SELECT COUNT(*) FROM casos').fetchone()[0],
        'casos_activos': conn.execute('SELECT COUNT(*) FROM casos WHERE estado = "activo"').fetchone()[0],
        'identikits_generados': conn.execute('SELECT COUNT(*) FROM identikits').fetchone()[0],
        'casos_resueltos': conn.execute('SELECT COUNT(*) FROM casos WHERE estado = "resuelto"').fetchone()[0]
    }
    
    # Casos recientes
    casos_recientes = conn.execute(
        'SELECT * FROM casos ORDER BY fecha_creacion DESC LIMIT 5'
    ).fetchall()
    
    conn.close()
    
    return render_template('dashboard.html', 
                         user=user, 
                         stats=stats, 
                         casos_recientes=[dict(caso) for caso in casos_recientes])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM usuarios WHERE username = ? AND password = ?',
            (username, password)
        ).fetchone()
        conn.close()
        
        if user:
            session['user_id'] = user['id']
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Credenciales incorrectas')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Cerrar sesión"""
    session.pop('user_id', None)
    return redirect(url_for('login'))

@app.route('/casos')
def casos():
    """Lista de casos"""
    user = get_current_user()
    if not user:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    casos_list = conn.execute(
        'SELECT * FROM casos ORDER BY fecha_creacion DESC'
    ).fetchall()
    conn.close()
    
    return render_template('casos.html', 
                         user=user, 
                         casos=[dict(caso) for caso in casos_list])

@app.route('/nuevo-caso', methods=['GET', 'POST'])
def nuevo_caso():
    """Crear nuevo caso"""
    user = get_current_user()
    if not user:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        # Procesar formulario de nuevo caso
        titulo = request.form['titulo']
        descripcion = request.form['descripcion']
        ubicacion = request.form['ubicacion']
        fecha_incidente = request.form['fecha_incidente']
        
        caso_id = str(uuid.uuid4())
        
        conn = get_db_connection()
        conn.execute(
            '''INSERT INTO casos (id, titulo, descripcion, ubicacion, fecha_incidente, 
               estado, investigador_id, fecha_creacion) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)''',
            (caso_id, titulo, descripcion, ubicacion, fecha_incidente, 
             'activo', user['id'], datetime.now().isoformat())
        )
        conn.commit()
        conn.close()
        
        return redirect(url_for('caso_detalle', caso_id=caso_id))
    
    return render_template('nuevo_caso.html', user=user)

@app.route('/caso/<caso_id>')
def caso_detalle(caso_id):
    """Detalle de un caso específico"""
    user = get_current_user()
    if not user:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    caso = conn.execute('SELECT * FROM casos WHERE id = ?', (caso_id,)).fetchone()
    
    if not caso:
        conn.close()
        return "Caso no encontrado", 404
    
    # Obtener identikits del caso
    identikits = conn.execute(
        'SELECT * FROM identikits WHERE caso_id = ? ORDER BY fecha_creacion DESC',
        (caso_id,)
    ).fetchall()
    
    conn.close()
    
    return render_template('caso_detalle.html', 
                         user=user, 
                         caso=dict(caso),
                         identikits=[dict(i) for i in identikits])

@app.route('/generar-ia/<caso_id>', methods=['GET', 'POST'])
def generar_ia(caso_id):
    """Generar identikit con IA"""
    user = get_current_user()
    if not user:
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        if 'sketch' not in request.files:
            return jsonify({'error': 'No se encontró archivo de boceto'}), 400
        
        file = request.files['sketch']
        if file.filename == '':
            return jsonify({'error': 'No se seleccionó archivo'}), 400
        
        if file and allowed_file(file.filename):
            # Guardar archivo original
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Procesar imagen
            try:
                processed_filename = f"processed_{filename}"
                processed_path = os.path.join(app.config['GENERATED_FOLDER'], processed_filename)
                
                # Aplicar procesamiento de imagen
                success = process_sketch_to_photo(filepath, processed_path)
                
                if success:
                    # Guardar en base de datos
                    identikit_id = str(uuid.uuid4())
                    conn = get_db_connection()
                    conn.execute(
                        '''INSERT INTO identikits (id, caso_id, imagen_original, imagen_procesada,
                           parametros, fecha_creacion, creado_por)
                           VALUES (?, ?, ?, ?, ?, ?, ?)''',
                        (identikit_id, caso_id, filename, processed_filename,
                         json.dumps(request.form.to_dict()), datetime.now().isoformat(), user['id'])
                    )
                    conn.commit()
                    conn.close()
                    
                    return jsonify({
                        'success': True,
                        'identikit_id': identikit_id,
                        'processed_image': processed_filename
                    })
                else:
                    return jsonify({'error': 'Error al procesar la imagen'}), 500
                    
            except Exception as e:
                print(f"Error procesando imagen: {e}")
                return jsonify({'error': 'Error interno del servidor'}), 500
    
    # GET request - mostrar formulario
    conn = get_db_connection()
    caso = conn.execute('SELECT * FROM casos WHERE id = ?', (caso_id,)).fetchone()
    conn.close()
    
    if not caso:
        return "Caso no encontrado", 404
    
    return render_template('generar_ia.html', user=user, caso=dict(caso))

# Rutas para servir archivos
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Servir archivos subidos"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/generated/<filename>')
def generated_file(filename):
    """Servir archivos generados"""
    return send_from_directory(app.config['GENERATED_FOLDER'], filename)

if __name__ == '__main__':
    print("🗄️ Inicializando base de datos...")
    init_database()
    
    print("✅ Sistema listo!")
    print("🌐 Acceder en: http://localhost:5000")
    print("👤 Credenciales: admin/admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
