{% extends "base.html" %}

{% block title %}Nuevo Caso - Sistema Identikit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-plus-circle"></i>
        Crear Nuevo Caso
    </h1>
    <a href="{{ url_for('casos') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i>
        Volver a Casos
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-plus"></i>
                    Información del Caso
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="nuevoCasoForm">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="titulo" class="form-label">
                                <i class="bi bi-card-heading"></i>
                                Título del Caso *
                            </label>
                            <input type="text" class="form-control" id="titulo" name="titulo" required
                                   placeholder="Ej: Robo a mano armada en joyería central">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="ubicacion" class="form-label">
                                <i class="bi bi-geo-alt"></i>
                                Ubicación del Incidente *
                            </label>
                            <input type="text" class="form-control" id="ubicacion" name="ubicacion" required
                                   placeholder="Dirección o zona del incidente">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="fecha_incidente" class="form-label">
                                <i class="bi bi-calendar-event"></i>
                                Fecha del Incidente *
                            </label>
                            <input type="date" class="form-control" id="fecha_incidente" name="fecha_incidente" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="descripcion" class="form-label">
                            <i class="bi bi-card-text"></i>
                            Descripción Detallada
                        </label>
                        <textarea class="form-control" id="descripcion" name="descripcion" rows="6"
                                  placeholder="Describe los hechos, circunstancias, testigos, evidencias encontradas, etc."></textarea>
                        <div class="form-text">
                            Incluye toda la información relevante sobre el caso
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tipo_delito" class="form-label">
                                <i class="bi bi-shield-exclamation"></i>
                                Tipo de Delito
                            </label>
                            <select class="form-select" id="tipo_delito" name="tipo_delito">
                                <option value="">Seleccionar tipo...</option>
                                <option value="robo">Robo</option>
                                <option value="hurto">Hurto</option>
                                <option value="asalto">Asalto</option>
                                <option value="homicidio">Homicidio</option>
                                <option value="secuestro">Secuestro</option>
                                <option value="violacion">Violación</option>
                                <option value="fraude">Fraude</option>
                                <option value="otro">Otro</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="prioridad" class="form-label">
                                <i class="bi bi-exclamation-triangle"></i>
                                Prioridad
                            </label>
                            <select class="form-select" id="prioridad" name="prioridad">
                                <option value="media">Media</option>
                                <option value="alta">Alta</option>
                                <option value="critica">Crítica</option>
                                <option value="baja">Baja</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="testigos" class="form-label">
                                <i class="bi bi-people"></i>
                                Número de Testigos
                            </label>
                            <input type="number" class="form-control" id="testigos" name="testigos" min="0" value="0">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="evidencias" class="form-label">
                                <i class="bi bi-camera"></i>
                                Evidencias Disponibles
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="video" name="evidencias" value="video">
                                <label class="form-check-label" for="video">Video</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fotos" name="evidencias" value="fotos">
                                <label class="form-check-label" for="fotos">Fotografías</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="huellas" name="evidencias" value="huellas">
                                <label class="form-check-label" for="huellas">Huellas</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="adn" name="evidencias" value="adn">
                                <label class="form-check-label" for="adn">ADN</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="observaciones" class="form-label">
                            <i class="bi bi-chat-square-text"></i>
                            Observaciones Adicionales
                        </label>
                        <textarea class="form-control" id="observaciones" name="observaciones" rows="3"
                                  placeholder="Cualquier información adicional relevante..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle"></i>
                            Cancelar
                        </button>
                        
                        <div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-save"></i>
                                Crear Caso
                            </button>
                            
                            <button type="submit" name="action" value="create_and_generate" class="btn btn-success">
                                <i class="bi bi-robot"></i>
                                Crear y Generar Identikit
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Panel de ayuda -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Guía de Creación
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> Consejos</h6>
                    <ul class="mb-0">
                        <li>Usa títulos descriptivos y específicos</li>
                        <li>Incluye la mayor cantidad de detalles posible</li>
                        <li>Especifica ubicaciones exactas</li>
                        <li>Documenta todos los testigos disponibles</li>
                    </ul>
                </div>
                
                <h6><i class="bi bi-list-check"></i> Información Requerida</h6>
                <ul class="list-unstyled">
                    <li><i class="bi bi-check-circle text-success"></i> Título del caso</li>
                    <li><i class="bi bi-check-circle text-success"></i> Ubicación del incidente</li>
                    <li><i class="bi bi-check-circle text-success"></i> Fecha del incidente</li>
                    <li><i class="bi bi-circle text-muted"></i> Descripción detallada</li>
                    <li><i class="bi bi-circle text-muted"></i> Tipo de delito</li>
                    <li><i class="bi bi-circle text-muted"></i> Evidencias disponibles</li>
                </ul>
                
                <div class="mt-3">
                    <h6><i class="bi bi-clock"></i> Próximos Pasos</h6>
                    <p class="small text-muted">
                        Después de crear el caso, podrás:
                    </p>
                    <ul class="small text-muted">
                        <li>Subir evidencias fotográficas</li>
                        <li>Generar identikits con IA</li>
                        <li>Asignar investigadores</li>
                        <li>Hacer seguimiento del progreso</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Estadísticas rápidas -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    Estadísticas
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">{{ stats.total_casos if stats else 0 }}</h4>
                            <small class="text-muted">Total Casos</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ stats.casos_resueltos if stats else 0 }}</h4>
                        <small class="text-muted">Resueltos</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Establecer fecha actual como máximo
document.getElementById('fecha_incidente').max = new Date().toISOString().split('T')[0];

// Validación del formulario
document.getElementById('nuevoCasoForm').addEventListener('submit', function(e) {
    const titulo = document.getElementById('titulo').value.trim();
    const ubicacion = document.getElementById('ubicacion').value.trim();
    const fecha = document.getElementById('fecha_incidente').value;
    
    if (!titulo || !ubicacion || !fecha) {
        e.preventDefault();
        alert('Por favor completa todos los campos requeridos (*)');
        return false;
    }
    
    // Validar que la fecha no sea futura
    const fechaIncidente = new Date(fecha);
    const hoy = new Date();
    hoy.setHours(0, 0, 0, 0);
    
    if (fechaIncidente > hoy) {
        e.preventDefault();
        alert('La fecha del incidente no puede ser futura');
        return false;
    }
    
    // Mostrar indicador de carga
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Creando...';
    submitBtn.disabled = true;
});

// Auto-guardar borrador cada 30 segundos
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(function() {
        const formData = new FormData(document.getElementById('nuevoCasoForm'));
        const data = Object.fromEntries(formData);
        
        // Guardar en localStorage como borrador
        localStorage.setItem('borrador_caso', JSON.stringify(data));
        
        // Mostrar indicador visual
        const indicator = document.createElement('div');
        indicator.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        indicator.innerHTML = `
            <div class="toast show" role="alert">
                <div class="toast-body">
                    <i class="bi bi-check-circle text-success"></i>
                    Borrador guardado automáticamente
                </div>
            </div>
        `;
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            document.body.removeChild(indicator);
        }, 2000);
    }, 30000);
}

// Cargar borrador si existe
window.addEventListener('load', function() {
    const borrador = localStorage.getItem('borrador_caso');
    if (borrador) {
        const data = JSON.parse(borrador);
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = data[key];
            }
        });
        
        // Mostrar notificación de borrador cargado
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show';
        alert.innerHTML = `
            <i class="bi bi-info-circle"></i>
            Se ha cargado un borrador guardado automáticamente.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.querySelector('.card-body').insertBefore(alert, document.querySelector('form'));
    }
    
    startAutoSave();
});

// Limpiar borrador al enviar exitosamente
window.addEventListener('beforeunload', function() {
    clearInterval(autoSaveInterval);
});
</script>
{% endblock %}
