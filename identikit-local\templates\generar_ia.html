{% extends "base.html" %}

{% block title %}Generar Identikit - Sistema Identikit{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-robot"></i>
        Generar Identikit con IA
    </h1>
    <a href="{{ url_for('caso_detalle', caso_id=caso.id) }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i>
        Volver al Caso
    </a>
</div>

<!-- Información del caso -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-folder"></i>
            Caso: {{ caso.titulo }}
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Ubicación:</strong> {{ caso.ubicacion }}</p>
                <p><strong>Fecha:</strong> {{ caso.fecha_incidente }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Estado:</strong> 
                    <span class="badge bg-warning">{{ caso.estado.title() }}</span>
                </p>
                <p><strong>Descripción:</strong> {{ caso.descripcion[:100] }}...</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Panel de carga -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-upload"></i>
                    Subir Boceto del Sospechoso
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <!-- Área de carga -->
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #3182ce;"></i>
                            <h4>Arrastra tu boceto aquí</h4>
                            <p class="text-muted">o haz clic para seleccionar archivo</p>
                            <input type="file" id="sketchFile" name="sketch" accept="image/*" style="display: none;">
                            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('sketchFile').click()">
                                <i class="bi bi-folder2-open"></i>
                                Seleccionar Archivo
                            </button>
                        </div>
                    </div>
                    
                    <!-- Vista previa -->
                    <div id="previewSection" style="display: none;">
                        <h6 class="mt-4">Vista Previa del Boceto</h6>
                        <div class="text-center">
                            <img id="previewImage" class="image-preview" alt="Vista previa">
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearPreview()">
                                <i class="bi bi-x-circle"></i>
                                Cambiar Imagen
                            </button>
                        </div>
                    </div>
                    
                    <!-- Parámetros de procesamiento -->
                    <div id="parametersSection" style="display: none;">
                        <h6 class="mt-4">Parámetros de Procesamiento</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="enhancement_level" class="form-label">Nivel de Mejora</label>
                                <select class="form-select" id="enhancement_level" name="enhancement_level">
                                    <option value="low">Bajo - Cambios mínimos</option>
                                    <option value="medium" selected>Medio - Mejora estándar</option>
                                    <option value="high">Alto - Mejora agresiva</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="style" class="form-label">Estilo de Salida</label>
                                <select class="form-select" id="style" name="style">
                                    <option value="realistic" selected>Fotorrealista</option>
                                    <option value="enhanced">Mejorado</option>
                                    <option value="artistic">Artístico</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="contrast" class="form-label">Contraste</label>
                                <input type="range" class="form-range" id="contrast" name="contrast" min="0.5" max="2.0" step="0.1" value="1.2">
                                <div class="d-flex justify-content-between">
                                    <small>Bajo</small>
                                    <small>Alto</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="brightness" class="form-label">Brillo</label>
                                <input type="range" class="form-range" id="brightness" name="brightness" min="0.5" max="2.0" step="0.1" value="1.1">
                                <div class="d-flex justify-content-between">
                                    <small>Oscuro</small>
                                    <small>Claro</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="noise_reduction" name="noise_reduction" checked>
                                <label class="form-check-label" for="noise_reduction">
                                    Reducción de ruido
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edge_enhancement" name="edge_enhancement" checked>
                                <label class="form-check-label" for="edge_enhancement">
                                    Mejora de bordes
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Botón de procesamiento -->
                    <div id="processSection" style="display: none;">
                        <div class="d-grid mt-4">
                            <button type="submit" class="btn btn-success btn-lg" id="processBtn">
                                <i class="bi bi-robot"></i>
                                Generar Identikit Fotorrealista
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Progreso -->
                <div id="progressSection" style="display: none;">
                    <div class="mt-4">
                        <h6>Procesando Imagen...</h6>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%" id="progressBar"></div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted" id="progressText">Iniciando procesamiento...</small>
                        </div>
                    </div>
                </div>
                
                <!-- Resultado -->
                <div id="resultSection" style="display: none;">
                    <h6 class="mt-4">Resultado del Procesamiento</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-center">Original</h6>
                            <img id="originalResult" class="image-preview w-100" alt="Original">
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-center">Procesado</h6>
                            <img id="processedResult" class="image-preview w-100" alt="Procesado">
                        </div>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <button class="btn btn-primary me-2" onclick="downloadResult()">
                            <i class="bi bi-download"></i>
                            Descargar Resultado
                        </button>
                        <button class="btn btn-success me-2" onclick="saveToCase()">
                            <i class="bi bi-save"></i>
                            Guardar en Caso
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise"></i>
                            Procesar Otra Imagen
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Panel de información -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Guía de Uso
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> Consejos</h6>
                    <ul class="mb-0 small">
                        <li>Usa imágenes de alta calidad</li>
                        <li>Bocetos claros dan mejores resultados</li>
                        <li>Evita imágenes borrosas o muy oscuras</li>
                        <li>Formatos recomendados: JPG, PNG</li>
                    </ul>
                </div>
                
                <h6><i class="bi bi-gear"></i> Parámetros</h6>
                <ul class="list-unstyled small">
                    <li><strong>Nivel Bajo:</strong> Cambios mínimos, preserva el estilo original</li>
                    <li><strong>Nivel Medio:</strong> Mejora equilibrada (recomendado)</li>
                    <li><strong>Nivel Alto:</strong> Transformación más agresiva</li>
                </ul>
                
                <h6><i class="bi bi-clock"></i> Tiempo de Procesamiento</h6>
                <p class="small text-muted">
                    El procesamiento típicamente toma entre 10-30 segundos dependiendo del tamaño de la imagen.
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i>
                    Procesamiento Local
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="bi bi-cpu" style="font-size: 3rem; color: #28a745;"></i>
                    <h6 class="mt-2">100% Offline</h6>
                    <p class="small text-muted">
                        Todo el procesamiento se realiza localmente en tu equipo. 
                        No se envían datos a servidores externos.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentFile = null;
let processedImageUrl = null;

// Configurar drag and drop
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('sketchFile');

uploadArea.addEventListener('click', () => fileInput.click());

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

function handleFileSelect(file) {
    // Validar tipo de archivo
    if (!file.type.startsWith('image/')) {
        alert('Por favor selecciona un archivo de imagen válido');
        return;
    }

    // Validar tamaño (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('El archivo es demasiado grande. Máximo 10MB');
        return;
    }

    currentFile = file;

    // Mostrar vista previa
    const reader = new FileReader();
    reader.onload = (e) => {
        document.getElementById('previewImage').src = e.target.result;
        document.getElementById('previewSection').style.display = 'block';
        document.getElementById('parametersSection').style.display = 'block';
        document.getElementById('processSection').style.display = 'block';
        document.getElementById('uploadArea').style.display = 'none';
    };
    reader.readAsDataURL(file);
}

function clearPreview() {
    currentFile = null;
    document.getElementById('previewSection').style.display = 'none';
    document.getElementById('parametersSection').style.display = 'none';
    document.getElementById('processSection').style.display = 'none';
    document.getElementById('uploadArea').style.display = 'block';
    document.getElementById('resultSection').style.display = 'none';
    fileInput.value = '';
}

// Manejar envío del formulario
document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    if (!currentFile) {
        alert('Por favor selecciona una imagen');
        return;
    }

    // Mostrar progreso
    document.getElementById('processSection').style.display = 'none';
    document.getElementById('progressSection').style.display = 'block';

    // Simular progreso
    let progress = 0;
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;

        progressBar.style.width = progress + '%';

        if (progress < 30) {
            progressText.textContent = 'Analizando imagen...';
        } else if (progress < 60) {
            progressText.textContent = 'Aplicando mejoras...';
        } else {
            progressText.textContent = 'Generando resultado...';
        }
    }, 500);

    // Crear FormData
    const formData = new FormData();
    formData.append('sketch', currentFile);
    formData.append('enhancement_level', document.getElementById('enhancement_level').value);
    formData.append('style', document.getElementById('style').value);
    formData.append('contrast', document.getElementById('contrast').value);
    formData.append('brightness', document.getElementById('brightness').value);
    formData.append('noise_reduction', document.getElementById('noise_reduction').checked);
    formData.append('edge_enhancement', document.getElementById('edge_enhancement').checked);

    try {
        const response = await fetch(`/generar-ia/{{ caso.id }}`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = 'Completado!';

        setTimeout(() => {
            document.getElementById('progressSection').style.display = 'none';

            if (result.success) {
                // Mostrar resultado
                document.getElementById('originalResult').src = URL.createObjectURL(currentFile);
                document.getElementById('processedResult').src = `/generated/${result.processed_image}`;
                processedImageUrl = `/generated/${result.processed_image}`;
                document.getElementById('resultSection').style.display = 'block';
            } else {
                alert('Error al procesar la imagen: ' + result.error);
                document.getElementById('processSection').style.display = 'block';
            }
        }, 1000);

    } catch (error) {
        clearInterval(progressInterval);
        console.error('Error:', error);
        alert('Error al procesar la imagen');
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('processSection').style.display = 'block';
    }
});

function downloadResult() {
    if (processedImageUrl) {
        const link = document.createElement('a');
        link.href = processedImageUrl;
        link.download = `identikit_${new Date().getTime()}.jpg`;
        link.click();
    }
}

function saveToCase() {
    if (processedImageUrl) {
        alert('Identikit guardado en el caso exitosamente');
    }
}

function resetForm() {
    clearPreview();
    document.getElementById('progressSection').style.display = 'none';
    processedImageUrl = null;
}
</script>
{% endblock %}
