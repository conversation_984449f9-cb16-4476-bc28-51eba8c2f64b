function Qf(i,c){for(var s=0;s<c.length;s++){const d=c[s];if(typeof d!="string"&&!Array.isArray(d)){for(const p in d)if(p!=="default"&&!(p in i)){const h=Object.getOwnPropertyDescriptor(d,p);h&&Object.defineProperty(i,p,h.get?h:{enumerable:!0,get:()=>d[p]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const p of document.querySelectorAll('link[rel="modulepreload"]'))d(p);new MutationObserver(p=>{for(const h of p)if(h.type==="childList")for(const v of h.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&d(v)}).observe(document,{childList:!0,subtree:!0});function s(p){const h={};return p.integrity&&(h.integrity=p.integrity),p.referrerPolicy&&(h.referrerPolicy=p.referrerPolicy),p.crossOrigin==="use-credentials"?h.credentials="include":p.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function d(p){if(p.ep)return;p.ep=!0;const h=s(p);fetch(p.href,h)}})();function Yo(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Eo={exports:{}},sa={},Po={exports:{}},ce={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wu;function Kf(){if(Wu)return ce;Wu=1;var i=Symbol.for("react.element"),c=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),d=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),h=Symbol.for("react.provider"),v=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),w=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),C=Symbol.iterator;function M(g){return g===null||typeof g!="object"?null:(g=C&&g[C]||g["@@iterator"],typeof g=="function"?g:null)}var Q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,I={};function O(g,P,ie){this.props=g,this.context=P,this.refs=I,this.updater=ie||Q}O.prototype.isReactComponent={},O.prototype.setState=function(g,P){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,P,"setState")},O.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function V(){}V.prototype=O.prototype;function Y(g,P,ie){this.props=g,this.context=P,this.refs=I,this.updater=ie||Q}var le=Y.prototype=new V;le.constructor=Y,E(le,O.prototype),le.isPureReactComponent=!0;var K=Array.isArray,ae=Object.prototype.hasOwnProperty,he={current:null},xe={key:!0,ref:!0,__self:!0,__source:!0};function Oe(g,P,ie){var re,de={},oe=null,ge=null;if(P!=null)for(re in P.ref!==void 0&&(ge=P.ref),P.key!==void 0&&(oe=""+P.key),P)ae.call(P,re)&&!xe.hasOwnProperty(re)&&(de[re]=P[re]);var fe=arguments.length-2;if(fe===1)de.children=ie;else if(1<fe){for(var je=Array(fe),Ve=0;Ve<fe;Ve++)je[Ve]=arguments[Ve+2];de.children=je}if(g&&g.defaultProps)for(re in fe=g.defaultProps,fe)de[re]===void 0&&(de[re]=fe[re]);return{$$typeof:i,type:g,key:oe,ref:ge,props:de,_owner:he.current}}function $e(g,P){return{$$typeof:i,type:g.type,key:P,ref:g.ref,props:g.props,_owner:g._owner}}function Fe(g){return typeof g=="object"&&g!==null&&g.$$typeof===i}function te(g){var P={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(ie){return P[ie]})}var se=/\/+/g;function we(g,P){return typeof g=="object"&&g!==null&&g.key!=null?te(""+g.key):P.toString(36)}function ue(g,P,ie,re,de){var oe=typeof g;(oe==="undefined"||oe==="boolean")&&(g=null);var ge=!1;if(g===null)ge=!0;else switch(oe){case"string":case"number":ge=!0;break;case"object":switch(g.$$typeof){case i:case c:ge=!0}}if(ge)return ge=g,de=de(ge),g=re===""?"."+we(ge,0):re,K(de)?(ie="",g!=null&&(ie=g.replace(se,"$&/")+"/"),ue(de,P,ie,"",function(Ve){return Ve})):de!=null&&(Fe(de)&&(de=$e(de,ie+(!de.key||ge&&ge.key===de.key?"":(""+de.key).replace(se,"$&/")+"/")+g)),P.push(de)),1;if(ge=0,re=re===""?".":re+":",K(g))for(var fe=0;fe<g.length;fe++){oe=g[fe];var je=re+we(oe,fe);ge+=ue(oe,P,ie,je,de)}else if(je=M(g),typeof je=="function")for(g=je.call(g),fe=0;!(oe=g.next()).done;)oe=oe.value,je=re+we(oe,fe++),ge+=ue(oe,P,ie,je,de);else if(oe==="object")throw P=String(g),Error("Objects are not valid as a React child (found: "+(P==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":P)+"). If you meant to render a collection of children, use an array instead.");return ge}function B(g,P,ie){if(g==null)return g;var re=[],de=0;return ue(g,re,"","",function(oe){return P.call(ie,oe,de++)}),re}function Ce(g){if(g._status===-1){var P=g._result;P=P(),P.then(function(ie){(g._status===0||g._status===-1)&&(g._status=1,g._result=ie)},function(ie){(g._status===0||g._status===-1)&&(g._status=2,g._result=ie)}),g._status===-1&&(g._status=0,g._result=P)}if(g._status===1)return g._result.default;throw g._result}var ve={current:null},L={transition:null},H={ReactCurrentDispatcher:ve,ReactCurrentBatchConfig:L,ReactCurrentOwner:he};function U(){throw Error("act(...) is not supported in production builds of React.")}return ce.Children={map:B,forEach:function(g,P,ie){B(g,function(){P.apply(this,arguments)},ie)},count:function(g){var P=0;return B(g,function(){P++}),P},toArray:function(g){return B(g,function(P){return P})||[]},only:function(g){if(!Fe(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},ce.Component=O,ce.Fragment=s,ce.Profiler=p,ce.PureComponent=Y,ce.StrictMode=d,ce.Suspense=b,ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=H,ce.act=U,ce.cloneElement=function(g,P,ie){if(g==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+g+".");var re=E({},g.props),de=g.key,oe=g.ref,ge=g._owner;if(P!=null){if(P.ref!==void 0&&(oe=P.ref,ge=he.current),P.key!==void 0&&(de=""+P.key),g.type&&g.type.defaultProps)var fe=g.type.defaultProps;for(je in P)ae.call(P,je)&&!xe.hasOwnProperty(je)&&(re[je]=P[je]===void 0&&fe!==void 0?fe[je]:P[je])}var je=arguments.length-2;if(je===1)re.children=ie;else if(1<je){fe=Array(je);for(var Ve=0;Ve<je;Ve++)fe[Ve]=arguments[Ve+2];re.children=fe}return{$$typeof:i,type:g.type,key:de,ref:oe,props:re,_owner:ge}},ce.createContext=function(g){return g={$$typeof:v,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},g.Provider={$$typeof:h,_context:g},g.Consumer=g},ce.createElement=Oe,ce.createFactory=function(g){var P=Oe.bind(null,g);return P.type=g,P},ce.createRef=function(){return{current:null}},ce.forwardRef=function(g){return{$$typeof:k,render:g}},ce.isValidElement=Fe,ce.lazy=function(g){return{$$typeof:z,_payload:{_status:-1,_result:g},_init:Ce}},ce.memo=function(g,P){return{$$typeof:w,type:g,compare:P===void 0?null:P}},ce.startTransition=function(g){var P=L.transition;L.transition={};try{g()}finally{L.transition=P}},ce.unstable_act=U,ce.useCallback=function(g,P){return ve.current.useCallback(g,P)},ce.useContext=function(g){return ve.current.useContext(g)},ce.useDebugValue=function(){},ce.useDeferredValue=function(g){return ve.current.useDeferredValue(g)},ce.useEffect=function(g,P){return ve.current.useEffect(g,P)},ce.useId=function(){return ve.current.useId()},ce.useImperativeHandle=function(g,P,ie){return ve.current.useImperativeHandle(g,P,ie)},ce.useInsertionEffect=function(g,P){return ve.current.useInsertionEffect(g,P)},ce.useLayoutEffect=function(g,P){return ve.current.useLayoutEffect(g,P)},ce.useMemo=function(g,P){return ve.current.useMemo(g,P)},ce.useReducer=function(g,P,ie){return ve.current.useReducer(g,P,ie)},ce.useRef=function(g){return ve.current.useRef(g)},ce.useState=function(g){return ve.current.useState(g)},ce.useSyncExternalStore=function(g,P,ie){return ve.current.useSyncExternalStore(g,P,ie)},ce.useTransition=function(){return ve.current.useTransition()},ce.version="18.3.1",ce}var Hu;function Xo(){return Hu||(Hu=1,Po.exports=Kf()),Po.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qu;function Yf(){if(qu)return sa;qu=1;var i=Xo(),c=Symbol.for("react.element"),s=Symbol.for("react.fragment"),d=Object.prototype.hasOwnProperty,p=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,h={key:!0,ref:!0,__self:!0,__source:!0};function v(k,b,w){var z,C={},M=null,Q=null;w!==void 0&&(M=""+w),b.key!==void 0&&(M=""+b.key),b.ref!==void 0&&(Q=b.ref);for(z in b)d.call(b,z)&&!h.hasOwnProperty(z)&&(C[z]=b[z]);if(k&&k.defaultProps)for(z in b=k.defaultProps,b)C[z]===void 0&&(C[z]=b[z]);return{$$typeof:c,type:k,key:M,ref:Q,props:C,_owner:p.current}}return sa.Fragment=s,sa.jsx=v,sa.jsxs=v,sa}var Gu;function Xf(){return Gu||(Gu=1,Eo.exports=Yf()),Eo.exports}var a=Xf(),S=Xo();const Zo=Yo(S),Zf=Qf({__proto__:null,default:Zo},[S]);var kl={},zo={exports:{}},ot={},_o={exports:{}},Do={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qu;function Jf(){return Qu||(Qu=1,function(i){function c(L,H){var U=L.length;L.push(H);e:for(;0<U;){var g=U-1>>>1,P=L[g];if(0<p(P,H))L[g]=H,L[U]=P,U=g;else break e}}function s(L){return L.length===0?null:L[0]}function d(L){if(L.length===0)return null;var H=L[0],U=L.pop();if(U!==H){L[0]=U;e:for(var g=0,P=L.length,ie=P>>>1;g<ie;){var re=2*(g+1)-1,de=L[re],oe=re+1,ge=L[oe];if(0>p(de,U))oe<P&&0>p(ge,de)?(L[g]=ge,L[oe]=U,g=oe):(L[g]=de,L[re]=U,g=re);else if(oe<P&&0>p(ge,U))L[g]=ge,L[oe]=U,g=oe;else break e}}return H}function p(L,H){var U=L.sortIndex-H.sortIndex;return U!==0?U:L.id-H.id}if(typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var v=Date,k=v.now();i.unstable_now=function(){return v.now()-k}}var b=[],w=[],z=1,C=null,M=3,Q=!1,E=!1,I=!1,O=typeof setTimeout=="function"?setTimeout:null,V=typeof clearTimeout=="function"?clearTimeout:null,Y=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function le(L){for(var H=s(w);H!==null;){if(H.callback===null)d(w);else if(H.startTime<=L)d(w),H.sortIndex=H.expirationTime,c(b,H);else break;H=s(w)}}function K(L){if(I=!1,le(L),!E)if(s(b)!==null)E=!0,Ce(ae);else{var H=s(w);H!==null&&ve(K,H.startTime-L)}}function ae(L,H){E=!1,I&&(I=!1,V(Oe),Oe=-1),Q=!0;var U=M;try{for(le(H),C=s(b);C!==null&&(!(C.expirationTime>H)||L&&!te());){var g=C.callback;if(typeof g=="function"){C.callback=null,M=C.priorityLevel;var P=g(C.expirationTime<=H);H=i.unstable_now(),typeof P=="function"?C.callback=P:C===s(b)&&d(b),le(H)}else d(b);C=s(b)}if(C!==null)var ie=!0;else{var re=s(w);re!==null&&ve(K,re.startTime-H),ie=!1}return ie}finally{C=null,M=U,Q=!1}}var he=!1,xe=null,Oe=-1,$e=5,Fe=-1;function te(){return!(i.unstable_now()-Fe<$e)}function se(){if(xe!==null){var L=i.unstable_now();Fe=L;var H=!0;try{H=xe(!0,L)}finally{H?we():(he=!1,xe=null)}}else he=!1}var we;if(typeof Y=="function")we=function(){Y(se)};else if(typeof MessageChannel<"u"){var ue=new MessageChannel,B=ue.port2;ue.port1.onmessage=se,we=function(){B.postMessage(null)}}else we=function(){O(se,0)};function Ce(L){xe=L,he||(he=!0,we())}function ve(L,H){Oe=O(function(){L(i.unstable_now())},H)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(L){L.callback=null},i.unstable_continueExecution=function(){E||Q||(E=!0,Ce(ae))},i.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$e=0<L?Math.floor(1e3/L):5},i.unstable_getCurrentPriorityLevel=function(){return M},i.unstable_getFirstCallbackNode=function(){return s(b)},i.unstable_next=function(L){switch(M){case 1:case 2:case 3:var H=3;break;default:H=M}var U=M;M=H;try{return L()}finally{M=U}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(L,H){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var U=M;M=L;try{return H()}finally{M=U}},i.unstable_scheduleCallback=function(L,H,U){var g=i.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?g+U:g):U=g,L){case 1:var P=-1;break;case 2:P=250;break;case 5:P=**********;break;case 4:P=1e4;break;default:P=5e3}return P=U+P,L={id:z++,callback:H,priorityLevel:L,startTime:U,expirationTime:P,sortIndex:-1},U>g?(L.sortIndex=U,c(w,L),s(b)===null&&L===s(w)&&(I?(V(Oe),Oe=-1):I=!0,ve(K,U-g))):(L.sortIndex=P,c(b,L),E||Q||(E=!0,Ce(ae))),L},i.unstable_shouldYield=te,i.unstable_wrapCallback=function(L){var H=M;return function(){var U=M;M=H;try{return L.apply(this,arguments)}finally{M=U}}}}(Do)),Do}var Ku;function em(){return Ku||(Ku=1,_o.exports=Jf()),_o.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yu;function tm(){if(Yu)return ot;Yu=1;var i=Xo(),c=em();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var d=new Set,p={};function h(e,t){v(e,t),v(e+"Capture",t)}function v(e,t){for(p[e]=t,e=0;e<t.length;e++)d.add(t[e])}var k=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),b=Object.prototype.hasOwnProperty,w=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,z={},C={};function M(e){return b.call(C,e)?!0:b.call(z,e)?!1:w.test(e)?C[e]=!0:(z[e]=!0,!1)}function Q(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function E(e,t,n,r){if(t===null||typeof t>"u"||Q(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function I(e,t,n,r,l,o,u){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=u}var O={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){O[e]=new I(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];O[t]=new I(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){O[e]=new I(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){O[e]=new I(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){O[e]=new I(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){O[e]=new I(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){O[e]=new I(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){O[e]=new I(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){O[e]=new I(e,5,!1,e.toLowerCase(),null,!1,!1)});var V=/[\-:]([a-z])/g;function Y(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(V,Y);O[t]=new I(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(V,Y);O[t]=new I(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(V,Y);O[t]=new I(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){O[e]=new I(e,1,!1,e.toLowerCase(),null,!1,!1)}),O.xlinkHref=new I("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){O[e]=new I(e,1,!1,e.toLowerCase(),null,!0,!0)});function le(e,t,n,r){var l=O.hasOwnProperty(t)?O[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(E(t,n,l,r)&&(n=null),r||l===null?M(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var K=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ae=Symbol.for("react.element"),he=Symbol.for("react.portal"),xe=Symbol.for("react.fragment"),Oe=Symbol.for("react.strict_mode"),$e=Symbol.for("react.profiler"),Fe=Symbol.for("react.provider"),te=Symbol.for("react.context"),se=Symbol.for("react.forward_ref"),we=Symbol.for("react.suspense"),ue=Symbol.for("react.suspense_list"),B=Symbol.for("react.memo"),Ce=Symbol.for("react.lazy"),ve=Symbol.for("react.offscreen"),L=Symbol.iterator;function H(e){return e===null||typeof e!="object"?null:(e=L&&e[L]||e["@@iterator"],typeof e=="function"?e:null)}var U=Object.assign,g;function P(e){if(g===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);g=t&&t[1]||""}return`
`+g+e}var ie=!1;function re(e,t){if(!e||ie)return"";ie=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(N){var r=N}Reflect.construct(e,[],t)}else{try{t.call()}catch(N){r=N}e.call(t.prototype)}else{try{throw Error()}catch(N){r=N}e()}}catch(N){if(N&&r&&typeof N.stack=="string"){for(var l=N.stack.split(`
`),o=r.stack.split(`
`),u=l.length-1,f=o.length-1;1<=u&&0<=f&&l[u]!==o[f];)f--;for(;1<=u&&0<=f;u--,f--)if(l[u]!==o[f]){if(u!==1||f!==1)do if(u--,f--,0>f||l[u]!==o[f]){var m=`
`+l[u].replace(" at new "," at ");return e.displayName&&m.includes("<anonymous>")&&(m=m.replace("<anonymous>",e.displayName)),m}while(1<=u&&0<=f);break}}}finally{ie=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?P(e):""}function de(e){switch(e.tag){case 5:return P(e.type);case 16:return P("Lazy");case 13:return P("Suspense");case 19:return P("SuspenseList");case 0:case 2:case 15:return e=re(e.type,!1),e;case 11:return e=re(e.type.render,!1),e;case 1:return e=re(e.type,!0),e;default:return""}}function oe(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case xe:return"Fragment";case he:return"Portal";case $e:return"Profiler";case Oe:return"StrictMode";case we:return"Suspense";case ue:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case te:return(e.displayName||"Context")+".Consumer";case Fe:return(e._context.displayName||"Context")+".Provider";case se:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case B:return t=e.displayName||null,t!==null?t:oe(e.type)||"Memo";case Ce:t=e._payload,e=e._init;try{return oe(e(t))}catch{}}return null}function ge(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return oe(t);case 8:return t===Oe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function je(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ve(e){var t=je(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(u){r=""+u,o.call(this,u)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(u){r=""+u},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function It(e){e._valueTracker||(e._valueTracker=Ve(e))}function Mt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=je(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function yn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Bn(e,t){var n=t.checked;return U({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function wr(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=fe(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function jr(e,t){t=t.checked,t!=null&&le(e,"checked",t,!1)}function Et(e,t){jr(e,t);var n=fe(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Gt(e,t.type,n):t.hasOwnProperty("defaultValue")&&Gt(e,t.type,fe(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function $n(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Gt(e,t,n){(t!=="number"||yn(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pt=Array.isArray;function Qt(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+fe(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function kr(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return U({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ma(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(s(92));if(Pt(n)){if(1<n.length)throw Error(s(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:fe(n)}}function A(e,t){var n=fe(t.value),r=fe(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function J(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Re(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pe(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var We,Kt=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(We=We||document.createElement("div"),We.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=We.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ft(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Lt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},br=["Webkit","ms","Moz","O"];Object.keys(Lt).forEach(function(e){br.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Lt[t]=Lt[e]})});function Vn(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Lt.hasOwnProperty(e)&&Lt[e]?(""+t).trim():t+"px"}function wn(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Vn(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Nr=U({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function jn(e,t){if(t){if(Nr[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function kn(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bn=null;function Sr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var $l=null,Wn=null,Hn=null;function os(e){if(e=Gr(e)){if(typeof $l!="function")throw Error(s(280));var t=e.stateNode;t&&(t=La(t),$l(e.stateNode,e.type,t))}}function ss(e){Wn?Hn?Hn.push(e):Hn=[e]:Wn=e}function cs(){if(Wn){var e=Wn,t=Hn;if(Hn=Wn=null,os(e),t)for(e=0;e<t.length;e++)os(t[e])}}function us(e,t){return e(t)}function ds(){}var Vl=!1;function ps(e,t,n){if(Vl)return e(t,n);Vl=!0;try{return us(e,t,n)}finally{Vl=!1,(Wn!==null||Hn!==null)&&(ds(),cs())}}function Cr(e,t){var n=e.stateNode;if(n===null)return null;var r=La(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var Wl=!1;if(k)try{var Er={};Object.defineProperty(Er,"passive",{get:function(){Wl=!0}}),window.addEventListener("test",Er,Er),window.removeEventListener("test",Er,Er)}catch{Wl=!1}function Jd(e,t,n,r,l,o,u,f,m){var N=Array.prototype.slice.call(arguments,3);try{t.apply(n,N)}catch(D){this.onError(D)}}var Pr=!1,ha=null,xa=!1,Hl=null,ep={onError:function(e){Pr=!0,ha=e}};function tp(e,t,n,r,l,o,u,f,m){Pr=!1,ha=null,Jd.apply(ep,arguments)}function np(e,t,n,r,l,o,u,f,m){if(tp.apply(this,arguments),Pr){if(Pr){var N=ha;Pr=!1,ha=null}else throw Error(s(198));xa||(xa=!0,Hl=N)}}function Nn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function fs(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ms(e){if(Nn(e)!==e)throw Error(s(188))}function rp(e){var t=e.alternate;if(!t){if(t=Nn(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return ms(l),e;if(o===r)return ms(l),t;o=o.sibling}throw Error(s(188))}if(n.return!==r.return)n=l,r=o;else{for(var u=!1,f=l.child;f;){if(f===n){u=!0,n=l,r=o;break}if(f===r){u=!0,r=l,n=o;break}f=f.sibling}if(!u){for(f=o.child;f;){if(f===n){u=!0,n=o,r=l;break}if(f===r){u=!0,r=o,n=l;break}f=f.sibling}if(!u)throw Error(s(189))}}if(n.alternate!==r)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function hs(e){return e=rp(e),e!==null?xs(e):null}function xs(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=xs(e);if(t!==null)return t;e=e.sibling}return null}var vs=c.unstable_scheduleCallback,gs=c.unstable_cancelCallback,ap=c.unstable_shouldYield,lp=c.unstable_requestPaint,Ie=c.unstable_now,ip=c.unstable_getCurrentPriorityLevel,ql=c.unstable_ImmediatePriority,ys=c.unstable_UserBlockingPriority,va=c.unstable_NormalPriority,op=c.unstable_LowPriority,ws=c.unstable_IdlePriority,ga=null,zt=null;function sp(e){if(zt&&typeof zt.onCommitFiberRoot=="function")try{zt.onCommitFiberRoot(ga,e,void 0,(e.current.flags&128)===128)}catch{}}var wt=Math.clz32?Math.clz32:dp,cp=Math.log,up=Math.LN2;function dp(e){return e>>>=0,e===0?32:31-(cp(e)/up|0)|0}var ya=64,wa=4194304;function zr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ja(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,o=e.pingedLanes,u=n&268435455;if(u!==0){var f=u&~l;f!==0?r=zr(f):(o&=u,o!==0&&(r=zr(o)))}else u=n&~l,u!==0?r=zr(u):o!==0&&(r=zr(o));if(r===0)return 0;if(t!==0&&t!==r&&(t&l)===0&&(l=r&-r,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-wt(t),l=1<<n,r|=e[n],t&=~l;return r}function pp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function fp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var u=31-wt(o),f=1<<u,m=l[u];m===-1?((f&n)===0||(f&r)!==0)&&(l[u]=pp(f,t)):m<=t&&(e.expiredLanes|=f),o&=~f}}function Gl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function js(){var e=ya;return ya<<=1,(ya&4194240)===0&&(ya=64),e}function Ql(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function _r(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-wt(t),e[t]=n}function mp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-wt(n),o=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~o}}function Kl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-wt(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var ye=0;function ks(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var bs,Yl,Ns,Ss,Cs,Xl=!1,ka=[],Yt=null,Xt=null,Zt=null,Dr=new Map,Rr=new Map,Jt=[],hp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Es(e,t){switch(e){case"focusin":case"focusout":Yt=null;break;case"dragenter":case"dragleave":Xt=null;break;case"mouseover":case"mouseout":Zt=null;break;case"pointerover":case"pointerout":Dr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rr.delete(t.pointerId)}}function Tr(e,t,n,r,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Gr(t),t!==null&&Yl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function xp(e,t,n,r,l){switch(t){case"focusin":return Yt=Tr(Yt,e,t,n,r,l),!0;case"dragenter":return Xt=Tr(Xt,e,t,n,r,l),!0;case"mouseover":return Zt=Tr(Zt,e,t,n,r,l),!0;case"pointerover":var o=l.pointerId;return Dr.set(o,Tr(Dr.get(o)||null,e,t,n,r,l)),!0;case"gotpointercapture":return o=l.pointerId,Rr.set(o,Tr(Rr.get(o)||null,e,t,n,r,l)),!0}return!1}function Ps(e){var t=Sn(e.target);if(t!==null){var n=Nn(t);if(n!==null){if(t=n.tag,t===13){if(t=fs(n),t!==null){e.blockedOn=t,Cs(e.priority,function(){Ns(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ba(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);bn=r,n.target.dispatchEvent(r),bn=null}else return t=Gr(n),t!==null&&Yl(t),e.blockedOn=n,!1;t.shift()}return!0}function zs(e,t,n){ba(e)&&n.delete(t)}function vp(){Xl=!1,Yt!==null&&ba(Yt)&&(Yt=null),Xt!==null&&ba(Xt)&&(Xt=null),Zt!==null&&ba(Zt)&&(Zt=null),Dr.forEach(zs),Rr.forEach(zs)}function Or(e,t){e.blockedOn===t&&(e.blockedOn=null,Xl||(Xl=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,vp)))}function Ir(e){function t(l){return Or(l,e)}if(0<ka.length){Or(ka[0],e);for(var n=1;n<ka.length;n++){var r=ka[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Yt!==null&&Or(Yt,e),Xt!==null&&Or(Xt,e),Zt!==null&&Or(Zt,e),Dr.forEach(t),Rr.forEach(t),n=0;n<Jt.length;n++)r=Jt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Jt.length&&(n=Jt[0],n.blockedOn===null);)Ps(n),n.blockedOn===null&&Jt.shift()}var qn=K.ReactCurrentBatchConfig,Na=!0;function gp(e,t,n,r){var l=ye,o=qn.transition;qn.transition=null;try{ye=1,Zl(e,t,n,r)}finally{ye=l,qn.transition=o}}function yp(e,t,n,r){var l=ye,o=qn.transition;qn.transition=null;try{ye=4,Zl(e,t,n,r)}finally{ye=l,qn.transition=o}}function Zl(e,t,n,r){if(Na){var l=Jl(e,t,n,r);if(l===null)xi(e,t,r,Sa,n),Es(e,r);else if(xp(l,e,t,n,r))r.stopPropagation();else if(Es(e,r),t&4&&-1<hp.indexOf(e)){for(;l!==null;){var o=Gr(l);if(o!==null&&bs(o),o=Jl(e,t,n,r),o===null&&xi(e,t,r,Sa,n),o===l)break;l=o}l!==null&&r.stopPropagation()}else xi(e,t,r,null,n)}}var Sa=null;function Jl(e,t,n,r){if(Sa=null,e=Sr(r),e=Sn(e),e!==null)if(t=Nn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=fs(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Sa=e,null}function _s(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ip()){case ql:return 1;case ys:return 4;case va:case op:return 16;case ws:return 536870912;default:return 16}default:return 16}}var en=null,ei=null,Ca=null;function Ds(){if(Ca)return Ca;var e,t=ei,n=t.length,r,l="value"in en?en.value:en.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var u=n-e;for(r=1;r<=u&&t[n-r]===l[o-r];r++);return Ca=l.slice(e,1<r?1-r:void 0)}function Ea(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Pa(){return!0}function Rs(){return!1}function st(e){function t(n,r,l,o,u){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=o,this.target=u,this.currentTarget=null;for(var f in e)e.hasOwnProperty(f)&&(n=e[f],this[f]=n?n(o):o[f]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Pa:Rs,this.isPropagationStopped=Rs,this}return U(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Pa)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Pa)},persist:function(){},isPersistent:Pa}),t}var Gn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ti=st(Gn),Mr=U({},Gn,{view:0,detail:0}),wp=st(Mr),ni,ri,Lr,za=U({},Mr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:li,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Lr&&(Lr&&e.type==="mousemove"?(ni=e.screenX-Lr.screenX,ri=e.screenY-Lr.screenY):ri=ni=0,Lr=e),ni)},movementY:function(e){return"movementY"in e?e.movementY:ri}}),Ts=st(za),jp=U({},za,{dataTransfer:0}),kp=st(jp),bp=U({},Mr,{relatedTarget:0}),ai=st(bp),Np=U({},Gn,{animationName:0,elapsedTime:0,pseudoElement:0}),Sp=st(Np),Cp=U({},Gn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ep=st(Cp),Pp=U({},Gn,{data:0}),Os=st(Pp),zp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_p={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Rp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dp[e])?!!t[e]:!1}function li(){return Rp}var Tp=U({},Mr,{key:function(e){if(e.key){var t=zp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ea(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?_p[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:li,charCode:function(e){return e.type==="keypress"?Ea(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ea(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Op=st(Tp),Ip=U({},za,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Is=st(Ip),Mp=U({},Mr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:li}),Lp=st(Mp),Ap=U({},Gn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Fp=st(Ap),Up=U({},za,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Bp=st(Up),$p=[9,13,27,32],ii=k&&"CompositionEvent"in window,Ar=null;k&&"documentMode"in document&&(Ar=document.documentMode);var Vp=k&&"TextEvent"in window&&!Ar,Ms=k&&(!ii||Ar&&8<Ar&&11>=Ar),Ls=" ",As=!1;function Fs(e,t){switch(e){case"keyup":return $p.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Us(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Qn=!1;function Wp(e,t){switch(e){case"compositionend":return Us(t);case"keypress":return t.which!==32?null:(As=!0,Ls);case"textInput":return e=t.data,e===Ls&&As?null:e;default:return null}}function Hp(e,t){if(Qn)return e==="compositionend"||!ii&&Fs(e,t)?(e=Ds(),Ca=ei=en=null,Qn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ms&&t.locale!=="ko"?null:t.data;default:return null}}var qp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!qp[e.type]:t==="textarea"}function $s(e,t,n,r){ss(r),t=Oa(t,"onChange"),0<t.length&&(n=new ti("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Fr=null,Ur=null;function Gp(e){ic(e,0)}function _a(e){var t=Jn(e);if(Mt(t))return e}function Qp(e,t){if(e==="change")return t}var Vs=!1;if(k){var oi;if(k){var si="oninput"in document;if(!si){var Ws=document.createElement("div");Ws.setAttribute("oninput","return;"),si=typeof Ws.oninput=="function"}oi=si}else oi=!1;Vs=oi&&(!document.documentMode||9<document.documentMode)}function Hs(){Fr&&(Fr.detachEvent("onpropertychange",qs),Ur=Fr=null)}function qs(e){if(e.propertyName==="value"&&_a(Ur)){var t=[];$s(t,Ur,e,Sr(e)),ps(Gp,t)}}function Kp(e,t,n){e==="focusin"?(Hs(),Fr=t,Ur=n,Fr.attachEvent("onpropertychange",qs)):e==="focusout"&&Hs()}function Yp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _a(Ur)}function Xp(e,t){if(e==="click")return _a(t)}function Zp(e,t){if(e==="input"||e==="change")return _a(t)}function Jp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var jt=typeof Object.is=="function"?Object.is:Jp;function Br(e,t){if(jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!b.call(t,l)||!jt(e[l],t[l]))return!1}return!0}function Gs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Qs(e,t){var n=Gs(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Gs(n)}}function Ks(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ks(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ys(){for(var e=window,t=yn();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=yn(e.document)}return t}function ci(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ef(e){var t=Ys(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ks(n.ownerDocument.documentElement,n)){if(r!==null&&ci(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(r.start,l);r=r.end===void 0?o:Math.min(r.end,l),!e.extend&&o>r&&(l=r,r=o,o=l),l=Qs(n,o);var u=Qs(n,r);l&&u&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==u.node||e.focusOffset!==u.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(u.node,u.offset)):(t.setEnd(u.node,u.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var tf=k&&"documentMode"in document&&11>=document.documentMode,Kn=null,ui=null,$r=null,di=!1;function Xs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;di||Kn==null||Kn!==yn(r)||(r=Kn,"selectionStart"in r&&ci(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),$r&&Br($r,r)||($r=r,r=Oa(ui,"onSelect"),0<r.length&&(t=new ti("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Kn)))}function Da(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Yn={animationend:Da("Animation","AnimationEnd"),animationiteration:Da("Animation","AnimationIteration"),animationstart:Da("Animation","AnimationStart"),transitionend:Da("Transition","TransitionEnd")},pi={},Zs={};k&&(Zs=document.createElement("div").style,"AnimationEvent"in window||(delete Yn.animationend.animation,delete Yn.animationiteration.animation,delete Yn.animationstart.animation),"TransitionEvent"in window||delete Yn.transitionend.transition);function Ra(e){if(pi[e])return pi[e];if(!Yn[e])return e;var t=Yn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Zs)return pi[e]=t[n];return e}var Js=Ra("animationend"),ec=Ra("animationiteration"),tc=Ra("animationstart"),nc=Ra("transitionend"),rc=new Map,ac="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tn(e,t){rc.set(e,t),h(t,[e])}for(var fi=0;fi<ac.length;fi++){var mi=ac[fi],nf=mi.toLowerCase(),rf=mi[0].toUpperCase()+mi.slice(1);tn(nf,"on"+rf)}tn(Js,"onAnimationEnd"),tn(ec,"onAnimationIteration"),tn(tc,"onAnimationStart"),tn("dblclick","onDoubleClick"),tn("focusin","onFocus"),tn("focusout","onBlur"),tn(nc,"onTransitionEnd"),v("onMouseEnter",["mouseout","mouseover"]),v("onMouseLeave",["mouseout","mouseover"]),v("onPointerEnter",["pointerout","pointerover"]),v("onPointerLeave",["pointerout","pointerover"]),h("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),h("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),h("onBeforeInput",["compositionend","keypress","textInput","paste"]),h("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),h("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),h("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),af=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vr));function lc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,np(r,t,void 0,e),e.currentTarget=null}function ic(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var u=r.length-1;0<=u;u--){var f=r[u],m=f.instance,N=f.currentTarget;if(f=f.listener,m!==o&&l.isPropagationStopped())break e;lc(l,f,N),o=m}else for(u=0;u<r.length;u++){if(f=r[u],m=f.instance,N=f.currentTarget,f=f.listener,m!==o&&l.isPropagationStopped())break e;lc(l,f,N),o=m}}}if(xa)throw e=Hl,xa=!1,Hl=null,e}function Ne(e,t){var n=t[ki];n===void 0&&(n=t[ki]=new Set);var r=e+"__bubble";n.has(r)||(oc(t,e,2,!1),n.add(r))}function hi(e,t,n){var r=0;t&&(r|=4),oc(n,e,r,t)}var Ta="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Ta]){e[Ta]=!0,d.forEach(function(n){n!=="selectionchange"&&(af.has(n)||hi(n,!1,e),hi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ta]||(t[Ta]=!0,hi("selectionchange",!1,t))}}function oc(e,t,n,r){switch(_s(t)){case 1:var l=gp;break;case 4:l=yp;break;default:l=Zl}n=l.bind(null,t,n,e),l=void 0,!Wl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function xi(e,t,n,r,l){var o=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var u=r.tag;if(u===3||u===4){var f=r.stateNode.containerInfo;if(f===l||f.nodeType===8&&f.parentNode===l)break;if(u===4)for(u=r.return;u!==null;){var m=u.tag;if((m===3||m===4)&&(m=u.stateNode.containerInfo,m===l||m.nodeType===8&&m.parentNode===l))return;u=u.return}for(;f!==null;){if(u=Sn(f),u===null)return;if(m=u.tag,m===5||m===6){r=o=u;continue e}f=f.parentNode}}r=r.return}ps(function(){var N=o,D=Sr(n),R=[];e:{var _=rc.get(e);if(_!==void 0){var F=ti,W=e;switch(e){case"keypress":if(Ea(n)===0)break e;case"keydown":case"keyup":F=Op;break;case"focusin":W="focus",F=ai;break;case"focusout":W="blur",F=ai;break;case"beforeblur":case"afterblur":F=ai;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":F=Ts;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":F=kp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":F=Lp;break;case Js:case ec:case tc:F=Sp;break;case nc:F=Fp;break;case"scroll":F=wp;break;case"wheel":F=Bp;break;case"copy":case"cut":case"paste":F=Ep;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":F=Is}var q=(t&4)!==0,Me=!q&&e==="scroll",y=q?_!==null?_+"Capture":null:_;q=[];for(var x=N,j;x!==null;){j=x;var T=j.stateNode;if(j.tag===5&&T!==null&&(j=T,y!==null&&(T=Cr(x,y),T!=null&&q.push(Hr(x,T,j)))),Me)break;x=x.return}0<q.length&&(_=new F(_,W,null,n,D),R.push({event:_,listeners:q}))}}if((t&7)===0){e:{if(_=e==="mouseover"||e==="pointerover",F=e==="mouseout"||e==="pointerout",_&&n!==bn&&(W=n.relatedTarget||n.fromElement)&&(Sn(W)||W[At]))break e;if((F||_)&&(_=D.window===D?D:(_=D.ownerDocument)?_.defaultView||_.parentWindow:window,F?(W=n.relatedTarget||n.toElement,F=N,W=W?Sn(W):null,W!==null&&(Me=Nn(W),W!==Me||W.tag!==5&&W.tag!==6)&&(W=null)):(F=null,W=N),F!==W)){if(q=Ts,T="onMouseLeave",y="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(q=Is,T="onPointerLeave",y="onPointerEnter",x="pointer"),Me=F==null?_:Jn(F),j=W==null?_:Jn(W),_=new q(T,x+"leave",F,n,D),_.target=Me,_.relatedTarget=j,T=null,Sn(D)===N&&(q=new q(y,x+"enter",W,n,D),q.target=j,q.relatedTarget=Me,T=q),Me=T,F&&W)t:{for(q=F,y=W,x=0,j=q;j;j=Xn(j))x++;for(j=0,T=y;T;T=Xn(T))j++;for(;0<x-j;)q=Xn(q),x--;for(;0<j-x;)y=Xn(y),j--;for(;x--;){if(q===y||y!==null&&q===y.alternate)break t;q=Xn(q),y=Xn(y)}q=null}else q=null;F!==null&&sc(R,_,F,q,!1),W!==null&&Me!==null&&sc(R,Me,W,q,!0)}}e:{if(_=N?Jn(N):window,F=_.nodeName&&_.nodeName.toLowerCase(),F==="select"||F==="input"&&_.type==="file")var G=Qp;else if(Bs(_))if(Vs)G=Zp;else{G=Yp;var X=Kp}else(F=_.nodeName)&&F.toLowerCase()==="input"&&(_.type==="checkbox"||_.type==="radio")&&(G=Xp);if(G&&(G=G(e,N))){$s(R,G,n,D);break e}X&&X(e,_,N),e==="focusout"&&(X=_._wrapperState)&&X.controlled&&_.type==="number"&&Gt(_,"number",_.value)}switch(X=N?Jn(N):window,e){case"focusin":(Bs(X)||X.contentEditable==="true")&&(Kn=X,ui=N,$r=null);break;case"focusout":$r=ui=Kn=null;break;case"mousedown":di=!0;break;case"contextmenu":case"mouseup":case"dragend":di=!1,Xs(R,n,D);break;case"selectionchange":if(tf)break;case"keydown":case"keyup":Xs(R,n,D)}var Z;if(ii)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else Qn?Fs(e,n)&&(ne="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ne="onCompositionStart");ne&&(Ms&&n.locale!=="ko"&&(Qn||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&Qn&&(Z=Ds()):(en=D,ei="value"in en?en.value:en.textContent,Qn=!0)),X=Oa(N,ne),0<X.length&&(ne=new Os(ne,e,null,n,D),R.push({event:ne,listeners:X}),Z?ne.data=Z:(Z=Us(n),Z!==null&&(ne.data=Z)))),(Z=Vp?Wp(e,n):Hp(e,n))&&(N=Oa(N,"onBeforeInput"),0<N.length&&(D=new Os("onBeforeInput","beforeinput",null,n,D),R.push({event:D,listeners:N}),D.data=Z))}ic(R,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Oa(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Cr(e,n),o!=null&&r.unshift(Hr(e,o,l)),o=Cr(e,t),o!=null&&r.push(Hr(e,o,l))),e=e.return}return r}function Xn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function sc(e,t,n,r,l){for(var o=t._reactName,u=[];n!==null&&n!==r;){var f=n,m=f.alternate,N=f.stateNode;if(m!==null&&m===r)break;f.tag===5&&N!==null&&(f=N,l?(m=Cr(n,o),m!=null&&u.unshift(Hr(n,m,f))):l||(m=Cr(n,o),m!=null&&u.push(Hr(n,m,f)))),n=n.return}u.length!==0&&e.push({event:t,listeners:u})}var lf=/\r\n?/g,of=/\u0000|\uFFFD/g;function cc(e){return(typeof e=="string"?e:""+e).replace(lf,`
`).replace(of,"")}function Ia(e,t,n){if(t=cc(t),cc(e)!==t&&n)throw Error(s(425))}function Ma(){}var vi=null,gi=null;function yi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var wi=typeof setTimeout=="function"?setTimeout:void 0,sf=typeof clearTimeout=="function"?clearTimeout:void 0,uc=typeof Promise=="function"?Promise:void 0,cf=typeof queueMicrotask=="function"?queueMicrotask:typeof uc<"u"?function(e){return uc.resolve(null).then(e).catch(uf)}:wi;function uf(e){setTimeout(function(){throw e})}function ji(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Ir(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Ir(t)}function nn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function dc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Zn=Math.random().toString(36).slice(2),_t="__reactFiber$"+Zn,qr="__reactProps$"+Zn,At="__reactContainer$"+Zn,ki="__reactEvents$"+Zn,df="__reactListeners$"+Zn,pf="__reactHandles$"+Zn;function Sn(e){var t=e[_t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[At]||n[_t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=dc(e);e!==null;){if(n=e[_t])return n;e=dc(e)}return t}e=n,n=e.parentNode}return null}function Gr(e){return e=e[_t]||e[At],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Jn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function La(e){return e[qr]||null}var bi=[],er=-1;function rn(e){return{current:e}}function Se(e){0>er||(e.current=bi[er],bi[er]=null,er--)}function be(e,t){er++,bi[er]=e.current,e.current=t}var an={},Ye=rn(an),nt=rn(!1),Cn=an;function tr(e,t){var n=e.type.contextTypes;if(!n)return an;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function rt(e){return e=e.childContextTypes,e!=null}function Aa(){Se(nt),Se(Ye)}function pc(e,t,n){if(Ye.current!==an)throw Error(s(168));be(Ye,t),be(nt,n)}function fc(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(s(108,ge(e)||"Unknown",l));return U({},n,r)}function Fa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||an,Cn=Ye.current,be(Ye,e),be(nt,nt.current),!0}function mc(e,t,n){var r=e.stateNode;if(!r)throw Error(s(169));n?(e=fc(e,t,Cn),r.__reactInternalMemoizedMergedChildContext=e,Se(nt),Se(Ye),be(Ye,e)):Se(nt),be(nt,n)}var Ft=null,Ua=!1,Ni=!1;function hc(e){Ft===null?Ft=[e]:Ft.push(e)}function ff(e){Ua=!0,hc(e)}function ln(){if(!Ni&&Ft!==null){Ni=!0;var e=0,t=ye;try{var n=Ft;for(ye=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ft=null,Ua=!1}catch(l){throw Ft!==null&&(Ft=Ft.slice(e+1)),vs(ql,ln),l}finally{ye=t,Ni=!1}}return null}var nr=[],rr=0,Ba=null,$a=0,mt=[],ht=0,En=null,Ut=1,Bt="";function Pn(e,t){nr[rr++]=$a,nr[rr++]=Ba,Ba=e,$a=t}function xc(e,t,n){mt[ht++]=Ut,mt[ht++]=Bt,mt[ht++]=En,En=e;var r=Ut;e=Bt;var l=32-wt(r)-1;r&=~(1<<l),n+=1;var o=32-wt(t)+l;if(30<o){var u=l-l%5;o=(r&(1<<u)-1).toString(32),r>>=u,l-=u,Ut=1<<32-wt(t)+l|n<<l|r,Bt=o+e}else Ut=1<<o|n<<l|r,Bt=e}function Si(e){e.return!==null&&(Pn(e,1),xc(e,1,0))}function Ci(e){for(;e===Ba;)Ba=nr[--rr],nr[rr]=null,$a=nr[--rr],nr[rr]=null;for(;e===En;)En=mt[--ht],mt[ht]=null,Bt=mt[--ht],mt[ht]=null,Ut=mt[--ht],mt[ht]=null}var ct=null,ut=null,Ee=!1,kt=null;function vc(e,t){var n=yt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function gc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ct=e,ut=nn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ct=e,ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=En!==null?{id:Ut,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=yt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ct=e,ut=null,!0):!1;default:return!1}}function Ei(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Pi(e){if(Ee){var t=ut;if(t){var n=t;if(!gc(e,t)){if(Ei(e))throw Error(s(418));t=nn(n.nextSibling);var r=ct;t&&gc(e,t)?vc(r,n):(e.flags=e.flags&-4097|2,Ee=!1,ct=e)}}else{if(Ei(e))throw Error(s(418));e.flags=e.flags&-4097|2,Ee=!1,ct=e}}}function yc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ct=e}function Va(e){if(e!==ct)return!1;if(!Ee)return yc(e),Ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!yi(e.type,e.memoizedProps)),t&&(t=ut)){if(Ei(e))throw wc(),Error(s(418));for(;t;)vc(e,t),t=nn(t.nextSibling)}if(yc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ut=nn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ut=null}}else ut=ct?nn(e.stateNode.nextSibling):null;return!0}function wc(){for(var e=ut;e;)e=nn(e.nextSibling)}function ar(){ut=ct=null,Ee=!1}function zi(e){kt===null?kt=[e]:kt.push(e)}var mf=K.ReactCurrentBatchConfig;function Qr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(s(309));var r=n.stateNode}if(!r)throw Error(s(147,e));var l=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(u){var f=l.refs;u===null?delete f[o]:f[o]=u},t._stringRef=o,t)}if(typeof e!="string")throw Error(s(284));if(!n._owner)throw Error(s(290,e))}return e}function Wa(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function jc(e){var t=e._init;return t(e._payload)}function kc(e){function t(y,x){if(e){var j=y.deletions;j===null?(y.deletions=[x],y.flags|=16):j.push(x)}}function n(y,x){if(!e)return null;for(;x!==null;)t(y,x),x=x.sibling;return null}function r(y,x){for(y=new Map;x!==null;)x.key!==null?y.set(x.key,x):y.set(x.index,x),x=x.sibling;return y}function l(y,x){return y=mn(y,x),y.index=0,y.sibling=null,y}function o(y,x,j){return y.index=j,e?(j=y.alternate,j!==null?(j=j.index,j<x?(y.flags|=2,x):j):(y.flags|=2,x)):(y.flags|=1048576,x)}function u(y){return e&&y.alternate===null&&(y.flags|=2),y}function f(y,x,j,T){return x===null||x.tag!==6?(x=jo(j,y.mode,T),x.return=y,x):(x=l(x,j),x.return=y,x)}function m(y,x,j,T){var G=j.type;return G===xe?D(y,x,j.props.children,T,j.key):x!==null&&(x.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Ce&&jc(G)===x.type)?(T=l(x,j.props),T.ref=Qr(y,x,j),T.return=y,T):(T=ml(j.type,j.key,j.props,null,y.mode,T),T.ref=Qr(y,x,j),T.return=y,T)}function N(y,x,j,T){return x===null||x.tag!==4||x.stateNode.containerInfo!==j.containerInfo||x.stateNode.implementation!==j.implementation?(x=ko(j,y.mode,T),x.return=y,x):(x=l(x,j.children||[]),x.return=y,x)}function D(y,x,j,T,G){return x===null||x.tag!==7?(x=Mn(j,y.mode,T,G),x.return=y,x):(x=l(x,j),x.return=y,x)}function R(y,x,j){if(typeof x=="string"&&x!==""||typeof x=="number")return x=jo(""+x,y.mode,j),x.return=y,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case ae:return j=ml(x.type,x.key,x.props,null,y.mode,j),j.ref=Qr(y,null,x),j.return=y,j;case he:return x=ko(x,y.mode,j),x.return=y,x;case Ce:var T=x._init;return R(y,T(x._payload),j)}if(Pt(x)||H(x))return x=Mn(x,y.mode,j,null),x.return=y,x;Wa(y,x)}return null}function _(y,x,j,T){var G=x!==null?x.key:null;if(typeof j=="string"&&j!==""||typeof j=="number")return G!==null?null:f(y,x,""+j,T);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case ae:return j.key===G?m(y,x,j,T):null;case he:return j.key===G?N(y,x,j,T):null;case Ce:return G=j._init,_(y,x,G(j._payload),T)}if(Pt(j)||H(j))return G!==null?null:D(y,x,j,T,null);Wa(y,j)}return null}function F(y,x,j,T,G){if(typeof T=="string"&&T!==""||typeof T=="number")return y=y.get(j)||null,f(x,y,""+T,G);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case ae:return y=y.get(T.key===null?j:T.key)||null,m(x,y,T,G);case he:return y=y.get(T.key===null?j:T.key)||null,N(x,y,T,G);case Ce:var X=T._init;return F(y,x,j,X(T._payload),G)}if(Pt(T)||H(T))return y=y.get(j)||null,D(x,y,T,G,null);Wa(x,T)}return null}function W(y,x,j,T){for(var G=null,X=null,Z=x,ne=x=0,Ge=null;Z!==null&&ne<j.length;ne++){Z.index>ne?(Ge=Z,Z=null):Ge=Z.sibling;var me=_(y,Z,j[ne],T);if(me===null){Z===null&&(Z=Ge);break}e&&Z&&me.alternate===null&&t(y,Z),x=o(me,x,ne),X===null?G=me:X.sibling=me,X=me,Z=Ge}if(ne===j.length)return n(y,Z),Ee&&Pn(y,ne),G;if(Z===null){for(;ne<j.length;ne++)Z=R(y,j[ne],T),Z!==null&&(x=o(Z,x,ne),X===null?G=Z:X.sibling=Z,X=Z);return Ee&&Pn(y,ne),G}for(Z=r(y,Z);ne<j.length;ne++)Ge=F(Z,y,ne,j[ne],T),Ge!==null&&(e&&Ge.alternate!==null&&Z.delete(Ge.key===null?ne:Ge.key),x=o(Ge,x,ne),X===null?G=Ge:X.sibling=Ge,X=Ge);return e&&Z.forEach(function(hn){return t(y,hn)}),Ee&&Pn(y,ne),G}function q(y,x,j,T){var G=H(j);if(typeof G!="function")throw Error(s(150));if(j=G.call(j),j==null)throw Error(s(151));for(var X=G=null,Z=x,ne=x=0,Ge=null,me=j.next();Z!==null&&!me.done;ne++,me=j.next()){Z.index>ne?(Ge=Z,Z=null):Ge=Z.sibling;var hn=_(y,Z,me.value,T);if(hn===null){Z===null&&(Z=Ge);break}e&&Z&&hn.alternate===null&&t(y,Z),x=o(hn,x,ne),X===null?G=hn:X.sibling=hn,X=hn,Z=Ge}if(me.done)return n(y,Z),Ee&&Pn(y,ne),G;if(Z===null){for(;!me.done;ne++,me=j.next())me=R(y,me.value,T),me!==null&&(x=o(me,x,ne),X===null?G=me:X.sibling=me,X=me);return Ee&&Pn(y,ne),G}for(Z=r(y,Z);!me.done;ne++,me=j.next())me=F(Z,y,ne,me.value,T),me!==null&&(e&&me.alternate!==null&&Z.delete(me.key===null?ne:me.key),x=o(me,x,ne),X===null?G=me:X.sibling=me,X=me);return e&&Z.forEach(function(Gf){return t(y,Gf)}),Ee&&Pn(y,ne),G}function Me(y,x,j,T){if(typeof j=="object"&&j!==null&&j.type===xe&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case ae:e:{for(var G=j.key,X=x;X!==null;){if(X.key===G){if(G=j.type,G===xe){if(X.tag===7){n(y,X.sibling),x=l(X,j.props.children),x.return=y,y=x;break e}}else if(X.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===Ce&&jc(G)===X.type){n(y,X.sibling),x=l(X,j.props),x.ref=Qr(y,X,j),x.return=y,y=x;break e}n(y,X);break}else t(y,X);X=X.sibling}j.type===xe?(x=Mn(j.props.children,y.mode,T,j.key),x.return=y,y=x):(T=ml(j.type,j.key,j.props,null,y.mode,T),T.ref=Qr(y,x,j),T.return=y,y=T)}return u(y);case he:e:{for(X=j.key;x!==null;){if(x.key===X)if(x.tag===4&&x.stateNode.containerInfo===j.containerInfo&&x.stateNode.implementation===j.implementation){n(y,x.sibling),x=l(x,j.children||[]),x.return=y,y=x;break e}else{n(y,x);break}else t(y,x);x=x.sibling}x=ko(j,y.mode,T),x.return=y,y=x}return u(y);case Ce:return X=j._init,Me(y,x,X(j._payload),T)}if(Pt(j))return W(y,x,j,T);if(H(j))return q(y,x,j,T);Wa(y,j)}return typeof j=="string"&&j!==""||typeof j=="number"?(j=""+j,x!==null&&x.tag===6?(n(y,x.sibling),x=l(x,j),x.return=y,y=x):(n(y,x),x=jo(j,y.mode,T),x.return=y,y=x),u(y)):n(y,x)}return Me}var lr=kc(!0),bc=kc(!1),Ha=rn(null),qa=null,ir=null,_i=null;function Di(){_i=ir=qa=null}function Ri(e){var t=Ha.current;Se(Ha),e._currentValue=t}function Ti(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function or(e,t){qa=e,_i=ir=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(at=!0),e.firstContext=null)}function xt(e){var t=e._currentValue;if(_i!==e)if(e={context:e,memoizedValue:t,next:null},ir===null){if(qa===null)throw Error(s(308));ir=e,qa.dependencies={lanes:0,firstContext:e}}else ir=ir.next=e;return t}var zn=null;function Oi(e){zn===null?zn=[e]:zn.push(e)}function Nc(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Oi(t)):(n.next=l.next,l.next=n),t.interleaved=n,$t(e,r)}function $t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var on=!1;function Ii(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function sn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(pe&2)!==0){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,$t(e,n)}return l=r.interleaved,l===null?(t.next=t,Oi(r)):(t.next=l.next,l.next=t),r.interleaved=t,$t(e,n)}function Ga(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kl(e,n)}}function Cc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var u={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=u:o=o.next=u,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Qa(e,t,n,r){var l=e.updateQueue;on=!1;var o=l.firstBaseUpdate,u=l.lastBaseUpdate,f=l.shared.pending;if(f!==null){l.shared.pending=null;var m=f,N=m.next;m.next=null,u===null?o=N:u.next=N,u=m;var D=e.alternate;D!==null&&(D=D.updateQueue,f=D.lastBaseUpdate,f!==u&&(f===null?D.firstBaseUpdate=N:f.next=N,D.lastBaseUpdate=m))}if(o!==null){var R=l.baseState;u=0,D=N=m=null,f=o;do{var _=f.lane,F=f.eventTime;if((r&_)===_){D!==null&&(D=D.next={eventTime:F,lane:0,tag:f.tag,payload:f.payload,callback:f.callback,next:null});e:{var W=e,q=f;switch(_=t,F=n,q.tag){case 1:if(W=q.payload,typeof W=="function"){R=W.call(F,R,_);break e}R=W;break e;case 3:W.flags=W.flags&-65537|128;case 0:if(W=q.payload,_=typeof W=="function"?W.call(F,R,_):W,_==null)break e;R=U({},R,_);break e;case 2:on=!0}}f.callback!==null&&f.lane!==0&&(e.flags|=64,_=l.effects,_===null?l.effects=[f]:_.push(f))}else F={eventTime:F,lane:_,tag:f.tag,payload:f.payload,callback:f.callback,next:null},D===null?(N=D=F,m=R):D=D.next=F,u|=_;if(f=f.next,f===null){if(f=l.shared.pending,f===null)break;_=f,f=_.next,_.next=null,l.lastBaseUpdate=_,l.shared.pending=null}}while(!0);if(D===null&&(m=R),l.baseState=m,l.firstBaseUpdate=N,l.lastBaseUpdate=D,t=l.shared.interleaved,t!==null){l=t;do u|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Rn|=u,e.lanes=u,e.memoizedState=R}}function Ec(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(s(191,l));l.call(r)}}}var Kr={},Dt=rn(Kr),Yr=rn(Kr),Xr=rn(Kr);function _n(e){if(e===Kr)throw Error(s(174));return e}function Mi(e,t){switch(be(Xr,t),be(Yr,e),be(Dt,Kr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Re(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Re(t,e)}Se(Dt),be(Dt,t)}function sr(){Se(Dt),Se(Yr),Se(Xr)}function Pc(e){_n(Xr.current);var t=_n(Dt.current),n=Re(t,e.type);t!==n&&(be(Yr,e),be(Dt,n))}function Li(e){Yr.current===e&&(Se(Dt),Se(Yr))}var ze=rn(0);function Ka(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ai=[];function Fi(){for(var e=0;e<Ai.length;e++)Ai[e]._workInProgressVersionPrimary=null;Ai.length=0}var Ya=K.ReactCurrentDispatcher,Ui=K.ReactCurrentBatchConfig,Dn=0,_e=null,Ue=null,He=null,Xa=!1,Zr=!1,Jr=0,hf=0;function Xe(){throw Error(s(321))}function Bi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!jt(e[n],t[n]))return!1;return!0}function $i(e,t,n,r,l,o){if(Dn=o,_e=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ya.current=e===null||e.memoizedState===null?yf:wf,e=n(r,l),Zr){o=0;do{if(Zr=!1,Jr=0,25<=o)throw Error(s(301));o+=1,He=Ue=null,t.updateQueue=null,Ya.current=jf,e=n(r,l)}while(Zr)}if(Ya.current=el,t=Ue!==null&&Ue.next!==null,Dn=0,He=Ue=_e=null,Xa=!1,t)throw Error(s(300));return e}function Vi(){var e=Jr!==0;return Jr=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return He===null?_e.memoizedState=He=e:He=He.next=e,He}function vt(){if(Ue===null){var e=_e.alternate;e=e!==null?e.memoizedState:null}else e=Ue.next;var t=He===null?_e.memoizedState:He.next;if(t!==null)He=t,Ue=e;else{if(e===null)throw Error(s(310));Ue=e,e={memoizedState:Ue.memoizedState,baseState:Ue.baseState,baseQueue:Ue.baseQueue,queue:Ue.queue,next:null},He===null?_e.memoizedState=He=e:He=He.next=e}return He}function ea(e,t){return typeof t=="function"?t(e):t}function Wi(e){var t=vt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var r=Ue,l=r.baseQueue,o=n.pending;if(o!==null){if(l!==null){var u=l.next;l.next=o.next,o.next=u}r.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,r=r.baseState;var f=u=null,m=null,N=o;do{var D=N.lane;if((Dn&D)===D)m!==null&&(m=m.next={lane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),r=N.hasEagerState?N.eagerState:e(r,N.action);else{var R={lane:D,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null};m===null?(f=m=R,u=r):m=m.next=R,_e.lanes|=D,Rn|=D}N=N.next}while(N!==null&&N!==o);m===null?u=r:m.next=f,jt(r,t.memoizedState)||(at=!0),t.memoizedState=r,t.baseState=u,t.baseQueue=m,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do o=l.lane,_e.lanes|=o,Rn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Hi(e){var t=vt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var u=l=l.next;do o=e(o,u.action),u=u.next;while(u!==l);jt(o,t.memoizedState)||(at=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function zc(){}function _c(e,t){var n=_e,r=vt(),l=t(),o=!jt(r.memoizedState,l);if(o&&(r.memoizedState=l,at=!0),r=r.queue,qi(Tc.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||He!==null&&He.memoizedState.tag&1){if(n.flags|=2048,ta(9,Rc.bind(null,n,r,l,t),void 0,null),qe===null)throw Error(s(349));(Dn&30)!==0||Dc(n,t,l)}return l}function Dc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=_e.updateQueue,t===null?(t={lastEffect:null,stores:null},_e.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Rc(e,t,n,r){t.value=n,t.getSnapshot=r,Oc(t)&&Ic(e)}function Tc(e,t,n){return n(function(){Oc(t)&&Ic(e)})}function Oc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!jt(e,n)}catch{return!0}}function Ic(e){var t=$t(e,1);t!==null&&Ct(t,e,1,-1)}function Mc(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:e},t.queue=e,e=e.dispatch=gf.bind(null,_e,e),[t.memoizedState,e]}function ta(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=_e.updateQueue,t===null?(t={lastEffect:null,stores:null},_e.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Lc(){return vt().memoizedState}function Za(e,t,n,r){var l=Rt();_e.flags|=e,l.memoizedState=ta(1|t,n,void 0,r===void 0?null:r)}function Ja(e,t,n,r){var l=vt();r=r===void 0?null:r;var o=void 0;if(Ue!==null){var u=Ue.memoizedState;if(o=u.destroy,r!==null&&Bi(r,u.deps)){l.memoizedState=ta(t,n,o,r);return}}_e.flags|=e,l.memoizedState=ta(1|t,n,o,r)}function Ac(e,t){return Za(8390656,8,e,t)}function qi(e,t){return Ja(2048,8,e,t)}function Fc(e,t){return Ja(4,2,e,t)}function Uc(e,t){return Ja(4,4,e,t)}function Bc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function $c(e,t,n){return n=n!=null?n.concat([e]):null,Ja(4,4,Bc.bind(null,t,e),n)}function Gi(){}function Vc(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Bi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wc(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Bi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hc(e,t,n){return(Dn&21)===0?(e.baseState&&(e.baseState=!1,at=!0),e.memoizedState=n):(jt(n,t)||(n=js(),_e.lanes|=n,Rn|=n,e.baseState=!0),t)}function xf(e,t){var n=ye;ye=n!==0&&4>n?n:4,e(!0);var r=Ui.transition;Ui.transition={};try{e(!1),t()}finally{ye=n,Ui.transition=r}}function qc(){return vt().memoizedState}function vf(e,t,n){var r=pn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gc(e))Qc(t,n);else if(n=Nc(e,t,n,r),n!==null){var l=tt();Ct(n,e,r,l),Kc(n,t,r)}}function gf(e,t,n){var r=pn(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gc(e))Qc(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var u=t.lastRenderedState,f=o(u,n);if(l.hasEagerState=!0,l.eagerState=f,jt(f,u)){var m=t.interleaved;m===null?(l.next=l,Oi(t)):(l.next=m.next,m.next=l),t.interleaved=l;return}}catch{}finally{}n=Nc(e,t,l,r),n!==null&&(l=tt(),Ct(n,e,r,l),Kc(n,t,r))}}function Gc(e){var t=e.alternate;return e===_e||t!==null&&t===_e}function Qc(e,t){Zr=Xa=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kc(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Kl(e,n)}}var el={readContext:xt,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useInsertionEffect:Xe,useLayoutEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useMutableSource:Xe,useSyncExternalStore:Xe,useId:Xe,unstable_isNewReconciler:!1},yf={readContext:xt,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:Ac,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Za(4194308,4,Bc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Za(4194308,4,e,t)},useInsertionEffect:function(e,t){return Za(4,2,e,t)},useMemo:function(e,t){var n=Rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=vf.bind(null,_e,e),[r.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:Mc,useDebugValue:Gi,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=Mc(!1),t=e[0];return e=xf.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=_e,l=Rt();if(Ee){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),qe===null)throw Error(s(349));(Dn&30)!==0||Dc(r,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,Ac(Tc.bind(null,r,o,e),[e]),r.flags|=2048,ta(9,Rc.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Rt(),t=qe.identifierPrefix;if(Ee){var n=Bt,r=Ut;n=(r&~(1<<32-wt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Jr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=hf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},wf={readContext:xt,useCallback:Vc,useContext:xt,useEffect:qi,useImperativeHandle:$c,useInsertionEffect:Fc,useLayoutEffect:Uc,useMemo:Wc,useReducer:Wi,useRef:Lc,useState:function(){return Wi(ea)},useDebugValue:Gi,useDeferredValue:function(e){var t=vt();return Hc(t,Ue.memoizedState,e)},useTransition:function(){var e=Wi(ea)[0],t=vt().memoizedState;return[e,t]},useMutableSource:zc,useSyncExternalStore:_c,useId:qc,unstable_isNewReconciler:!1},jf={readContext:xt,useCallback:Vc,useContext:xt,useEffect:qi,useImperativeHandle:$c,useInsertionEffect:Fc,useLayoutEffect:Uc,useMemo:Wc,useReducer:Hi,useRef:Lc,useState:function(){return Hi(ea)},useDebugValue:Gi,useDeferredValue:function(e){var t=vt();return Ue===null?t.memoizedState=e:Hc(t,Ue.memoizedState,e)},useTransition:function(){var e=Hi(ea)[0],t=vt().memoizedState;return[e,t]},useMutableSource:zc,useSyncExternalStore:_c,useId:qc,unstable_isNewReconciler:!1};function bt(e,t){if(e&&e.defaultProps){t=U({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qi(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:U({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var tl={isMounted:function(e){return(e=e._reactInternals)?Nn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tt(),l=pn(e),o=Vt(r,l);o.payload=t,n!=null&&(o.callback=n),t=sn(e,o,l),t!==null&&(Ct(t,e,l,r),Ga(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tt(),l=pn(e),o=Vt(r,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=sn(e,o,l),t!==null&&(Ct(t,e,l,r),Ga(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tt(),r=pn(e),l=Vt(n,r);l.tag=2,t!=null&&(l.callback=t),t=sn(e,l,r),t!==null&&(Ct(t,e,r,n),Ga(t,e,r))}};function Yc(e,t,n,r,l,o,u){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,u):t.prototype&&t.prototype.isPureReactComponent?!Br(n,r)||!Br(l,o):!0}function Xc(e,t,n){var r=!1,l=an,o=t.contextType;return typeof o=="object"&&o!==null?o=xt(o):(l=rt(t)?Cn:Ye.current,r=t.contextTypes,o=(r=r!=null)?tr(e,l):an),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=tl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function Zc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&tl.enqueueReplaceState(t,t.state,null)}function Ki(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Ii(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=xt(o):(o=rt(t)?Cn:Ye.current,l.context=tr(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Qi(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&tl.enqueueReplaceState(l,l.state,null),Qa(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function cr(e,t){try{var n="",r=t;do n+=de(r),r=r.return;while(r);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function Yi(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Xi(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var kf=typeof WeakMap=="function"?WeakMap:Map;function Jc(e,t,n){n=Vt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){sl||(sl=!0,fo=r),Xi(e,t)},n}function eu(e,t,n){n=Vt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Xi(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Xi(e,t),typeof r!="function"&&(un===null?un=new Set([this]):un.add(this));var u=t.stack;this.componentDidCatch(t.value,{componentStack:u!==null?u:""})}),n}function tu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new kf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Mf.bind(null,e,t,n),t.then(e,e))}function nu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ru(e,t,n,r,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Vt(-1,1),t.tag=2,sn(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var bf=K.ReactCurrentOwner,at=!1;function et(e,t,n,r){t.child=e===null?bc(t,null,n,r):lr(t,e.child,n,r)}function au(e,t,n,r,l){n=n.render;var o=t.ref;return or(t,l),r=$i(e,t,n,r,o,l),n=Vi(),e!==null&&!at?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Wt(e,t,l)):(Ee&&n&&Si(t),t.flags|=1,et(e,t,r,l),t.child)}function lu(e,t,n,r,l){if(e===null){var o=n.type;return typeof o=="function"&&!wo(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,iu(e,t,o,r,l)):(e=ml(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var u=o.memoizedProps;if(n=n.compare,n=n!==null?n:Br,n(u,r)&&e.ref===t.ref)return Wt(e,t,l)}return t.flags|=1,e=mn(o,r),e.ref=t.ref,e.return=t,t.child=e}function iu(e,t,n,r,l){if(e!==null){var o=e.memoizedProps;if(Br(o,r)&&e.ref===t.ref)if(at=!1,t.pendingProps=r=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(at=!0);else return t.lanes=e.lanes,Wt(e,t,l)}return Zi(e,t,n,r,l)}function ou(e,t,n){var r=t.pendingProps,l=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},be(dr,dt),dt|=n;else{if((n&1073741824)===0)return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,be(dr,dt),dt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,be(dr,dt),dt|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,be(dr,dt),dt|=r;return et(e,t,l,n),t.child}function su(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Zi(e,t,n,r,l){var o=rt(n)?Cn:Ye.current;return o=tr(t,o),or(t,l),n=$i(e,t,n,r,o,l),r=Vi(),e!==null&&!at?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Wt(e,t,l)):(Ee&&r&&Si(t),t.flags|=1,et(e,t,n,l),t.child)}function cu(e,t,n,r,l){if(rt(n)){var o=!0;Fa(t)}else o=!1;if(or(t,l),t.stateNode===null)rl(e,t),Xc(t,n,r),Ki(t,n,r,l),r=!0;else if(e===null){var u=t.stateNode,f=t.memoizedProps;u.props=f;var m=u.context,N=n.contextType;typeof N=="object"&&N!==null?N=xt(N):(N=rt(n)?Cn:Ye.current,N=tr(t,N));var D=n.getDerivedStateFromProps,R=typeof D=="function"||typeof u.getSnapshotBeforeUpdate=="function";R||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==r||m!==N)&&Zc(t,u,r,N),on=!1;var _=t.memoizedState;u.state=_,Qa(t,r,u,l),m=t.memoizedState,f!==r||_!==m||nt.current||on?(typeof D=="function"&&(Qi(t,n,D,r),m=t.memoizedState),(f=on||Yc(t,n,f,r,_,m,N))?(R||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=m),u.props=r,u.state=m,u.context=N,r=f):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{u=t.stateNode,Sc(e,t),f=t.memoizedProps,N=t.type===t.elementType?f:bt(t.type,f),u.props=N,R=t.pendingProps,_=u.context,m=n.contextType,typeof m=="object"&&m!==null?m=xt(m):(m=rt(n)?Cn:Ye.current,m=tr(t,m));var F=n.getDerivedStateFromProps;(D=typeof F=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==R||_!==m)&&Zc(t,u,r,m),on=!1,_=t.memoizedState,u.state=_,Qa(t,r,u,l);var W=t.memoizedState;f!==R||_!==W||nt.current||on?(typeof F=="function"&&(Qi(t,n,F,r),W=t.memoizedState),(N=on||Yc(t,n,N,r,_,W,m)||!1)?(D||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(r,W,m),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(r,W,m)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=W),u.props=r,u.state=W,u.context=m,r=N):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&_===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&_===e.memoizedState||(t.flags|=1024),r=!1)}return Ji(e,t,n,r,o,l)}function Ji(e,t,n,r,l,o){su(e,t);var u=(t.flags&128)!==0;if(!r&&!u)return l&&mc(t,n,!1),Wt(e,t,o);r=t.stateNode,bf.current=t;var f=u&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&u?(t.child=lr(t,e.child,null,o),t.child=lr(t,null,f,o)):et(e,t,f,o),t.memoizedState=r.state,l&&mc(t,n,!0),t.child}function uu(e){var t=e.stateNode;t.pendingContext?pc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&pc(e,t.context,!1),Mi(e,t.containerInfo)}function du(e,t,n,r,l){return ar(),zi(l),t.flags|=256,et(e,t,n,r),t.child}var eo={dehydrated:null,treeContext:null,retryLane:0};function to(e){return{baseLanes:e,cachePool:null,transitions:null}}function pu(e,t,n){var r=t.pendingProps,l=ze.current,o=!1,u=(t.flags&128)!==0,f;if((f=u)||(f=e!==null&&e.memoizedState===null?!1:(l&2)!==0),f?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),be(ze,l&1),e===null)return Pi(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(u=r.children,e=r.fallback,o?(r=t.mode,o=t.child,u={mode:"hidden",children:u},(r&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=u):o=hl(u,r,0,null),e=Mn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=to(n),t.memoizedState=eo,e):no(t,u));if(l=e.memoizedState,l!==null&&(f=l.dehydrated,f!==null))return Nf(e,t,u,r,f,l,n);if(o){o=r.fallback,u=t.mode,l=e.child,f=l.sibling;var m={mode:"hidden",children:r.children};return(u&1)===0&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=m,t.deletions=null):(r=mn(l,m),r.subtreeFlags=l.subtreeFlags&14680064),f!==null?o=mn(f,o):(o=Mn(o,u,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,u=e.child.memoizedState,u=u===null?to(n):{baseLanes:u.baseLanes|n,cachePool:null,transitions:u.transitions},o.memoizedState=u,o.childLanes=e.childLanes&~n,t.memoizedState=eo,r}return o=e.child,e=o.sibling,r=mn(o,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function no(e,t){return t=hl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function nl(e,t,n,r){return r!==null&&zi(r),lr(t,e.child,null,n),e=no(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Nf(e,t,n,r,l,o,u){if(n)return t.flags&256?(t.flags&=-257,r=Yi(Error(s(422))),nl(e,t,u,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,l=t.mode,r=hl({mode:"visible",children:r.children},l,0,null),o=Mn(o,l,u,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,(t.mode&1)!==0&&lr(t,e.child,null,u),t.child.memoizedState=to(u),t.memoizedState=eo,o);if((t.mode&1)===0)return nl(e,t,u,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var f=r.dgst;return r=f,o=Error(s(419)),r=Yi(o,r,void 0),nl(e,t,u,r)}if(f=(u&e.childLanes)!==0,at||f){if(r=qe,r!==null){switch(u&-u){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(r.suspendedLanes|u))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,$t(e,l),Ct(r,e,l,-1))}return yo(),r=Yi(Error(s(421))),nl(e,t,u,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Lf.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,ut=nn(l.nextSibling),ct=t,Ee=!0,kt=null,e!==null&&(mt[ht++]=Ut,mt[ht++]=Bt,mt[ht++]=En,Ut=e.id,Bt=e.overflow,En=t),t=no(t,r.children),t.flags|=4096,t)}function fu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ti(e.return,t,n)}function ro(e,t,n,r,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=l)}function mu(e,t,n){var r=t.pendingProps,l=r.revealOrder,o=r.tail;if(et(e,t,r.children,n),r=ze.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&fu(e,n,t);else if(e.tag===19)fu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(be(ze,r),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Ka(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),ro(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Ka(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}ro(t,!0,n,null,o);break;case"together":ro(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function rl(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Wt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Rn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=mn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=mn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Sf(e,t,n){switch(t.tag){case 3:uu(t),ar();break;case 5:Pc(t);break;case 1:rt(t.type)&&Fa(t);break;case 4:Mi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;be(Ha,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(be(ze,ze.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?pu(e,t,n):(be(ze,ze.current&1),e=Wt(e,t,n),e!==null?e.sibling:null);be(ze,ze.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return mu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),be(ze,ze.current),r)break;return null;case 22:case 23:return t.lanes=0,ou(e,t,n)}return Wt(e,t,n)}var hu,ao,xu,vu;hu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},ao=function(){},xu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,_n(Dt.current);var o=null;switch(n){case"input":l=Bn(e,l),r=Bn(e,r),o=[];break;case"select":l=U({},l,{value:void 0}),r=U({},r,{value:void 0}),o=[];break;case"textarea":l=kr(e,l),r=kr(e,r),o=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ma)}jn(n,r);var u;n=null;for(N in l)if(!r.hasOwnProperty(N)&&l.hasOwnProperty(N)&&l[N]!=null)if(N==="style"){var f=l[N];for(u in f)f.hasOwnProperty(u)&&(n||(n={}),n[u]="")}else N!=="dangerouslySetInnerHTML"&&N!=="children"&&N!=="suppressContentEditableWarning"&&N!=="suppressHydrationWarning"&&N!=="autoFocus"&&(p.hasOwnProperty(N)?o||(o=[]):(o=o||[]).push(N,null));for(N in r){var m=r[N];if(f=l!=null?l[N]:void 0,r.hasOwnProperty(N)&&m!==f&&(m!=null||f!=null))if(N==="style")if(f){for(u in f)!f.hasOwnProperty(u)||m&&m.hasOwnProperty(u)||(n||(n={}),n[u]="");for(u in m)m.hasOwnProperty(u)&&f[u]!==m[u]&&(n||(n={}),n[u]=m[u])}else n||(o||(o=[]),o.push(N,n)),n=m;else N==="dangerouslySetInnerHTML"?(m=m?m.__html:void 0,f=f?f.__html:void 0,m!=null&&f!==m&&(o=o||[]).push(N,m)):N==="children"?typeof m!="string"&&typeof m!="number"||(o=o||[]).push(N,""+m):N!=="suppressContentEditableWarning"&&N!=="suppressHydrationWarning"&&(p.hasOwnProperty(N)?(m!=null&&N==="onScroll"&&Ne("scroll",e),o||f===m||(o=[])):(o=o||[]).push(N,m))}n&&(o=o||[]).push("style",n);var N=o;(t.updateQueue=N)&&(t.flags|=4)}},vu=function(e,t,n,r){n!==r&&(t.flags|=4)};function na(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Cf(e,t,n){var r=t.pendingProps;switch(Ci(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ze(t),null;case 1:return rt(t.type)&&Aa(),Ze(t),null;case 3:return r=t.stateNode,sr(),Se(nt),Se(Ye),Fi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Va(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,kt!==null&&(xo(kt),kt=null))),ao(e,t),Ze(t),null;case 5:Li(t);var l=_n(Xr.current);if(n=t.type,e!==null&&t.stateNode!=null)xu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(s(166));return Ze(t),null}if(e=_n(Dt.current),Va(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[_t]=t,r[qr]=o,e=(t.mode&1)!==0,n){case"dialog":Ne("cancel",r),Ne("close",r);break;case"iframe":case"object":case"embed":Ne("load",r);break;case"video":case"audio":for(l=0;l<Vr.length;l++)Ne(Vr[l],r);break;case"source":Ne("error",r);break;case"img":case"image":case"link":Ne("error",r),Ne("load",r);break;case"details":Ne("toggle",r);break;case"input":wr(r,o),Ne("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Ne("invalid",r);break;case"textarea":ma(r,o),Ne("invalid",r)}jn(n,o),l=null;for(var u in o)if(o.hasOwnProperty(u)){var f=o[u];u==="children"?typeof f=="string"?r.textContent!==f&&(o.suppressHydrationWarning!==!0&&Ia(r.textContent,f,e),l=["children",f]):typeof f=="number"&&r.textContent!==""+f&&(o.suppressHydrationWarning!==!0&&Ia(r.textContent,f,e),l=["children",""+f]):p.hasOwnProperty(u)&&f!=null&&u==="onScroll"&&Ne("scroll",r)}switch(n){case"input":It(r),$n(r,o,!0);break;case"textarea":It(r),J(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ma)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{u=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pe(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=u.createElement(n,{is:r.is}):(e=u.createElement(n),n==="select"&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,n),e[_t]=t,e[qr]=r,hu(e,t,!1,!1),t.stateNode=e;e:{switch(u=kn(n,r),n){case"dialog":Ne("cancel",e),Ne("close",e),l=r;break;case"iframe":case"object":case"embed":Ne("load",e),l=r;break;case"video":case"audio":for(l=0;l<Vr.length;l++)Ne(Vr[l],e);l=r;break;case"source":Ne("error",e),l=r;break;case"img":case"image":case"link":Ne("error",e),Ne("load",e),l=r;break;case"details":Ne("toggle",e),l=r;break;case"input":wr(e,r),l=Bn(e,r),Ne("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=U({},r,{value:void 0}),Ne("invalid",e);break;case"textarea":ma(e,r),l=kr(e,r),Ne("invalid",e);break;default:l=r}jn(n,l),f=l;for(o in f)if(f.hasOwnProperty(o)){var m=f[o];o==="style"?wn(e,m):o==="dangerouslySetInnerHTML"?(m=m?m.__html:void 0,m!=null&&Kt(e,m)):o==="children"?typeof m=="string"?(n!=="textarea"||m!=="")&&ft(e,m):typeof m=="number"&&ft(e,""+m):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(p.hasOwnProperty(o)?m!=null&&o==="onScroll"&&Ne("scroll",e):m!=null&&le(e,o,m,u))}switch(n){case"input":It(e),$n(e,r,!1);break;case"textarea":It(e),J(e);break;case"option":r.value!=null&&e.setAttribute("value",""+fe(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Qt(e,!!r.multiple,o,!1):r.defaultValue!=null&&Qt(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Ma)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ze(t),null;case 6:if(e&&t.stateNode!=null)vu(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(s(166));if(n=_n(Xr.current),_n(Dt.current),Va(t)){if(r=t.stateNode,n=t.memoizedProps,r[_t]=t,(o=r.nodeValue!==n)&&(e=ct,e!==null))switch(e.tag){case 3:Ia(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ia(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[_t]=t,t.stateNode=r}return Ze(t),null;case 13:if(Se(ze),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ee&&ut!==null&&(t.mode&1)!==0&&(t.flags&128)===0)wc(),ar(),t.flags|=98560,o=!1;else if(o=Va(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(s(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(s(317));o[_t]=t}else ar(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ze(t),o=!1}else kt!==null&&(xo(kt),kt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(ze.current&1)!==0?Be===0&&(Be=3):yo())),t.updateQueue!==null&&(t.flags|=4),Ze(t),null);case 4:return sr(),ao(e,t),e===null&&Wr(t.stateNode.containerInfo),Ze(t),null;case 10:return Ri(t.type._context),Ze(t),null;case 17:return rt(t.type)&&Aa(),Ze(t),null;case 19:if(Se(ze),o=t.memoizedState,o===null)return Ze(t),null;if(r=(t.flags&128)!==0,u=o.rendering,u===null)if(r)na(o,!1);else{if(Be!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Ka(e),u!==null){for(t.flags|=128,na(o,!1),r=u.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,u=o.alternate,u===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=u.childLanes,o.lanes=u.lanes,o.child=u.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=u.memoizedProps,o.memoizedState=u.memoizedState,o.updateQueue=u.updateQueue,o.type=u.type,e=u.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return be(ze,ze.current&1|2),t.child}e=e.sibling}o.tail!==null&&Ie()>pr&&(t.flags|=128,r=!0,na(o,!1),t.lanes=4194304)}else{if(!r)if(e=Ka(u),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),na(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!Ee)return Ze(t),null}else 2*Ie()-o.renderingStartTime>pr&&n!==1073741824&&(t.flags|=128,r=!0,na(o,!1),t.lanes=4194304);o.isBackwards?(u.sibling=t.child,t.child=u):(n=o.last,n!==null?n.sibling=u:t.child=u,o.last=u)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ie(),t.sibling=null,n=ze.current,be(ze,r?n&1|2:n&1),t):(Ze(t),null);case 22:case 23:return go(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(dt&1073741824)!==0&&(Ze(t),t.subtreeFlags&6&&(t.flags|=8192)):Ze(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function Ef(e,t){switch(Ci(t),t.tag){case 1:return rt(t.type)&&Aa(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return sr(),Se(nt),Se(Ye),Fi(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Li(t),null;case 13:if(Se(ze),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));ar()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Se(ze),null;case 4:return sr(),null;case 10:return Ri(t.type._context),null;case 22:case 23:return go(),null;case 24:return null;default:return null}}var al=!1,Je=!1,Pf=typeof WeakSet=="function"?WeakSet:Set,$=null;function ur(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Te(e,t,r)}else n.current=null}function lo(e,t,n){try{n()}catch(r){Te(e,t,r)}}var gu=!1;function zf(e,t){if(vi=Na,e=Ys(),ci(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var u=0,f=-1,m=-1,N=0,D=0,R=e,_=null;t:for(;;){for(var F;R!==n||l!==0&&R.nodeType!==3||(f=u+l),R!==o||r!==0&&R.nodeType!==3||(m=u+r),R.nodeType===3&&(u+=R.nodeValue.length),(F=R.firstChild)!==null;)_=R,R=F;for(;;){if(R===e)break t;if(_===n&&++N===l&&(f=u),_===o&&++D===r&&(m=u),(F=R.nextSibling)!==null)break;R=_,_=R.parentNode}R=F}n=f===-1||m===-1?null:{start:f,end:m}}else n=null}n=n||{start:0,end:0}}else n=null;for(gi={focusedElem:e,selectionRange:n},Na=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var W=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(W!==null){var q=W.memoizedProps,Me=W.memoizedState,y=t.stateNode,x=y.getSnapshotBeforeUpdate(t.elementType===t.type?q:bt(t.type,q),Me);y.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var j=t.stateNode.containerInfo;j.nodeType===1?j.textContent="":j.nodeType===9&&j.documentElement&&j.removeChild(j.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(T){Te(t,t.return,T)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return W=gu,gu=!1,W}function ra(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&lo(t,n,o)}l=l.next}while(l!==r)}}function ll(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function io(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function yu(e){var t=e.alternate;t!==null&&(e.alternate=null,yu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[_t],delete t[qr],delete t[ki],delete t[df],delete t[pf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function wu(e){return e.tag===5||e.tag===3||e.tag===4}function ju(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function oo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ma));else if(r!==4&&(e=e.child,e!==null))for(oo(e,t,n),e=e.sibling;e!==null;)oo(e,t,n),e=e.sibling}function so(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(so(e,t,n),e=e.sibling;e!==null;)so(e,t,n),e=e.sibling}var Qe=null,Nt=!1;function cn(e,t,n){for(n=n.child;n!==null;)ku(e,t,n),n=n.sibling}function ku(e,t,n){if(zt&&typeof zt.onCommitFiberUnmount=="function")try{zt.onCommitFiberUnmount(ga,n)}catch{}switch(n.tag){case 5:Je||ur(n,t);case 6:var r=Qe,l=Nt;Qe=null,cn(e,t,n),Qe=r,Nt=l,Qe!==null&&(Nt?(e=Qe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Qe.removeChild(n.stateNode));break;case 18:Qe!==null&&(Nt?(e=Qe,n=n.stateNode,e.nodeType===8?ji(e.parentNode,n):e.nodeType===1&&ji(e,n),Ir(e)):ji(Qe,n.stateNode));break;case 4:r=Qe,l=Nt,Qe=n.stateNode.containerInfo,Nt=!0,cn(e,t,n),Qe=r,Nt=l;break;case 0:case 11:case 14:case 15:if(!Je&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var o=l,u=o.destroy;o=o.tag,u!==void 0&&((o&2)!==0||(o&4)!==0)&&lo(n,t,u),l=l.next}while(l!==r)}cn(e,t,n);break;case 1:if(!Je&&(ur(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(f){Te(n,t,f)}cn(e,t,n);break;case 21:cn(e,t,n);break;case 22:n.mode&1?(Je=(r=Je)||n.memoizedState!==null,cn(e,t,n),Je=r):cn(e,t,n);break;default:cn(e,t,n)}}function bu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Pf),t.forEach(function(r){var l=Af.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function St(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var o=e,u=t,f=u;e:for(;f!==null;){switch(f.tag){case 5:Qe=f.stateNode,Nt=!1;break e;case 3:Qe=f.stateNode.containerInfo,Nt=!0;break e;case 4:Qe=f.stateNode.containerInfo,Nt=!0;break e}f=f.return}if(Qe===null)throw Error(s(160));ku(o,u,l),Qe=null,Nt=!1;var m=l.alternate;m!==null&&(m.return=null),l.return=null}catch(N){Te(l,t,N)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Nu(t,e),t=t.sibling}function Nu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Tt(e),r&4){try{ra(3,e,e.return),ll(3,e)}catch(q){Te(e,e.return,q)}try{ra(5,e,e.return)}catch(q){Te(e,e.return,q)}}break;case 1:St(t,e),Tt(e),r&512&&n!==null&&ur(n,n.return);break;case 5:if(St(t,e),Tt(e),r&512&&n!==null&&ur(n,n.return),e.flags&32){var l=e.stateNode;try{ft(l,"")}catch(q){Te(e,e.return,q)}}if(r&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,u=n!==null?n.memoizedProps:o,f=e.type,m=e.updateQueue;if(e.updateQueue=null,m!==null)try{f==="input"&&o.type==="radio"&&o.name!=null&&jr(l,o),kn(f,u);var N=kn(f,o);for(u=0;u<m.length;u+=2){var D=m[u],R=m[u+1];D==="style"?wn(l,R):D==="dangerouslySetInnerHTML"?Kt(l,R):D==="children"?ft(l,R):le(l,D,R,N)}switch(f){case"input":Et(l,o);break;case"textarea":A(l,o);break;case"select":var _=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var F=o.value;F!=null?Qt(l,!!o.multiple,F,!1):_!==!!o.multiple&&(o.defaultValue!=null?Qt(l,!!o.multiple,o.defaultValue,!0):Qt(l,!!o.multiple,o.multiple?[]:"",!1))}l[qr]=o}catch(q){Te(e,e.return,q)}}break;case 6:if(St(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(s(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(q){Te(e,e.return,q)}}break;case 3:if(St(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ir(t.containerInfo)}catch(q){Te(e,e.return,q)}break;case 4:St(t,e),Tt(e);break;case 13:St(t,e),Tt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(po=Ie())),r&4&&bu(e);break;case 22:if(D=n!==null&&n.memoizedState!==null,e.mode&1?(Je=(N=Je)||D,St(t,e),Je=N):St(t,e),Tt(e),r&8192){if(N=e.memoizedState!==null,(e.stateNode.isHidden=N)&&!D&&(e.mode&1)!==0)for($=e,D=e.child;D!==null;){for(R=$=D;$!==null;){switch(_=$,F=_.child,_.tag){case 0:case 11:case 14:case 15:ra(4,_,_.return);break;case 1:ur(_,_.return);var W=_.stateNode;if(typeof W.componentWillUnmount=="function"){r=_,n=_.return;try{t=r,W.props=t.memoizedProps,W.state=t.memoizedState,W.componentWillUnmount()}catch(q){Te(r,n,q)}}break;case 5:ur(_,_.return);break;case 22:if(_.memoizedState!==null){Eu(R);continue}}F!==null?(F.return=_,$=F):Eu(R)}D=D.sibling}e:for(D=null,R=e;;){if(R.tag===5){if(D===null){D=R;try{l=R.stateNode,N?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(f=R.stateNode,m=R.memoizedProps.style,u=m!=null&&m.hasOwnProperty("display")?m.display:null,f.style.display=Vn("display",u))}catch(q){Te(e,e.return,q)}}}else if(R.tag===6){if(D===null)try{R.stateNode.nodeValue=N?"":R.memoizedProps}catch(q){Te(e,e.return,q)}}else if((R.tag!==22&&R.tag!==23||R.memoizedState===null||R===e)&&R.child!==null){R.child.return=R,R=R.child;continue}if(R===e)break e;for(;R.sibling===null;){if(R.return===null||R.return===e)break e;D===R&&(D=null),R=R.return}D===R&&(D=null),R.sibling.return=R.return,R=R.sibling}}break;case 19:St(t,e),Tt(e),r&4&&bu(e);break;case 21:break;default:St(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(wu(n)){var r=n;break e}n=n.return}throw Error(s(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(ft(l,""),r.flags&=-33);var o=ju(e);so(e,o,l);break;case 3:case 4:var u=r.stateNode.containerInfo,f=ju(e);oo(e,f,u);break;default:throw Error(s(161))}}catch(m){Te(e,e.return,m)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function _f(e,t,n){$=e,Su(e)}function Su(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var l=$,o=l.child;if(l.tag===22&&r){var u=l.memoizedState!==null||al;if(!u){var f=l.alternate,m=f!==null&&f.memoizedState!==null||Je;f=al;var N=Je;if(al=u,(Je=m)&&!N)for($=l;$!==null;)u=$,m=u.child,u.tag===22&&u.memoizedState!==null?Pu(l):m!==null?(m.return=u,$=m):Pu(l);for(;o!==null;)$=o,Su(o),o=o.sibling;$=l,al=f,Je=N}Cu(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,$=o):Cu(e)}}function Cu(e){for(;$!==null;){var t=$;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Je||ll(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Je)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:bt(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ec(t,o,r);break;case 3:var u=t.updateQueue;if(u!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ec(t,u,n)}break;case 5:var f=t.stateNode;if(n===null&&t.flags&4){n=f;var m=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":m.autoFocus&&n.focus();break;case"img":m.src&&(n.src=m.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var N=t.alternate;if(N!==null){var D=N.memoizedState;if(D!==null){var R=D.dehydrated;R!==null&&Ir(R)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}Je||t.flags&512&&io(t)}catch(_){Te(t,t.return,_)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function Eu(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function Pu(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ll(4,t)}catch(m){Te(t,n,m)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(m){Te(t,l,m)}}var o=t.return;try{io(t)}catch(m){Te(t,o,m)}break;case 5:var u=t.return;try{io(t)}catch(m){Te(t,u,m)}}}catch(m){Te(t,t.return,m)}if(t===e){$=null;break}var f=t.sibling;if(f!==null){f.return=t.return,$=f;break}$=t.return}}var Df=Math.ceil,il=K.ReactCurrentDispatcher,co=K.ReactCurrentOwner,gt=K.ReactCurrentBatchConfig,pe=0,qe=null,Le=null,Ke=0,dt=0,dr=rn(0),Be=0,aa=null,Rn=0,ol=0,uo=0,la=null,lt=null,po=0,pr=1/0,Ht=null,sl=!1,fo=null,un=null,cl=!1,dn=null,ul=0,ia=0,mo=null,dl=-1,pl=0;function tt(){return(pe&6)!==0?Ie():dl!==-1?dl:dl=Ie()}function pn(e){return(e.mode&1)===0?1:(pe&2)!==0&&Ke!==0?Ke&-Ke:mf.transition!==null?(pl===0&&(pl=js()),pl):(e=ye,e!==0||(e=window.event,e=e===void 0?16:_s(e.type)),e)}function Ct(e,t,n,r){if(50<ia)throw ia=0,mo=null,Error(s(185));_r(e,n,r),((pe&2)===0||e!==qe)&&(e===qe&&((pe&2)===0&&(ol|=n),Be===4&&fn(e,Ke)),it(e,r),n===1&&pe===0&&(t.mode&1)===0&&(pr=Ie()+500,Ua&&ln()))}function it(e,t){var n=e.callbackNode;fp(e,t);var r=ja(e,e===qe?Ke:0);if(r===0)n!==null&&gs(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&gs(n),t===1)e.tag===0?ff(_u.bind(null,e)):hc(_u.bind(null,e)),cf(function(){(pe&6)===0&&ln()}),n=null;else{switch(ks(r)){case 1:n=ql;break;case 4:n=ys;break;case 16:n=va;break;case 536870912:n=ws;break;default:n=va}n=Au(n,zu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function zu(e,t){if(dl=-1,pl=0,(pe&6)!==0)throw Error(s(327));var n=e.callbackNode;if(fr()&&e.callbackNode!==n)return null;var r=ja(e,e===qe?Ke:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=fl(e,r);else{t=r;var l=pe;pe|=2;var o=Ru();(qe!==e||Ke!==t)&&(Ht=null,pr=Ie()+500,On(e,t));do try{Of();break}catch(f){Du(e,f)}while(!0);Di(),il.current=o,pe=l,Le!==null?t=0:(qe=null,Ke=0,t=Be)}if(t!==0){if(t===2&&(l=Gl(e),l!==0&&(r=l,t=ho(e,l))),t===1)throw n=aa,On(e,0),fn(e,r),it(e,Ie()),n;if(t===6)fn(e,r);else{if(l=e.current.alternate,(r&30)===0&&!Rf(l)&&(t=fl(e,r),t===2&&(o=Gl(e),o!==0&&(r=o,t=ho(e,o))),t===1))throw n=aa,On(e,0),fn(e,r),it(e,Ie()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(s(345));case 2:In(e,lt,Ht);break;case 3:if(fn(e,r),(r&130023424)===r&&(t=po+500-Ie(),10<t)){if(ja(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){tt(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=wi(In.bind(null,e,lt,Ht),t);break}In(e,lt,Ht);break;case 4:if(fn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var u=31-wt(r);o=1<<u,u=t[u],u>l&&(l=u),r&=~o}if(r=l,r=Ie()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Df(r/1960))-r,10<r){e.timeoutHandle=wi(In.bind(null,e,lt,Ht),r);break}In(e,lt,Ht);break;case 5:In(e,lt,Ht);break;default:throw Error(s(329))}}}return it(e,Ie()),e.callbackNode===n?zu.bind(null,e):null}function ho(e,t){var n=la;return e.current.memoizedState.isDehydrated&&(On(e,t).flags|=256),e=fl(e,t),e!==2&&(t=lt,lt=n,t!==null&&xo(t)),e}function xo(e){lt===null?lt=e:lt.push.apply(lt,e)}function Rf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],o=l.getSnapshot;l=l.value;try{if(!jt(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function fn(e,t){for(t&=~uo,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-wt(t),r=1<<n;e[n]=-1,t&=~r}}function _u(e){if((pe&6)!==0)throw Error(s(327));fr();var t=ja(e,0);if((t&1)===0)return it(e,Ie()),null;var n=fl(e,t);if(e.tag!==0&&n===2){var r=Gl(e);r!==0&&(t=r,n=ho(e,r))}if(n===1)throw n=aa,On(e,0),fn(e,t),it(e,Ie()),n;if(n===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,In(e,lt,Ht),it(e,Ie()),null}function vo(e,t){var n=pe;pe|=1;try{return e(t)}finally{pe=n,pe===0&&(pr=Ie()+500,Ua&&ln())}}function Tn(e){dn!==null&&dn.tag===0&&(pe&6)===0&&fr();var t=pe;pe|=1;var n=gt.transition,r=ye;try{if(gt.transition=null,ye=1,e)return e()}finally{ye=r,gt.transition=n,pe=t,(pe&6)===0&&ln()}}function go(){dt=dr.current,Se(dr)}function On(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,sf(n)),Le!==null)for(n=Le.return;n!==null;){var r=n;switch(Ci(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Aa();break;case 3:sr(),Se(nt),Se(Ye),Fi();break;case 5:Li(r);break;case 4:sr();break;case 13:Se(ze);break;case 19:Se(ze);break;case 10:Ri(r.type._context);break;case 22:case 23:go()}n=n.return}if(qe=e,Le=e=mn(e.current,null),Ke=dt=t,Be=0,aa=null,uo=ol=Rn=0,lt=la=null,zn!==null){for(t=0;t<zn.length;t++)if(n=zn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,o=n.pending;if(o!==null){var u=o.next;o.next=l,r.next=u}n.pending=r}zn=null}return e}function Du(e,t){do{var n=Le;try{if(Di(),Ya.current=el,Xa){for(var r=_e.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Xa=!1}if(Dn=0,He=Ue=_e=null,Zr=!1,Jr=0,co.current=null,n===null||n.return===null){Be=1,aa=t,Le=null;break}e:{var o=e,u=n.return,f=n,m=t;if(t=Ke,f.flags|=32768,m!==null&&typeof m=="object"&&typeof m.then=="function"){var N=m,D=f,R=D.tag;if((D.mode&1)===0&&(R===0||R===11||R===15)){var _=D.alternate;_?(D.updateQueue=_.updateQueue,D.memoizedState=_.memoizedState,D.lanes=_.lanes):(D.updateQueue=null,D.memoizedState=null)}var F=nu(u);if(F!==null){F.flags&=-257,ru(F,u,f,o,t),F.mode&1&&tu(o,N,t),t=F,m=N;var W=t.updateQueue;if(W===null){var q=new Set;q.add(m),t.updateQueue=q}else W.add(m);break e}else{if((t&1)===0){tu(o,N,t),yo();break e}m=Error(s(426))}}else if(Ee&&f.mode&1){var Me=nu(u);if(Me!==null){(Me.flags&65536)===0&&(Me.flags|=256),ru(Me,u,f,o,t),zi(cr(m,f));break e}}o=m=cr(m,f),Be!==4&&(Be=2),la===null?la=[o]:la.push(o),o=u;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=Jc(o,m,t);Cc(o,y);break e;case 1:f=m;var x=o.type,j=o.stateNode;if((o.flags&128)===0&&(typeof x.getDerivedStateFromError=="function"||j!==null&&typeof j.componentDidCatch=="function"&&(un===null||!un.has(j)))){o.flags|=65536,t&=-t,o.lanes|=t;var T=eu(o,f,t);Cc(o,T);break e}}o=o.return}while(o!==null)}Ou(n)}catch(G){t=G,Le===n&&n!==null&&(Le=n=n.return);continue}break}while(!0)}function Ru(){var e=il.current;return il.current=el,e===null?el:e}function yo(){(Be===0||Be===3||Be===2)&&(Be=4),qe===null||(Rn&268435455)===0&&(ol&268435455)===0||fn(qe,Ke)}function fl(e,t){var n=pe;pe|=2;var r=Ru();(qe!==e||Ke!==t)&&(Ht=null,On(e,t));do try{Tf();break}catch(l){Du(e,l)}while(!0);if(Di(),pe=n,il.current=r,Le!==null)throw Error(s(261));return qe=null,Ke=0,Be}function Tf(){for(;Le!==null;)Tu(Le)}function Of(){for(;Le!==null&&!ap();)Tu(Le)}function Tu(e){var t=Lu(e.alternate,e,dt);e.memoizedProps=e.pendingProps,t===null?Ou(e):Le=t,co.current=null}function Ou(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=Cf(n,t,dt),n!==null){Le=n;return}}else{if(n=Ef(n,t),n!==null){n.flags&=32767,Le=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Be=6,Le=null;return}}if(t=t.sibling,t!==null){Le=t;return}Le=t=e}while(t!==null);Be===0&&(Be=5)}function In(e,t,n){var r=ye,l=gt.transition;try{gt.transition=null,ye=1,If(e,t,n,r)}finally{gt.transition=l,ye=r}return null}function If(e,t,n,r){do fr();while(dn!==null);if((pe&6)!==0)throw Error(s(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(mp(e,o),e===qe&&(Le=qe=null,Ke=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||cl||(cl=!0,Au(va,function(){return fr(),null})),o=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||o){o=gt.transition,gt.transition=null;var u=ye;ye=1;var f=pe;pe|=4,co.current=null,zf(e,n),Nu(n,e),ef(gi),Na=!!vi,gi=vi=null,e.current=n,_f(n),lp(),pe=f,ye=u,gt.transition=o}else e.current=n;if(cl&&(cl=!1,dn=e,ul=l),o=e.pendingLanes,o===0&&(un=null),sp(n.stateNode),it(e,Ie()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(sl)throw sl=!1,e=fo,fo=null,e;return(ul&1)!==0&&e.tag!==0&&fr(),o=e.pendingLanes,(o&1)!==0?e===mo?ia++:(ia=0,mo=e):ia=0,ln(),null}function fr(){if(dn!==null){var e=ks(ul),t=gt.transition,n=ye;try{if(gt.transition=null,ye=16>e?16:e,dn===null)var r=!1;else{if(e=dn,dn=null,ul=0,(pe&6)!==0)throw Error(s(331));var l=pe;for(pe|=4,$=e.current;$!==null;){var o=$,u=o.child;if(($.flags&16)!==0){var f=o.deletions;if(f!==null){for(var m=0;m<f.length;m++){var N=f[m];for($=N;$!==null;){var D=$;switch(D.tag){case 0:case 11:case 15:ra(8,D,o)}var R=D.child;if(R!==null)R.return=D,$=R;else for(;$!==null;){D=$;var _=D.sibling,F=D.return;if(yu(D),D===N){$=null;break}if(_!==null){_.return=F,$=_;break}$=F}}}var W=o.alternate;if(W!==null){var q=W.child;if(q!==null){W.child=null;do{var Me=q.sibling;q.sibling=null,q=Me}while(q!==null)}}$=o}}if((o.subtreeFlags&2064)!==0&&u!==null)u.return=o,$=u;else e:for(;$!==null;){if(o=$,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:ra(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,$=y;break e}$=o.return}}var x=e.current;for($=x;$!==null;){u=$;var j=u.child;if((u.subtreeFlags&2064)!==0&&j!==null)j.return=u,$=j;else e:for(u=x;$!==null;){if(f=$,(f.flags&2048)!==0)try{switch(f.tag){case 0:case 11:case 15:ll(9,f)}}catch(G){Te(f,f.return,G)}if(f===u){$=null;break e}var T=f.sibling;if(T!==null){T.return=f.return,$=T;break e}$=f.return}}if(pe=l,ln(),zt&&typeof zt.onPostCommitFiberRoot=="function")try{zt.onPostCommitFiberRoot(ga,e)}catch{}r=!0}return r}finally{ye=n,gt.transition=t}}return!1}function Iu(e,t,n){t=cr(n,t),t=Jc(e,t,1),e=sn(e,t,1),t=tt(),e!==null&&(_r(e,1,t),it(e,t))}function Te(e,t,n){if(e.tag===3)Iu(e,e,n);else for(;t!==null;){if(t.tag===3){Iu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(un===null||!un.has(r))){e=cr(n,e),e=eu(t,e,1),t=sn(t,e,1),e=tt(),t!==null&&(_r(t,1,e),it(t,e));break}}t=t.return}}function Mf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=tt(),e.pingedLanes|=e.suspendedLanes&n,qe===e&&(Ke&n)===n&&(Be===4||Be===3&&(Ke&130023424)===Ke&&500>Ie()-po?On(e,0):uo|=n),it(e,t)}function Mu(e,t){t===0&&((e.mode&1)===0?t=1:(t=wa,wa<<=1,(wa&130023424)===0&&(wa=4194304)));var n=tt();e=$t(e,t),e!==null&&(_r(e,t,n),it(e,n))}function Lf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Mu(e,n)}function Af(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(s(314))}r!==null&&r.delete(t),Mu(e,n)}var Lu;Lu=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||nt.current)at=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return at=!1,Sf(e,t,n);at=(e.flags&131072)!==0}else at=!1,Ee&&(t.flags&1048576)!==0&&xc(t,$a,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;rl(e,t),e=t.pendingProps;var l=tr(t,Ye.current);or(t,n),l=$i(null,t,r,e,l,n);var o=Vi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,rt(r)?(o=!0,Fa(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ii(t),l.updater=tl,t.stateNode=l,l._reactInternals=t,Ki(t,r,e,n),t=Ji(null,t,r,!0,o,n)):(t.tag=0,Ee&&o&&Si(t),et(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(rl(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Uf(r),e=bt(r,e),l){case 0:t=Zi(null,t,r,e,n);break e;case 1:t=cu(null,t,r,e,n);break e;case 11:t=au(null,t,r,e,n);break e;case 14:t=lu(null,t,r,bt(r.type,e),n);break e}throw Error(s(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:bt(r,l),Zi(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:bt(r,l),cu(e,t,r,l,n);case 3:e:{if(uu(t),e===null)throw Error(s(387));r=t.pendingProps,o=t.memoizedState,l=o.element,Sc(e,t),Qa(t,r,null,n);var u=t.memoizedState;if(r=u.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:u.cache,pendingSuspenseBoundaries:u.pendingSuspenseBoundaries,transitions:u.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=cr(Error(s(423)),t),t=du(e,t,r,n,l);break e}else if(r!==l){l=cr(Error(s(424)),t),t=du(e,t,r,n,l);break e}else for(ut=nn(t.stateNode.containerInfo.firstChild),ct=t,Ee=!0,kt=null,n=bc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ar(),r===l){t=Wt(e,t,n);break e}et(e,t,r,n)}t=t.child}return t;case 5:return Pc(t),e===null&&Pi(t),r=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,u=l.children,yi(r,l)?u=null:o!==null&&yi(r,o)&&(t.flags|=32),su(e,t),et(e,t,u,n),t.child;case 6:return e===null&&Pi(t),null;case 13:return pu(e,t,n);case 4:return Mi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=lr(t,null,r,n):et(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:bt(r,l),au(e,t,r,l,n);case 7:return et(e,t,t.pendingProps,n),t.child;case 8:return et(e,t,t.pendingProps.children,n),t.child;case 12:return et(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,o=t.memoizedProps,u=l.value,be(Ha,r._currentValue),r._currentValue=u,o!==null)if(jt(o.value,u)){if(o.children===l.children&&!nt.current){t=Wt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var f=o.dependencies;if(f!==null){u=o.child;for(var m=f.firstContext;m!==null;){if(m.context===r){if(o.tag===1){m=Vt(-1,n&-n),m.tag=2;var N=o.updateQueue;if(N!==null){N=N.shared;var D=N.pending;D===null?m.next=m:(m.next=D.next,D.next=m),N.pending=m}}o.lanes|=n,m=o.alternate,m!==null&&(m.lanes|=n),Ti(o.return,n,t),f.lanes|=n;break}m=m.next}}else if(o.tag===10)u=o.type===t.type?null:o.child;else if(o.tag===18){if(u=o.return,u===null)throw Error(s(341));u.lanes|=n,f=u.alternate,f!==null&&(f.lanes|=n),Ti(u,n,t),u=o.sibling}else u=o.child;if(u!==null)u.return=o;else for(u=o;u!==null;){if(u===t){u=null;break}if(o=u.sibling,o!==null){o.return=u.return,u=o;break}u=u.return}o=u}et(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,or(t,n),l=xt(l),r=r(l),t.flags|=1,et(e,t,r,n),t.child;case 14:return r=t.type,l=bt(r,t.pendingProps),l=bt(r.type,l),lu(e,t,r,l,n);case 15:return iu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:bt(r,l),rl(e,t),t.tag=1,rt(r)?(e=!0,Fa(t)):e=!1,or(t,n),Xc(t,r,l),Ki(t,r,l,n),Ji(null,t,r,!0,e,n);case 19:return mu(e,t,n);case 22:return ou(e,t,n)}throw Error(s(156,t.tag))};function Au(e,t){return vs(e,t)}function Ff(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function yt(e,t,n,r){return new Ff(e,t,n,r)}function wo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Uf(e){if(typeof e=="function")return wo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===se)return 11;if(e===B)return 14}return 2}function mn(e,t){var n=e.alternate;return n===null?(n=yt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ml(e,t,n,r,l,o){var u=2;if(r=e,typeof e=="function")wo(e)&&(u=1);else if(typeof e=="string")u=5;else e:switch(e){case xe:return Mn(n.children,l,o,t);case Oe:u=8,l|=8;break;case $e:return e=yt(12,n,t,l|2),e.elementType=$e,e.lanes=o,e;case we:return e=yt(13,n,t,l),e.elementType=we,e.lanes=o,e;case ue:return e=yt(19,n,t,l),e.elementType=ue,e.lanes=o,e;case ve:return hl(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Fe:u=10;break e;case te:u=9;break e;case se:u=11;break e;case B:u=14;break e;case Ce:u=16,r=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=yt(u,n,t,l),t.elementType=e,t.type=r,t.lanes=o,t}function Mn(e,t,n,r){return e=yt(7,e,r,t),e.lanes=n,e}function hl(e,t,n,r){return e=yt(22,e,r,t),e.elementType=ve,e.lanes=n,e.stateNode={isHidden:!1},e}function jo(e,t,n){return e=yt(6,e,null,t),e.lanes=n,e}function ko(e,t,n){return t=yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ql(0),this.expirationTimes=Ql(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ql(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function bo(e,t,n,r,l,o,u,f,m){return e=new Bf(e,t,n,f,m),t===1?(t=1,o===!0&&(t|=8)):t=0,o=yt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ii(o),e}function $f(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:he,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Fu(e){if(!e)return an;e=e._reactInternals;e:{if(Nn(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(rt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var n=e.type;if(rt(n))return fc(e,n,t)}return t}function Uu(e,t,n,r,l,o,u,f,m){return e=bo(n,r,!0,e,l,o,u,f,m),e.context=Fu(null),n=e.current,r=tt(),l=pn(n),o=Vt(r,l),o.callback=t??null,sn(n,o,l),e.current.lanes=l,_r(e,l,r),it(e,r),e}function xl(e,t,n,r){var l=t.current,o=tt(),u=pn(l);return n=Fu(n),t.context===null?t.context=n:t.pendingContext=n,t=Vt(o,u),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=sn(l,t,u),e!==null&&(Ct(e,l,u,o),Ga(e,l,u)),u}function vl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Bu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function No(e,t){Bu(e,t),(e=e.alternate)&&Bu(e,t)}function Vf(){return null}var $u=typeof reportError=="function"?reportError:function(e){console.error(e)};function So(e){this._internalRoot=e}gl.prototype.render=So.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));xl(e,t,null,null)},gl.prototype.unmount=So.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Tn(function(){xl(null,e,null,null)}),t[At]=null}};function gl(e){this._internalRoot=e}gl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ss();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Jt.length&&t!==0&&t<Jt[n].priority;n++);Jt.splice(n,0,e),n===0&&Ps(e)}};function Co(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function yl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Vu(){}function Wf(e,t,n,r,l){if(l){if(typeof r=="function"){var o=r;r=function(){var N=vl(u);o.call(N)}}var u=Uu(t,r,e,0,null,!1,!1,"",Vu);return e._reactRootContainer=u,e[At]=u.current,Wr(e.nodeType===8?e.parentNode:e),Tn(),u}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var f=r;r=function(){var N=vl(m);f.call(N)}}var m=bo(e,0,!1,null,null,!1,!1,"",Vu);return e._reactRootContainer=m,e[At]=m.current,Wr(e.nodeType===8?e.parentNode:e),Tn(function(){xl(t,m,n,r)}),m}function wl(e,t,n,r,l){var o=n._reactRootContainer;if(o){var u=o;if(typeof l=="function"){var f=l;l=function(){var m=vl(u);f.call(m)}}xl(t,u,e,l)}else u=Wf(n,t,e,l,r);return vl(u)}bs=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=zr(t.pendingLanes);n!==0&&(Kl(t,n|1),it(t,Ie()),(pe&6)===0&&(pr=Ie()+500,ln()))}break;case 13:Tn(function(){var r=$t(e,1);if(r!==null){var l=tt();Ct(r,e,1,l)}}),No(e,1)}},Yl=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var n=tt();Ct(t,e,134217728,n)}No(e,134217728)}},Ns=function(e){if(e.tag===13){var t=pn(e),n=$t(e,t);if(n!==null){var r=tt();Ct(n,e,t,r)}No(e,t)}},Ss=function(){return ye},Cs=function(e,t){var n=ye;try{return ye=e,t()}finally{ye=n}},$l=function(e,t,n){switch(t){case"input":if(Et(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=La(r);if(!l)throw Error(s(90));Mt(r),Et(r,l)}}}break;case"textarea":A(e,n);break;case"select":t=n.value,t!=null&&Qt(e,!!n.multiple,t,!1)}},us=vo,ds=Tn;var Hf={usingClientEntryPoint:!1,Events:[Gr,Jn,La,ss,cs,vo]},oa={findFiberByHostInstance:Sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},qf={bundleType:oa.bundleType,version:oa.version,rendererPackageName:oa.rendererPackageName,rendererConfig:oa.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:K.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=hs(e),e===null?null:e.stateNode},findFiberByHostInstance:oa.findFiberByHostInstance||Vf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var jl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!jl.isDisabled&&jl.supportsFiber)try{ga=jl.inject(qf),zt=jl}catch{}}return ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hf,ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Co(t))throw Error(s(200));return $f(e,t,null,n)},ot.createRoot=function(e,t){if(!Co(e))throw Error(s(299));var n=!1,r="",l=$u;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=bo(e,1,!1,null,null,n,!1,r,l),e[At]=t.current,Wr(e.nodeType===8?e.parentNode:e),new So(t)},ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=hs(t),e=e===null?null:e.stateNode,e},ot.flushSync=function(e){return Tn(e)},ot.hydrate=function(e,t,n){if(!yl(t))throw Error(s(200));return wl(null,e,t,!0,n)},ot.hydrateRoot=function(e,t,n){if(!Co(e))throw Error(s(405));var r=n!=null&&n.hydratedSources||null,l=!1,o="",u=$u;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(u=n.onRecoverableError)),t=Uu(t,null,e,1,n??null,l,!1,o,u),e[At]=t.current,Wr(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new gl(t)},ot.render=function(e,t,n){if(!yl(t))throw Error(s(200));return wl(null,e,t,!1,n)},ot.unmountComponentAtNode=function(e){if(!yl(e))throw Error(s(40));return e._reactRootContainer?(Tn(function(){wl(null,null,e,!1,function(){e._reactRootContainer=null,e[At]=null})}),!0):!1},ot.unstable_batchedUpdates=vo,ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!yl(n))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return wl(e,t,n,!1,r)},ot.version="18.3.1-next-f1338f8080-20240426",ot}var Xu;function Sd(){if(Xu)return zo.exports;Xu=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),zo.exports=tm(),zo.exports}var Zu;function nm(){if(Zu)return kl;Zu=1;var i=Sd();return kl.createRoot=i.createRoot,kl.hydrateRoot=i.hydrateRoot,kl}var rm=nm();const am=i=>i instanceof Error?i.message+`
`+i.stack:JSON.stringify(i,null,2);class lm extends Zo.Component{constructor(c){super(c),this.state={hasError:!1,error:null}}static getDerivedStateFromError(c){return{hasError:!0,error:c}}render(){return this.state.hasError?a.jsxs("div",{className:"p-4 border border-red-500 rounded",children:[a.jsx("h2",{className:"text-red-500",children:"Something went wrong."}),a.jsx("pre",{className:"mt-2 text-sm",children:am(this.state.error)})]}):this.props.children}}Sd();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ca(){return ca=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(i[d]=s[d])}return i},ca.apply(this,arguments)}var xn;(function(i){i.Pop="POP",i.Push="PUSH",i.Replace="REPLACE"})(xn||(xn={}));const Ju="popstate";function im(i){i===void 0&&(i={});function c(d,p){let{pathname:h,search:v,hash:k}=d.location;return Ao("",{pathname:h,search:v,hash:k},p.state&&p.state.usr||null,p.state&&p.state.key||"default")}function s(d,p){return typeof p=="string"?p:Pl(p)}return sm(c,s,null,i)}function Ae(i,c){if(i===!1||i===null||typeof i>"u")throw new Error(c)}function Cd(i,c){if(!i){typeof console<"u"&&console.warn(c);try{throw new Error(c)}catch{}}}function om(){return Math.random().toString(36).substr(2,8)}function ed(i,c){return{usr:i.state,key:i.key,idx:c}}function Ao(i,c,s,d){return s===void 0&&(s=null),ca({pathname:typeof i=="string"?i:i.pathname,search:"",hash:""},typeof c=="string"?gr(c):c,{state:s,key:c&&c.key||d||om()})}function Pl(i){let{pathname:c="/",search:s="",hash:d=""}=i;return s&&s!=="?"&&(c+=s.charAt(0)==="?"?s:"?"+s),d&&d!=="#"&&(c+=d.charAt(0)==="#"?d:"#"+d),c}function gr(i){let c={};if(i){let s=i.indexOf("#");s>=0&&(c.hash=i.substr(s),i=i.substr(0,s));let d=i.indexOf("?");d>=0&&(c.search=i.substr(d),i=i.substr(0,d)),i&&(c.pathname=i)}return c}function sm(i,c,s,d){d===void 0&&(d={});let{window:p=document.defaultView,v5Compat:h=!1}=d,v=p.history,k=xn.Pop,b=null,w=z();w==null&&(w=0,v.replaceState(ca({},v.state,{idx:w}),""));function z(){return(v.state||{idx:null}).idx}function C(){k=xn.Pop;let O=z(),V=O==null?null:O-w;w=O,b&&b({action:k,location:I.location,delta:V})}function M(O,V){k=xn.Push;let Y=Ao(I.location,O,V);w=z()+1;let le=ed(Y,w),K=I.createHref(Y);try{v.pushState(le,"",K)}catch(ae){if(ae instanceof DOMException&&ae.name==="DataCloneError")throw ae;p.location.assign(K)}h&&b&&b({action:k,location:I.location,delta:1})}function Q(O,V){k=xn.Replace;let Y=Ao(I.location,O,V);w=z();let le=ed(Y,w),K=I.createHref(Y);v.replaceState(le,"",K),h&&b&&b({action:k,location:I.location,delta:0})}function E(O){let V=p.location.origin!=="null"?p.location.origin:p.location.href,Y=typeof O=="string"?O:Pl(O);return Y=Y.replace(/ $/,"%20"),Ae(V,"No window.location.(origin|href) available to create URL for href: "+Y),new URL(Y,V)}let I={get action(){return k},get location(){return i(p,v)},listen(O){if(b)throw new Error("A history only accepts one active listener");return p.addEventListener(Ju,C),b=O,()=>{p.removeEventListener(Ju,C),b=null}},createHref(O){return c(p,O)},createURL:E,encodeLocation(O){let V=E(O);return{pathname:V.pathname,search:V.search,hash:V.hash}},push:M,replace:Q,go(O){return v.go(O)}};return I}var td;(function(i){i.data="data",i.deferred="deferred",i.redirect="redirect",i.error="error"})(td||(td={}));function cm(i,c,s){return s===void 0&&(s="/"),um(i,c,s)}function um(i,c,s,d){let p=typeof c=="string"?gr(c):c,h=Jo(p.pathname||"/",s);if(h==null)return null;let v=Ed(i);dm(v);let k=null;for(let b=0;k==null&&b<v.length;++b){let w=bm(h);k=wm(v[b],w)}return k}function Ed(i,c,s,d){c===void 0&&(c=[]),s===void 0&&(s=[]),d===void 0&&(d="");let p=(h,v,k)=>{let b={relativePath:k===void 0?h.path||"":k,caseSensitive:h.caseSensitive===!0,childrenIndex:v,route:h};b.relativePath.startsWith("/")&&(Ae(b.relativePath.startsWith(d),'Absolute route path "'+b.relativePath+'" nested under path '+('"'+d+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),b.relativePath=b.relativePath.slice(d.length));let w=vn([d,b.relativePath]),z=s.concat(b);h.children&&h.children.length>0&&(Ae(h.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+w+'".')),Ed(h.children,c,z,w)),!(h.path==null&&!h.index)&&c.push({path:w,score:gm(w,h.index),routesMeta:z})};return i.forEach((h,v)=>{var k;if(h.path===""||!((k=h.path)!=null&&k.includes("?")))p(h,v);else for(let b of Pd(h.path))p(h,v,b)}),c}function Pd(i){let c=i.split("/");if(c.length===0)return[];let[s,...d]=c,p=s.endsWith("?"),h=s.replace(/\?$/,"");if(d.length===0)return p?[h,""]:[h];let v=Pd(d.join("/")),k=[];return k.push(...v.map(b=>b===""?h:[h,b].join("/"))),p&&k.push(...v),k.map(b=>i.startsWith("/")&&b===""?"/":b)}function dm(i){i.sort((c,s)=>c.score!==s.score?s.score-c.score:ym(c.routesMeta.map(d=>d.childrenIndex),s.routesMeta.map(d=>d.childrenIndex)))}const pm=/^:[\w-]+$/,fm=3,mm=2,hm=1,xm=10,vm=-2,nd=i=>i==="*";function gm(i,c){let s=i.split("/"),d=s.length;return s.some(nd)&&(d+=vm),c&&(d+=mm),s.filter(p=>!nd(p)).reduce((p,h)=>p+(pm.test(h)?fm:h===""?hm:xm),d)}function ym(i,c){return i.length===c.length&&i.slice(0,-1).every((d,p)=>d===c[p])?i[i.length-1]-c[c.length-1]:0}function wm(i,c,s){let{routesMeta:d}=i,p={},h="/",v=[];for(let k=0;k<d.length;++k){let b=d[k],w=k===d.length-1,z=h==="/"?c:c.slice(h.length)||"/",C=jm({path:b.relativePath,caseSensitive:b.caseSensitive,end:w},z),M=b.route;if(!C)return null;Object.assign(p,C.params),v.push({params:p,pathname:vn([h,C.pathname]),pathnameBase:Em(vn([h,C.pathnameBase])),route:M}),C.pathnameBase!=="/"&&(h=vn([h,C.pathnameBase]))}return v}function jm(i,c){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[s,d]=km(i.path,i.caseSensitive,i.end),p=c.match(s);if(!p)return null;let h=p[0],v=h.replace(/(.)\/+$/,"$1"),k=p.slice(1);return{params:d.reduce((w,z,C)=>{let{paramName:M,isOptional:Q}=z;if(M==="*"){let I=k[C]||"";v=h.slice(0,h.length-I.length).replace(/(.)\/+$/,"$1")}const E=k[C];return Q&&!E?w[M]=void 0:w[M]=(E||"").replace(/%2F/g,"/"),w},{}),pathname:h,pathnameBase:v,pattern:i}}function km(i,c,s){c===void 0&&(c=!1),s===void 0&&(s=!0),Cd(i==="*"||!i.endsWith("*")||i.endsWith("/*"),'Route path "'+i+'" will be treated as if it were '+('"'+i.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+i.replace(/\*$/,"/*")+'".'));let d=[],p="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(v,k,b)=>(d.push({paramName:k,isOptional:b!=null}),b?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(d.push({paramName:"*"}),p+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?p+="\\/*$":i!==""&&i!=="/"&&(p+="(?:(?=\\/|$))"),[new RegExp(p,c?void 0:"i"),d]}function bm(i){try{return i.split("/").map(c=>decodeURIComponent(c).replace(/\//g,"%2F")).join("/")}catch(c){return Cd(!1,'The URL path "'+i+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+c+").")),i}}function Jo(i,c){if(c==="/")return i;if(!i.toLowerCase().startsWith(c.toLowerCase()))return null;let s=c.endsWith("/")?c.length-1:c.length,d=i.charAt(s);return d&&d!=="/"?null:i.slice(s)||"/"}function Nm(i,c){c===void 0&&(c="/");let{pathname:s,search:d="",hash:p=""}=typeof i=="string"?gr(i):i;return{pathname:s?s.startsWith("/")?s:Sm(s,c):c,search:Pm(d),hash:zm(p)}}function Sm(i,c){let s=c.replace(/\/+$/,"").split("/");return i.split("/").forEach(p=>{p===".."?s.length>1&&s.pop():p!=="."&&s.push(p)}),s.length>1?s.join("/"):"/"}function Ro(i,c,s,d){return"Cannot include a '"+i+"' character in a manually specified "+("`to."+c+"` field ["+JSON.stringify(d)+"].  Please separate it out to the ")+("`to."+s+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Cm(i){return i.filter((c,s)=>s===0||c.route.path&&c.route.path.length>0)}function zd(i,c){let s=Cm(i);return c?s.map((d,p)=>p===s.length-1?d.pathname:d.pathnameBase):s.map(d=>d.pathnameBase)}function _d(i,c,s,d){d===void 0&&(d=!1);let p;typeof i=="string"?p=gr(i):(p=ca({},i),Ae(!p.pathname||!p.pathname.includes("?"),Ro("?","pathname","search",p)),Ae(!p.pathname||!p.pathname.includes("#"),Ro("#","pathname","hash",p)),Ae(!p.search||!p.search.includes("#"),Ro("#","search","hash",p)));let h=i===""||p.pathname==="",v=h?"/":p.pathname,k;if(v==null)k=s;else{let C=c.length-1;if(!d&&v.startsWith("..")){let M=v.split("/");for(;M[0]==="..";)M.shift(),C-=1;p.pathname=M.join("/")}k=C>=0?c[C]:"/"}let b=Nm(p,k),w=v&&v!=="/"&&v.endsWith("/"),z=(h||v===".")&&s.endsWith("/");return!b.pathname.endsWith("/")&&(w||z)&&(b.pathname+="/"),b}const vn=i=>i.join("/").replace(/\/\/+/g,"/"),Em=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),Pm=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,zm=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function _m(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}const Dd=["post","put","patch","delete"];new Set(Dd);const Dm=["get",...Dd];new Set(Dm);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ua(){return ua=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(i[d]=s[d])}return i},ua.apply(this,arguments)}const es=S.createContext(null),Rm=S.createContext(null),Fn=S.createContext(null),Ll=S.createContext(null),gn=S.createContext({outlet:null,matches:[],isDataRoute:!1}),Rd=S.createContext(null);function Tm(i,c){let{relative:s}=c===void 0?{}:c;fa()||Ae(!1);let{basename:d,navigator:p}=S.useContext(Fn),{hash:h,pathname:v,search:k}=Od(i,{relative:s}),b=v;return d!=="/"&&(b=v==="/"?d:vn([d,v])),p.createHref({pathname:b,search:k,hash:h})}function fa(){return S.useContext(Ll)!=null}function yr(){return fa()||Ae(!1),S.useContext(Ll).location}function Td(i){S.useContext(Fn).static||S.useLayoutEffect(i)}function Al(){let{isDataRoute:i}=S.useContext(gn);return i?Gm():Om()}function Om(){fa()||Ae(!1);let i=S.useContext(es),{basename:c,future:s,navigator:d}=S.useContext(Fn),{matches:p}=S.useContext(gn),{pathname:h}=yr(),v=JSON.stringify(zd(p,s.v7_relativeSplatPath)),k=S.useRef(!1);return Td(()=>{k.current=!0}),S.useCallback(function(w,z){if(z===void 0&&(z={}),!k.current)return;if(typeof w=="number"){d.go(w);return}let C=_d(w,JSON.parse(v),h,z.relative==="path");i==null&&c!=="/"&&(C.pathname=C.pathname==="/"?c:vn([c,C.pathname])),(z.replace?d.replace:d.push)(C,z.state,z)},[c,d,v,h,i])}function Im(){let{matches:i}=S.useContext(gn),c=i[i.length-1];return c?c.params:{}}function Od(i,c){let{relative:s}=c===void 0?{}:c,{future:d}=S.useContext(Fn),{matches:p}=S.useContext(gn),{pathname:h}=yr(),v=JSON.stringify(zd(p,d.v7_relativeSplatPath));return S.useMemo(()=>_d(i,JSON.parse(v),h,s==="path"),[i,v,h,s])}function Mm(i,c){return Lm(i,c)}function Lm(i,c,s,d){fa()||Ae(!1);let{navigator:p,static:h}=S.useContext(Fn),{matches:v}=S.useContext(gn),k=v[v.length-1],b=k?k.params:{};k&&k.pathname;let w=k?k.pathnameBase:"/";k&&k.route;let z=yr(),C;if(c){var M;let V=typeof c=="string"?gr(c):c;w==="/"||(M=V.pathname)!=null&&M.startsWith(w)||Ae(!1),C=V}else C=z;let Q=C.pathname||"/",E=Q;if(w!=="/"){let V=w.replace(/^\//,"").split("/");E="/"+Q.replace(/^\//,"").split("/").slice(V.length).join("/")}let I=cm(i,{pathname:E}),O=$m(I&&I.map(V=>Object.assign({},V,{params:Object.assign({},b,V.params),pathname:vn([w,p.encodeLocation?p.encodeLocation(V.pathname).pathname:V.pathname]),pathnameBase:V.pathnameBase==="/"?w:vn([w,p.encodeLocation?p.encodeLocation(V.pathnameBase).pathname:V.pathnameBase])})),v,s,d);return c&&O?S.createElement(Ll.Provider,{value:{location:ua({pathname:"/",search:"",hash:"",state:null,key:"default"},C),navigationType:xn.Pop}},O):O}function Am(){let i=qm(),c=_m(i)?i.status+" "+i.statusText:i instanceof Error?i.message:JSON.stringify(i),s=i instanceof Error?i.stack:null,p={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},c),s?S.createElement("pre",{style:p},s):null,null)}const Fm=S.createElement(Am,null);class Um extends S.Component{constructor(c){super(c),this.state={location:c.location,revalidation:c.revalidation,error:c.error}}static getDerivedStateFromError(c){return{error:c}}static getDerivedStateFromProps(c,s){return s.location!==c.location||s.revalidation!=="idle"&&c.revalidation==="idle"?{error:c.error,location:c.location,revalidation:c.revalidation}:{error:c.error!==void 0?c.error:s.error,location:s.location,revalidation:c.revalidation||s.revalidation}}componentDidCatch(c,s){console.error("React Router caught the following error during render",c,s)}render(){return this.state.error!==void 0?S.createElement(gn.Provider,{value:this.props.routeContext},S.createElement(Rd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Bm(i){let{routeContext:c,match:s,children:d}=i,p=S.useContext(es);return p&&p.static&&p.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(p.staticContext._deepestRenderedBoundaryId=s.route.id),S.createElement(gn.Provider,{value:c},d)}function $m(i,c,s,d){var p;if(c===void 0&&(c=[]),s===void 0&&(s=null),d===void 0&&(d=null),i==null){var h;if(!s)return null;if(s.errors)i=s.matches;else if((h=d)!=null&&h.v7_partialHydration&&c.length===0&&!s.initialized&&s.matches.length>0)i=s.matches;else return null}let v=i,k=(p=s)==null?void 0:p.errors;if(k!=null){let z=v.findIndex(C=>C.route.id&&(k==null?void 0:k[C.route.id])!==void 0);z>=0||Ae(!1),v=v.slice(0,Math.min(v.length,z+1))}let b=!1,w=-1;if(s&&d&&d.v7_partialHydration)for(let z=0;z<v.length;z++){let C=v[z];if((C.route.HydrateFallback||C.route.hydrateFallbackElement)&&(w=z),C.route.id){let{loaderData:M,errors:Q}=s,E=C.route.loader&&M[C.route.id]===void 0&&(!Q||Q[C.route.id]===void 0);if(C.route.lazy||E){b=!0,w>=0?v=v.slice(0,w+1):v=[v[0]];break}}}return v.reduceRight((z,C,M)=>{let Q,E=!1,I=null,O=null;s&&(Q=k&&C.route.id?k[C.route.id]:void 0,I=C.route.errorElement||Fm,b&&(w<0&&M===0?(Qm("route-fallback"),E=!0,O=null):w===M&&(E=!0,O=C.route.hydrateFallbackElement||null)));let V=c.concat(v.slice(0,M+1)),Y=()=>{let le;return Q?le=I:E?le=O:C.route.Component?le=S.createElement(C.route.Component,null):C.route.element?le=C.route.element:le=z,S.createElement(Bm,{match:C,routeContext:{outlet:z,matches:V,isDataRoute:s!=null},children:le})};return s&&(C.route.ErrorBoundary||C.route.errorElement||M===0)?S.createElement(Um,{location:s.location,revalidation:s.revalidation,component:I,error:Q,children:Y(),routeContext:{outlet:null,matches:V,isDataRoute:!0}}):Y()},null)}var Id=function(i){return i.UseBlocker="useBlocker",i.UseRevalidator="useRevalidator",i.UseNavigateStable="useNavigate",i}(Id||{}),Md=function(i){return i.UseBlocker="useBlocker",i.UseLoaderData="useLoaderData",i.UseActionData="useActionData",i.UseRouteError="useRouteError",i.UseNavigation="useNavigation",i.UseRouteLoaderData="useRouteLoaderData",i.UseMatches="useMatches",i.UseRevalidator="useRevalidator",i.UseNavigateStable="useNavigate",i.UseRouteId="useRouteId",i}(Md||{});function Vm(i){let c=S.useContext(es);return c||Ae(!1),c}function Wm(i){let c=S.useContext(Rm);return c||Ae(!1),c}function Hm(i){let c=S.useContext(gn);return c||Ae(!1),c}function Ld(i){let c=Hm(),s=c.matches[c.matches.length-1];return s.route.id||Ae(!1),s.route.id}function qm(){var i;let c=S.useContext(Rd),s=Wm(),d=Ld();return c!==void 0?c:(i=s.errors)==null?void 0:i[d]}function Gm(){let{router:i}=Vm(Id.UseNavigateStable),c=Ld(Md.UseNavigateStable),s=S.useRef(!1);return Td(()=>{s.current=!0}),S.useCallback(function(p,h){h===void 0&&(h={}),s.current&&(typeof p=="number"?i.navigate(p):i.navigate(p,ua({fromRouteId:c},h)))},[i,c])}const rd={};function Qm(i,c,s){rd[i]||(rd[i]=!0)}function Km(i,c){i==null||i.v7_startTransition,i==null||i.v7_relativeSplatPath}function Ln(i){Ae(!1)}function Ym(i){let{basename:c="/",children:s=null,location:d,navigationType:p=xn.Pop,navigator:h,static:v=!1,future:k}=i;fa()&&Ae(!1);let b=c.replace(/^\/*/,"/"),w=S.useMemo(()=>({basename:b,navigator:h,static:v,future:ua({v7_relativeSplatPath:!1},k)}),[b,k,h,v]);typeof d=="string"&&(d=gr(d));let{pathname:z="/",search:C="",hash:M="",state:Q=null,key:E="default"}=d,I=S.useMemo(()=>{let O=Jo(z,b);return O==null?null:{location:{pathname:O,search:C,hash:M,state:Q,key:E},navigationType:p}},[b,z,C,M,Q,E,p]);return I==null?null:S.createElement(Fn.Provider,{value:w},S.createElement(Ll.Provider,{children:s,value:I}))}function Xm(i){let{children:c,location:s}=i;return Mm(Fo(c),s)}new Promise(()=>{});function Fo(i,c){c===void 0&&(c=[]);let s=[];return S.Children.forEach(i,(d,p)=>{if(!S.isValidElement(d))return;let h=[...c,p];if(d.type===S.Fragment){s.push.apply(s,Fo(d.props.children,h));return}d.type!==Ln&&Ae(!1),!d.props.index||!d.props.children||Ae(!1);let v={id:d.props.id||h.join("-"),caseSensitive:d.props.caseSensitive,element:d.props.element,Component:d.props.Component,index:d.props.index,path:d.props.path,loader:d.props.loader,action:d.props.action,errorElement:d.props.errorElement,ErrorBoundary:d.props.ErrorBoundary,hasErrorBoundary:d.props.ErrorBoundary!=null||d.props.errorElement!=null,shouldRevalidate:d.props.shouldRevalidate,handle:d.props.handle,lazy:d.props.lazy};d.props.children&&(v.children=Fo(d.props.children,h)),s.push(v)}),s}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Uo(){return Uo=Object.assign?Object.assign.bind():function(i){for(var c=1;c<arguments.length;c++){var s=arguments[c];for(var d in s)Object.prototype.hasOwnProperty.call(s,d)&&(i[d]=s[d])}return i},Uo.apply(this,arguments)}function Zm(i,c){if(i==null)return{};var s={},d=Object.keys(i),p,h;for(h=0;h<d.length;h++)p=d[h],!(c.indexOf(p)>=0)&&(s[p]=i[p]);return s}function Jm(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function eh(i,c){return i.button===0&&(!c||c==="_self")&&!Jm(i)}const th=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],nh="6";try{window.__reactRouterVersion=nh}catch{}const rh="startTransition",ad=Zf[rh];function ah(i){let{basename:c,children:s,future:d,window:p}=i,h=S.useRef();h.current==null&&(h.current=im({window:p,v5Compat:!0}));let v=h.current,[k,b]=S.useState({action:v.action,location:v.location}),{v7_startTransition:w}=d||{},z=S.useCallback(C=>{w&&ad?ad(()=>b(C)):b(C)},[b,w]);return S.useLayoutEffect(()=>v.listen(z),[v,z]),S.useEffect(()=>Km(d),[d]),S.createElement(Ym,{basename:c,children:s,location:k.location,navigationType:k.action,navigator:v,future:d})}const lh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ih=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,pt=S.forwardRef(function(c,s){let{onClick:d,relative:p,reloadDocument:h,replace:v,state:k,target:b,to:w,preventScrollReset:z,viewTransition:C}=c,M=Zm(c,th),{basename:Q}=S.useContext(Fn),E,I=!1;if(typeof w=="string"&&ih.test(w)&&(E=w,lh))try{let le=new URL(window.location.href),K=w.startsWith("//")?new URL(le.protocol+w):new URL(w),ae=Jo(K.pathname,Q);K.origin===le.origin&&ae!=null?w=ae+K.search+K.hash:I=!0}catch{}let O=Tm(w,{relative:p}),V=oh(w,{replace:v,state:k,target:b,preventScrollReset:z,relative:p,viewTransition:C});function Y(le){d&&d(le),le.defaultPrevented||V(le)}return S.createElement("a",Uo({},M,{href:E||O,onClick:I||h?d:Y,ref:s,target:b}))});var ld;(function(i){i.UseScrollRestoration="useScrollRestoration",i.UseSubmit="useSubmit",i.UseSubmitFetcher="useSubmitFetcher",i.UseFetcher="useFetcher",i.useViewTransitionState="useViewTransitionState"})(ld||(ld={}));var id;(function(i){i.UseFetcher="useFetcher",i.UseFetchers="useFetchers",i.UseScrollRestoration="useScrollRestoration"})(id||(id={}));function oh(i,c){let{target:s,replace:d,state:p,preventScrollReset:h,relative:v,viewTransition:k}=c===void 0?{}:c,b=Al(),w=yr(),z=Od(i,{relative:v});return S.useCallback(C=>{if(eh(C,s)){C.preventDefault();let M=d!==void 0?d:Pl(w)===Pl(z);b(i,{replace:M,state:p,preventScrollReset:h,relative:v,viewTransition:k})}},[w,b,z,d,p,s,i,h,v,k])}/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var sh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ch=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=(i,c)=>{const s=S.forwardRef(({color:d="currentColor",size:p=24,strokeWidth:h=2,absoluteStrokeWidth:v,className:k="",children:b,...w},z)=>S.createElement("svg",{ref:z,...sh,width:p,height:p,stroke:d,strokeWidth:v?Number(h)*24/Number(p):h,className:["lucide",`lucide-${ch(i)}`,k].join(" "),...w},[...c.map(([C,M])=>S.createElement(C,M)),...Array.isArray(b)?b:[b]]));return s.displayName=`${i}`,s};/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=ee("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=ee("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bl=ee("ArrowDownWideNarrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=ee("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=ee("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fd=ee("Badge",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uh=ee("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qt=ee("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=ee("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=ee("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=ee("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=ee("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=ee("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=ee("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=ee("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=ee("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const od=ee("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=ee("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ph=ee("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=ee("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fh=ee("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=ee("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mh=ee("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hh=ee("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=ee("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh=ee("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vh=ee("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh=ee("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yh=ee("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=ee("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wh=ee("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jh=ee("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kh=ee("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sd=ee("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const El=ee("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=ee("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=ee("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=ee("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bh=ee("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=ee("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nh=ee("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sh=ee("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ch=ee("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=ee("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vo=ee("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=ee("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=ee("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eh=ee("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bd=ee("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=ee("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cd=ee("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);function zh({children:i,usuario:c,onLogout:s}){const[d,p]=S.useState(!0),h=yr(),v=[{icon:xh,label:"Dashboard",path:"/",color:"text-blue-400"},{icon:Dl,label:"Nuevo Caso",path:"/nuevo-caso",color:"text-green-400"},{icon:hh,label:"Casos",path:"/casos",color:"text-yellow-400"},{icon:qt,label:"Generar IA",path:"/generar-ia",color:"text-purple-400"},{icon:as,label:"Configuración",path:"/configuracion",color:"text-gray-400"}],k=b=>h.pathname===b;return a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800",children:[a.jsx("header",{className:"bg-slate-800/90 backdrop-blur-sm border-b border-slate-700 shadow-xl",children:a.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>p(!d),className:"p-2 text-gray-300 hover:text-white hover:bg-slate-700 rounded-md transition-colors",children:d?a.jsx(Bd,{size:20}):a.jsx(wh,{size:20})}),a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg",children:a.jsx(da,{className:"w-8 h-8 text-white"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-xl font-bold text-white",children:"SISTEMA FORENSE IDENTIKIT"}),a.jsx("p",{className:"text-sm text-gray-400 font-mono",children:"UNIDAD DE CRIMINALÍSTICA - PERÚ"})]})]})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("div",{className:"relative",children:[a.jsx(Tl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),a.jsx("input",{type:"text",placeholder:"Buscar casos...",className:"bg-slate-700 text-white pl-10 pr-4 py-2 rounded-md border border-slate-600 focus:border-blue-500 focus:outline-none w-64"})]}),a.jsxs("button",{className:"relative p-2 text-gray-300 hover:text-white hover:bg-slate-700 rounded-md transition-colors",children:[a.jsx(uh,{size:20}),a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:"3"})]}),a.jsxs("div",{className:"flex items-center space-x-3 border-l border-slate-600 pl-4",children:[a.jsxs("div",{className:"text-right",children:[a.jsx("p",{className:"text-white font-semibold",children:c.nombre}),a.jsx("p",{className:"text-sm text-gray-400",children:c.rango})]}),a.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold",children:c.nombre.split(" ").map(b=>b[0]).join("")})}),a.jsx("button",{onClick:s,className:"p-2 text-gray-300 hover:text-red-400 hover:bg-slate-700 rounded-md transition-colors",title:"Cerrar Sesión",children:a.jsx(yh,{size:20})})]})]})]})}),a.jsxs("div",{className:"flex",children:[a.jsxs("aside",{className:`${d?"w-64":"w-16"} transition-all duration-300 bg-slate-800/90 backdrop-blur-sm border-r border-slate-700 min-h-screen`,children:[a.jsx("div",{className:"p-4",children:a.jsx("nav",{className:"space-y-2",children:v.map(b=>{const w=b.icon,z=k(b.path);return a.jsxs(pt,{to:b.path,className:`flex items-center space-x-3 px-3 py-3 rounded-md transition-all duration-200 ${z?"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg":"text-gray-300 hover:text-white hover:bg-slate-700/50"}`,children:[a.jsx(w,{className:`w-5 h-5 ${z?"text-white":b.color}`}),d&&a.jsx("span",{className:"font-medium",children:b.label})]},b.path)})})}),d&&a.jsx("div",{className:"absolute bottom-4 left-4 right-4",children:a.jsx("div",{className:"bg-slate-700/50 rounded-lg p-4 border border-slate-600",children:a.jsxs("div",{className:"text-center",children:[a.jsx(da,{className:"w-8 h-8 text-blue-400 mx-auto mb-2"}),a.jsx("p",{className:"text-xs text-gray-400 font-mono",children:"SISTEMA SEGURO"}),a.jsx("p",{className:"text-xs text-green-400 font-mono",children:"CONECTADO"})]})})})]}),a.jsx("main",{className:"flex-1 ml-0 transition-all duration-300",children:a.jsx("div",{className:"p-6 min-h-screen",children:i})})]})]})}function _h(){const[i,c]=S.useState(null),[s,d]=S.useState([]),[p,h]=S.useState(!0);S.useEffect(()=>{(async()=>{try{const C=await(await fetch("/data/casos-mock.json")).json();c(C.estadisticas),d(C.casos.slice(0,5))}catch(z){console.error("Error cargando datos:",z)}finally{h(!1)}})()},[]);const v=w=>{switch(w){case"activo":return"text-green-400 bg-green-900/30 border-green-700";case"pendiente":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"resuelto":return"text-blue-400 bg-blue-900/30 border-blue-700";case"archivado":return"text-gray-400 bg-gray-900/30 border-gray-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},k=w=>{switch(w){case"muy_alta":return"text-red-400 bg-red-900/30 border-red-700";case"alta":return"text-orange-400 bg-orange-900/30 border-orange-700";case"media":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"baja":return"text-green-400 bg-green-900/30 border-green-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},b=w=>new Date(w).toLocaleDateString("es-PE",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return p?a.jsx("div",{className:"flex items-center justify-center h-96",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-300",children:"Cargando dashboard..."})]})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Dashboard Criminalístico"}),a.jsxs("p",{className:"text-gray-400",children:["Sistema de Gestión de Identikits Fotorrealistas - ",new Date().toLocaleDateString("es-PE",{weekday:"long",year:"numeric",month:"long",day:"numeric"})]})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs(pt,{to:"/nuevo-caso",className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-md flex items-center space-x-2 transition-all duration-200 shadow-lg",children:[a.jsx(Dl,{size:20}),a.jsx("span",{children:"Nuevo Caso"})]}),a.jsxs(pt,{to:"/generar-ia",className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-md flex items-center space-x-2 transition-all duration-200 shadow-lg",children:[a.jsx(qt,{size:20}),a.jsx("span",{children:"Generar IA"})]})]})]}),i&&a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm font-medium",children:"Total de Casos"}),a.jsx("p",{className:"text-3xl font-bold text-white",children:i.total})]}),a.jsx("div",{className:"p-3 bg-blue-600/20 rounded-lg",children:a.jsx(xr,{className:"w-8 h-8 text-blue-400"})})]}),a.jsxs("p",{className:"text-gray-500 text-sm mt-2",children:[a.jsx(Ch,{className:"inline w-4 h-4 mr-1"}),"Registro completo"]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm font-medium",children:"Casos Activos"}),a.jsx("p",{className:"text-3xl font-bold text-green-400",children:i.activos})]}),a.jsx("div",{className:"p-3 bg-green-600/20 rounded-lg",children:a.jsx(ts,{className:"w-8 h-8 text-green-400"})})]}),a.jsxs("p",{className:"text-gray-500 text-sm mt-2",children:[a.jsx(hr,{className:"inline w-4 h-4 mr-1"}),"En investigación"]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm font-medium",children:"Pendientes"}),a.jsx("p",{className:"text-3xl font-bold text-yellow-400",children:i.pendientes})]}),a.jsx("div",{className:"p-3 bg-yellow-600/20 rounded-lg",children:a.jsx(Rl,{className:"w-8 h-8 text-yellow-400"})})]}),a.jsxs("p",{className:"text-gray-500 text-sm mt-2",children:[a.jsx(_l,{className:"inline w-4 h-4 mr-1"}),"Esperando acción"]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm font-medium",children:"Resueltos"}),a.jsx("p",{className:"text-3xl font-bold text-blue-400",children:i.resueltos})]}),a.jsx("div",{className:"p-3 bg-blue-600/20 rounded-lg",children:a.jsx(Fl,{className:"w-8 h-8 text-blue-400"})})]}),a.jsxs("p",{className:"text-gray-500 text-sm mt-2",children:[a.jsx(Wo,{className:"inline w-4 h-4 mr-1"}),"Casos cerrados"]})]})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(Rl,{className:"w-6 h-6 mr-2 text-blue-400"}),"Casos Recientes"]}),a.jsx("div",{className:"space-y-4",children:s.map(w=>a.jsx("div",{className:"bg-slate-700/50 border border-slate-600 rounded-lg p-4 hover:bg-slate-700/70 transition-colors",children:a.jsxs("div",{className:"flex justify-between items-start mb-2",children:[a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[a.jsx("span",{className:"font-mono text-blue-400 font-bold",children:w.id}),a.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium border ${v(w.estado)}`,children:w.estado.toUpperCase()}),a.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium border ${k(w.prioridad)}`,children:w.prioridad.replace("_"," ").toUpperCase()})]}),a.jsx("p",{className:"text-white font-medium mb-1",children:w.descripcion}),a.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[a.jsxs("span",{className:"flex items-center",children:[a.jsx(Bl,{className:"w-4 h-4 mr-1"}),w.ubicacion]}),a.jsxs("span",{className:"flex items-center",children:[a.jsx(Wo,{className:"w-4 h-4 mr-1"}),w.oficial]}),a.jsxs("span",{className:"flex items-center",children:[a.jsx(_l,{className:"w-4 h-4 mr-1"}),b(w.fecha)]})]})]}),a.jsx(pt,{to:`/caso/${w.id}`,className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors",children:"Ver Detalle"})]})},w.id))}),a.jsx("div",{className:"mt-4 text-center",children:a.jsxs(pt,{to:"/casos",className:"text-blue-400 hover:text-blue-300 font-medium flex items-center justify-center",children:[a.jsx(Tl,{className:"w-4 h-4 mr-1"}),"Ver todos los casos"]})})]})}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Acciones Rápidas"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs(pt,{to:"/nuevo-caso",className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200",children:[a.jsx(Dl,{size:20}),a.jsx("span",{children:"Crear Nuevo Caso"})]}),a.jsxs(pt,{to:"/generar-ia",className:"w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200",children:[a.jsx(qt,{size:20}),a.jsx("span",{children:"Procesar con IA"})]}),a.jsxs(pt,{to:"/casos",className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white p-3 rounded-md flex items-center space-x-3 transition-all duration-200",children:[a.jsx(Tl,{size:20}),a.jsx("span",{children:"Buscar Casos"})]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Estado del Sistema"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-400",children:"IA Identikit"}),a.jsx("span",{className:"text-green-400 font-medium",children:"● ACTIVO"})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-400",children:"Base de Datos"}),a.jsx("span",{className:"text-green-400 font-medium",children:"● CONECTADA"})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-400",children:"Servidor"}),a.jsx("span",{className:"text-green-400 font-medium",children:"● ONLINE"})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-gray-400",children:"Backup"}),a.jsx("span",{className:"text-yellow-400 font-medium",children:"● PROGRAMADO"})]})]})]})]})]})]})}var To={exports:{}},Oo,ud;function Dh(){if(ud)return Oo;ud=1;var i="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Oo=i,Oo}var Io,dd;function Rh(){if(dd)return Io;dd=1;var i=Dh();function c(){}function s(){}return s.resetWarningCache=c,Io=function(){function d(v,k,b,w,z,C){if(C!==i){var M=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw M.name="Invariant Violation",M}}d.isRequired=d;function p(){return d}var h={array:d,bigint:d,bool:d,func:d,number:d,object:d,string:d,symbol:d,any:d,arrayOf:p,element:d,elementType:d,instanceOf:p,node:d,objectOf:p,oneOf:p,oneOfType:p,shape:p,exact:p,checkPropTypes:s,resetWarningCache:c};return h.PropTypes=h,h},Io}var pd;function Th(){return pd||(pd=1,To.exports=Rh()()),To.exports}var Oh=Th();const ke=Yo(Oh);function Un(i,c,s,d){function p(h){return h instanceof s?h:new s(function(v){v(h)})}return new(s||(s=Promise))(function(h,v){function k(z){try{w(d.next(z))}catch(C){v(C)}}function b(z){try{w(d.throw(z))}catch(C){v(C)}}function w(z){z.done?h(z.value):p(z.value).then(k,b)}w((d=d.apply(i,c||[])).next())})}const Ih=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function vr(i,c,s){const d=Mh(i),{webkitRelativePath:p}=i,h=typeof c=="string"?c:typeof p=="string"&&p.length>0?p:`./${i.name}`;return typeof d.path!="string"&&fd(d,"path",h),fd(d,"relativePath",h),d}function Mh(i){const{name:c}=i;if(c&&c.lastIndexOf(".")!==-1&&!i.type){const d=c.split(".").pop().toLowerCase(),p=Ih.get(d);p&&Object.defineProperty(i,"type",{value:p,writable:!1,configurable:!1,enumerable:!0})}return i}function fd(i,c,s){Object.defineProperty(i,c,{value:s,writable:!1,configurable:!1,enumerable:!0})}const Lh=[".DS_Store","Thumbs.db"];function Ah(i){return Un(this,void 0,void 0,function*(){return Ol(i)&&Fh(i.dataTransfer)?Vh(i.dataTransfer,i.type):Uh(i)?Bh(i):Array.isArray(i)&&i.every(c=>"getFile"in c&&typeof c.getFile=="function")?$h(i):[]})}function Fh(i){return Ol(i)}function Uh(i){return Ol(i)&&Ol(i.target)}function Ol(i){return typeof i=="object"&&i!==null}function Bh(i){return Ho(i.target.files).map(c=>vr(c))}function $h(i){return Un(this,void 0,void 0,function*(){return(yield Promise.all(i.map(s=>s.getFile()))).map(s=>vr(s))})}function Vh(i,c){return Un(this,void 0,void 0,function*(){if(i.items){const s=Ho(i.items).filter(p=>p.kind==="file");if(c!=="drop")return s;const d=yield Promise.all(s.map(Wh));return md($d(d))}return md(Ho(i.files).map(s=>vr(s)))})}function md(i){return i.filter(c=>Lh.indexOf(c.name)===-1)}function Ho(i){if(i===null)return[];const c=[];for(let s=0;s<i.length;s++){const d=i[s];c.push(d)}return c}function Wh(i){if(typeof i.webkitGetAsEntry!="function")return hd(i);const c=i.webkitGetAsEntry();return c&&c.isDirectory?Vd(c):hd(i,c)}function $d(i){return i.reduce((c,s)=>[...c,...Array.isArray(s)?$d(s):[s]],[])}function hd(i,c){return Un(this,void 0,void 0,function*(){var s;if(globalThis.isSecureContext&&typeof i.getAsFileSystemHandle=="function"){const h=yield i.getAsFileSystemHandle();if(h===null)throw new Error(`${i} is not a File`);if(h!==void 0){const v=yield h.getFile();return v.handle=h,vr(v)}}const d=i.getAsFile();if(!d)throw new Error(`${i} is not a File`);return vr(d,(s=c==null?void 0:c.fullPath)!==null&&s!==void 0?s:void 0)})}function Hh(i){return Un(this,void 0,void 0,function*(){return i.isDirectory?Vd(i):qh(i)})}function Vd(i){const c=i.createReader();return new Promise((s,d)=>{const p=[];function h(){c.readEntries(v=>Un(this,void 0,void 0,function*(){if(v.length){const k=Promise.all(v.map(Hh));p.push(k),h()}else try{const k=yield Promise.all(p);s(k)}catch(k){d(k)}}),v=>{d(v)})}h()})}function qh(i){return Un(this,void 0,void 0,function*(){return new Promise((c,s)=>{i.file(d=>{const p=vr(d,i.fullPath);c(p)},d=>{s(d)})})})}var Sl={},xd;function Gh(){return xd||(xd=1,Sl.__esModule=!0,Sl.default=function(i,c){if(i&&c){var s=Array.isArray(c)?c:c.split(",");if(s.length===0)return!0;var d=i.name||"",p=(i.type||"").toLowerCase(),h=p.replace(/\/.*$/,"");return s.some(function(v){var k=v.trim().toLowerCase();return k.charAt(0)==="."?d.toLowerCase().endsWith(k):k.endsWith("/*")?h===k.replace(/\/.*$/,""):p===k})}return!0}),Sl}var Qh=Gh();const Mo=Yo(Qh);function vd(i){return Xh(i)||Yh(i)||Hd(i)||Kh()}function Kh(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yh(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function Xh(i){if(Array.isArray(i))return qo(i)}function gd(i,c){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(i);c&&(d=d.filter(function(p){return Object.getOwnPropertyDescriptor(i,p).enumerable})),s.push.apply(s,d)}return s}function yd(i){for(var c=1;c<arguments.length;c++){var s=arguments[c]!=null?arguments[c]:{};c%2?gd(Object(s),!0).forEach(function(d){Wd(i,d,s[d])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):gd(Object(s)).forEach(function(d){Object.defineProperty(i,d,Object.getOwnPropertyDescriptor(s,d))})}return i}function Wd(i,c,s){return c in i?Object.defineProperty(i,c,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[c]=s,i}function pa(i,c){return ex(i)||Jh(i,c)||Hd(i,c)||Zh()}function Zh(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Hd(i,c){if(i){if(typeof i=="string")return qo(i,c);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return qo(i,c)}}function qo(i,c){(c==null||c>i.length)&&(c=i.length);for(var s=0,d=new Array(c);s<c;s++)d[s]=i[s];return d}function Jh(i,c){var s=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s!=null){var d=[],p=!0,h=!1,v,k;try{for(s=s.call(i);!(p=(v=s.next()).done)&&(d.push(v.value),!(c&&d.length===c));p=!0);}catch(b){h=!0,k=b}finally{try{!p&&s.return!=null&&s.return()}finally{if(h)throw k}}return d}}function ex(i){if(Array.isArray(i))return i}var tx=typeof Mo=="function"?Mo:Mo.default,nx="file-invalid-type",rx="file-too-large",ax="file-too-small",lx="too-many-files",ix=function(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",s=c.split(","),d=s.length>1?"one of ".concat(s.join(", ")):s[0];return{code:nx,message:"File type must be ".concat(d)}},wd=function(c){return{code:rx,message:"File is larger than ".concat(c," ").concat(c===1?"byte":"bytes")}},jd=function(c){return{code:ax,message:"File is smaller than ".concat(c," ").concat(c===1?"byte":"bytes")}},ox={code:lx,message:"Too many files"};function qd(i,c){var s=i.type==="application/x-moz-file"||tx(i,c);return[s,s?null:ix(c)]}function Gd(i,c,s){if(An(i.size))if(An(c)&&An(s)){if(i.size>s)return[!1,wd(s)];if(i.size<c)return[!1,jd(c)]}else{if(An(c)&&i.size<c)return[!1,jd(c)];if(An(s)&&i.size>s)return[!1,wd(s)]}return[!0,null]}function An(i){return i!=null}function sx(i){var c=i.files,s=i.accept,d=i.minSize,p=i.maxSize,h=i.multiple,v=i.maxFiles,k=i.validator;return!h&&c.length>1||h&&v>=1&&c.length>v?!1:c.every(function(b){var w=qd(b,s),z=pa(w,1),C=z[0],M=Gd(b,d,p),Q=pa(M,1),E=Q[0],I=k?k(b):null;return C&&E&&!I})}function Il(i){return typeof i.isPropagationStopped=="function"?i.isPropagationStopped():typeof i.cancelBubble<"u"?i.cancelBubble:!1}function Cl(i){return i.dataTransfer?Array.prototype.some.call(i.dataTransfer.types,function(c){return c==="Files"||c==="application/x-moz-file"}):!!i.target&&!!i.target.files}function kd(i){i.preventDefault()}function cx(i){return i.indexOf("MSIE")!==-1||i.indexOf("Trident/")!==-1}function ux(i){return i.indexOf("Edge/")!==-1}function dx(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return cx(i)||ux(i)}function Ot(){for(var i=arguments.length,c=new Array(i),s=0;s<i;s++)c[s]=arguments[s];return function(d){for(var p=arguments.length,h=new Array(p>1?p-1:0),v=1;v<p;v++)h[v-1]=arguments[v];return c.some(function(k){return!Il(d)&&k&&k.apply(void 0,[d].concat(h)),Il(d)})}}function px(){return"showOpenFilePicker"in window}function fx(i){if(An(i)){var c=Object.entries(i).filter(function(s){var d=pa(s,2),p=d[0],h=d[1],v=!0;return Qd(p)||(console.warn('Skipped "'.concat(p,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),v=!1),(!Array.isArray(h)||!h.every(Kd))&&(console.warn('Skipped "'.concat(p,'" because an invalid file extension was provided.')),v=!1),v}).reduce(function(s,d){var p=pa(d,2),h=p[0],v=p[1];return yd(yd({},s),{},Wd({},h,v))},{});return[{description:"Files",accept:c}]}return i}function mx(i){if(An(i))return Object.entries(i).reduce(function(c,s){var d=pa(s,2),p=d[0],h=d[1];return[].concat(vd(c),[p],vd(h))},[]).filter(function(c){return Qd(c)||Kd(c)}).join(",")}function hx(i){return i instanceof DOMException&&(i.name==="AbortError"||i.code===i.ABORT_ERR)}function xx(i){return i instanceof DOMException&&(i.name==="SecurityError"||i.code===i.SECURITY_ERR)}function Qd(i){return i==="audio/*"||i==="video/*"||i==="image/*"||i==="text/*"||i==="application/*"||/\w+\/[-+.\w]+/g.test(i)}function Kd(i){return/^.*\.[\w]+$/.test(i)}var vx=["children"],gx=["open"],yx=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],wx=["refKey","onChange","onClick"];function jx(i){return Nx(i)||bx(i)||Yd(i)||kx()}function kx(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bx(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function Nx(i){if(Array.isArray(i))return Go(i)}function Lo(i,c){return Ex(i)||Cx(i,c)||Yd(i,c)||Sx()}function Sx(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yd(i,c){if(i){if(typeof i=="string")return Go(i,c);var s=Object.prototype.toString.call(i).slice(8,-1);if(s==="Object"&&i.constructor&&(s=i.constructor.name),s==="Map"||s==="Set")return Array.from(i);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return Go(i,c)}}function Go(i,c){(c==null||c>i.length)&&(c=i.length);for(var s=0,d=new Array(c);s<c;s++)d[s]=i[s];return d}function Cx(i,c){var s=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(s!=null){var d=[],p=!0,h=!1,v,k;try{for(s=s.call(i);!(p=(v=s.next()).done)&&(d.push(v.value),!(c&&d.length===c));p=!0);}catch(b){h=!0,k=b}finally{try{!p&&s.return!=null&&s.return()}finally{if(h)throw k}}return d}}function Ex(i){if(Array.isArray(i))return i}function bd(i,c){var s=Object.keys(i);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(i);c&&(d=d.filter(function(p){return Object.getOwnPropertyDescriptor(i,p).enumerable})),s.push.apply(s,d)}return s}function De(i){for(var c=1;c<arguments.length;c++){var s=arguments[c]!=null?arguments[c]:{};c%2?bd(Object(s),!0).forEach(function(d){Qo(i,d,s[d])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(s)):bd(Object(s)).forEach(function(d){Object.defineProperty(i,d,Object.getOwnPropertyDescriptor(s,d))})}return i}function Qo(i,c,s){return c in i?Object.defineProperty(i,c,{value:s,enumerable:!0,configurable:!0,writable:!0}):i[c]=s,i}function Ml(i,c){if(i==null)return{};var s=Px(i,c),d,p;if(Object.getOwnPropertySymbols){var h=Object.getOwnPropertySymbols(i);for(p=0;p<h.length;p++)d=h[p],!(c.indexOf(d)>=0)&&Object.prototype.propertyIsEnumerable.call(i,d)&&(s[d]=i[d])}return s}function Px(i,c){if(i==null)return{};var s={},d=Object.keys(i),p,h;for(h=0;h<d.length;h++)p=d[h],!(c.indexOf(p)>=0)&&(s[p]=i[p]);return s}var is=S.forwardRef(function(i,c){var s=i.children,d=Ml(i,vx),p=Zd(d),h=p.open,v=Ml(p,gx);return S.useImperativeHandle(c,function(){return{open:h}},[h]),Zo.createElement(S.Fragment,null,s(De(De({},v),{},{open:h})))});is.displayName="Dropzone";var Xd={disabled:!1,getFilesFromEvent:Ah,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};is.defaultProps=Xd;is.propTypes={children:ke.func,accept:ke.objectOf(ke.arrayOf(ke.string)),multiple:ke.bool,preventDropOnDocument:ke.bool,noClick:ke.bool,noKeyboard:ke.bool,noDrag:ke.bool,noDragEventsBubbling:ke.bool,minSize:ke.number,maxSize:ke.number,maxFiles:ke.number,disabled:ke.bool,getFilesFromEvent:ke.func,onFileDialogCancel:ke.func,onFileDialogOpen:ke.func,useFsAccessApi:ke.bool,autoFocus:ke.bool,onDragEnter:ke.func,onDragLeave:ke.func,onDragOver:ke.func,onDrop:ke.func,onDropAccepted:ke.func,onDropRejected:ke.func,onError:ke.func,validator:ke.func};var Ko={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Zd(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},c=De(De({},Xd),i),s=c.accept,d=c.disabled,p=c.getFilesFromEvent,h=c.maxSize,v=c.minSize,k=c.multiple,b=c.maxFiles,w=c.onDragEnter,z=c.onDragLeave,C=c.onDragOver,M=c.onDrop,Q=c.onDropAccepted,E=c.onDropRejected,I=c.onFileDialogCancel,O=c.onFileDialogOpen,V=c.useFsAccessApi,Y=c.autoFocus,le=c.preventDropOnDocument,K=c.noClick,ae=c.noKeyboard,he=c.noDrag,xe=c.noDragEventsBubbling,Oe=c.onError,$e=c.validator,Fe=S.useMemo(function(){return mx(s)},[s]),te=S.useMemo(function(){return fx(s)},[s]),se=S.useMemo(function(){return typeof O=="function"?O:Nd},[O]),we=S.useMemo(function(){return typeof I=="function"?I:Nd},[I]),ue=S.useRef(null),B=S.useRef(null),Ce=S.useReducer(zx,Ko),ve=Lo(Ce,2),L=ve[0],H=ve[1],U=L.isFocused,g=L.isFileDialogActive,P=S.useRef(typeof window<"u"&&window.isSecureContext&&V&&px()),ie=function(){!P.current&&g&&setTimeout(function(){if(B.current){var J=B.current.files;J.length||(H({type:"closeDialog"}),we())}},300)};S.useEffect(function(){return window.addEventListener("focus",ie,!1),function(){window.removeEventListener("focus",ie,!1)}},[B,g,we,P]);var re=S.useRef([]),de=function(J){ue.current&&ue.current.contains(J.target)||(J.preventDefault(),re.current=[])};S.useEffect(function(){return le&&(document.addEventListener("dragover",kd,!1),document.addEventListener("drop",de,!1)),function(){le&&(document.removeEventListener("dragover",kd),document.removeEventListener("drop",de))}},[ue,le]),S.useEffect(function(){return!d&&Y&&ue.current&&ue.current.focus(),function(){}},[ue,Y,d]);var oe=S.useCallback(function(A){Oe?Oe(A):console.error(A)},[Oe]),ge=S.useCallback(function(A){A.preventDefault(),A.persist(),Pt(A),re.current=[].concat(jx(re.current),[A.target]),Cl(A)&&Promise.resolve(p(A)).then(function(J){if(!(Il(A)&&!xe)){var Pe=J.length,Re=Pe>0&&sx({files:J,accept:Fe,minSize:v,maxSize:h,multiple:k,maxFiles:b,validator:$e}),We=Pe>0&&!Re;H({isDragAccept:Re,isDragReject:We,isDragActive:!0,type:"setDraggedFiles"}),w&&w(A)}}).catch(function(J){return oe(J)})},[p,w,oe,xe,Fe,v,h,k,b,$e]),fe=S.useCallback(function(A){A.preventDefault(),A.persist(),Pt(A);var J=Cl(A);if(J&&A.dataTransfer)try{A.dataTransfer.dropEffect="copy"}catch{}return J&&C&&C(A),!1},[C,xe]),je=S.useCallback(function(A){A.preventDefault(),A.persist(),Pt(A);var J=re.current.filter(function(Re){return ue.current&&ue.current.contains(Re)}),Pe=J.indexOf(A.target);Pe!==-1&&J.splice(Pe,1),re.current=J,!(J.length>0)&&(H({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Cl(A)&&z&&z(A))},[ue,z,xe]),Ve=S.useCallback(function(A,J){var Pe=[],Re=[];A.forEach(function(We){var Kt=qd(We,Fe),ft=Lo(Kt,2),Lt=ft[0],br=ft[1],Vn=Gd(We,v,h),wn=Lo(Vn,2),Nr=wn[0],jn=wn[1],kn=$e?$e(We):null;if(Lt&&Nr&&!kn)Pe.push(We);else{var bn=[br,jn];kn&&(bn=bn.concat(kn)),Re.push({file:We,errors:bn.filter(function(Sr){return Sr})})}}),(!k&&Pe.length>1||k&&b>=1&&Pe.length>b)&&(Pe.forEach(function(We){Re.push({file:We,errors:[ox]})}),Pe.splice(0)),H({acceptedFiles:Pe,fileRejections:Re,isDragReject:Re.length>0,type:"setFiles"}),M&&M(Pe,Re,J),Re.length>0&&E&&E(Re,J),Pe.length>0&&Q&&Q(Pe,J)},[H,k,Fe,v,h,b,M,Q,E,$e]),It=S.useCallback(function(A){A.preventDefault(),A.persist(),Pt(A),re.current=[],Cl(A)&&Promise.resolve(p(A)).then(function(J){Il(A)&&!xe||Ve(J,A)}).catch(function(J){return oe(J)}),H({type:"reset"})},[p,Ve,oe,xe]),Mt=S.useCallback(function(){if(P.current){H({type:"openDialog"}),se();var A={multiple:k,types:te};window.showOpenFilePicker(A).then(function(J){return p(J)}).then(function(J){Ve(J,null),H({type:"closeDialog"})}).catch(function(J){hx(J)?(we(J),H({type:"closeDialog"})):xx(J)?(P.current=!1,B.current?(B.current.value=null,B.current.click()):oe(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):oe(J)});return}B.current&&(H({type:"openDialog"}),se(),B.current.value=null,B.current.click())},[H,se,we,V,Ve,oe,te,k]),yn=S.useCallback(function(A){!ue.current||!ue.current.isEqualNode(A.target)||(A.key===" "||A.key==="Enter"||A.keyCode===32||A.keyCode===13)&&(A.preventDefault(),Mt())},[ue,Mt]),Bn=S.useCallback(function(){H({type:"focus"})},[]),wr=S.useCallback(function(){H({type:"blur"})},[]),jr=S.useCallback(function(){K||(dx()?setTimeout(Mt,0):Mt())},[K,Mt]),Et=function(J){return d?null:J},$n=function(J){return ae?null:Et(J)},Gt=function(J){return he?null:Et(J)},Pt=function(J){xe&&J.stopPropagation()},Qt=S.useMemo(function(){return function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=A.refKey,Pe=J===void 0?"ref":J,Re=A.role,We=A.onKeyDown,Kt=A.onFocus,ft=A.onBlur,Lt=A.onClick,br=A.onDragEnter,Vn=A.onDragOver,wn=A.onDragLeave,Nr=A.onDrop,jn=Ml(A,yx);return De(De(Qo({onKeyDown:$n(Ot(We,yn)),onFocus:$n(Ot(Kt,Bn)),onBlur:$n(Ot(ft,wr)),onClick:Et(Ot(Lt,jr)),onDragEnter:Gt(Ot(br,ge)),onDragOver:Gt(Ot(Vn,fe)),onDragLeave:Gt(Ot(wn,je)),onDrop:Gt(Ot(Nr,It)),role:typeof Re=="string"&&Re!==""?Re:"presentation"},Pe,ue),!d&&!ae?{tabIndex:0}:{}),jn)}},[ue,yn,Bn,wr,jr,ge,fe,je,It,ae,he,d]),kr=S.useCallback(function(A){A.stopPropagation()},[]),ma=S.useMemo(function(){return function(){var A=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=A.refKey,Pe=J===void 0?"ref":J,Re=A.onChange,We=A.onClick,Kt=Ml(A,wx),ft=Qo({accept:Fe,multiple:k,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Et(Ot(Re,It)),onClick:Et(Ot(We,kr)),tabIndex:-1},Pe,B);return De(De({},ft),Kt)}},[B,s,k,It,d]);return De(De({},L),{},{isFocused:U&&!d,getRootProps:Qt,getInputProps:ma,rootRef:ue,inputRef:B,open:Et(Mt)})}function zx(i,c){switch(c.type){case"focus":return De(De({},i),{},{isFocused:!0});case"blur":return De(De({},i),{},{isFocused:!1});case"openDialog":return De(De({},Ko),{},{isFileDialogActive:!0});case"closeDialog":return De(De({},i),{},{isFileDialogActive:!1});case"setDraggedFiles":return De(De({},i),{},{isDragActive:c.isDragActive,isDragAccept:c.isDragAccept,isDragReject:c.isDragReject});case"setFiles":return De(De({},i),{},{acceptedFiles:c.acceptedFiles,fileRejections:c.fileRejections,isDragReject:c.isDragReject});case"reset":return De({},Ko);default:return i}}function Nd(){}function _x(){const i=Al(),[c,s]=S.useState({descripcion:"",testigo:"",oficial:"",ubicacion:"",tipoDelito:"",prioridad:"media",observaciones:""}),[d,p]=S.useState(null),[h,v]=S.useState(null),[k,b]=S.useState(!1),[w,z]=S.useState({}),C=S.useCallback(K=>{const ae=K[0];if(ae){p(ae);const he=URL.createObjectURL(ae);v(he)}},[]),{getRootProps:M,getInputProps:Q,isDragActive:E}=Zd({onDrop:C,accept:{"image/*":[".jpg",".jpeg",".png",".bmp",".gif"]},maxFiles:1,maxSize:10*1024*1024}),I=K=>{const{name:ae,value:he}=K.target;s(xe=>({...xe,[ae]:he})),w[ae]&&z(xe=>({...xe,[ae]:""}))},O=()=>{const K={};return c.descripcion.trim()||(K.descripcion="La descripción del caso es obligatoria"),c.testigo.trim()||(K.testigo="El nombre del testigo es obligatorio"),c.oficial.trim()||(K.oficial="El oficial a cargo es obligatorio"),c.ubicacion.trim()||(K.ubicacion="La ubicación del incidente es obligatoria"),c.tipoDelito.trim()||(K.tipoDelito="El tipo de delito es obligatorio"),d||(K.boceto="Debe cargar un boceto o imagen del sospechoso"),z(K),Object.keys(K).length===0},V=async K=>{if(K.preventDefault(),!!O()){b(!0);try{await new Promise(he=>setTimeout(he,2e3));const ae=`CASO-${new Date().getFullYear()}-${String(Math.floor(Math.random()*999)+1).padStart(3,"0")}`;console.log("Nuevo caso creado:",{id:ae,...c,boceto:d==null?void 0:d.name,fecha:new Date().toISOString()}),alert(`Caso ${ae} creado exitosamente`),i("/casos")}catch(ae){console.error("Error creando caso:",ae),alert("Error al crear el caso. Por favor intente nuevamente.")}finally{b(!1)}}},Y=()=>{p(null),h&&(URL.revokeObjectURL(h),v(null)),w.boceto&&z(K=>({...K,boceto:""}))},le=[{value:"robo_armado",label:"Robo a mano armada"},{value:"asalto",label:"Asalto y robo"},{value:"hurto",label:"Hurto"},{value:"secuestro",label:"Secuestro"},{value:"intento_secuestro",label:"Intento de secuestro"},{value:"estafa",label:"Estafa"},{value:"agresion",label:"Agresión"},{value:"homicidio",label:"Homicidio"},{value:"tentativa_homicidio",label:"Tentativa de homicidio"},{value:"otro",label:"Otro"}];return a.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>i(-1),className:"p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-md transition-colors",children:a.jsx(zl,{size:20})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-white",children:"Nuevo Caso Criminalístico"}),a.jsx("p",{className:"text-gray-400",children:"Registro de caso con boceto para generación de identikit fotorrealista"})]})]})}),a.jsxs("form",{onSubmit:V,className:"space-y-6",children:[a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(xr,{className:"w-6 h-6 mr-2 text-blue-400"}),"Información del Caso"]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Descripción del Caso *"}),a.jsx("textarea",{name:"descripcion",value:c.descripcion,onChange:I,rows:3,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Describa brevemente el incidente..."}),w.descripcion&&a.jsxs("p",{className:"text-red-400 text-sm mt-1 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.descripcion]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Tipo de Delito *"}),a.jsxs("select",{name:"tipoDelito",value:c.tipoDelito,onChange:I,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",children:[a.jsx("option",{value:"",children:"Seleccionar tipo..."}),le.map(K=>a.jsx("option",{value:K.value,children:K.label},K.value))]}),w.tipoDelito&&a.jsxs("p",{className:"text-red-400 text-sm mt-1 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.tipoDelito]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Prioridad"}),a.jsxs("select",{name:"prioridad",value:c.prioridad,onChange:I,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",children:[a.jsx("option",{value:"baja",children:"Baja"}),a.jsx("option",{value:"media",children:"Media"}),a.jsx("option",{value:"alta",children:"Alta"}),a.jsx("option",{value:"muy_alta",children:"Muy Alta"})]})]})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:[a.jsx(Bl,{className:"inline w-4 h-4 mr-1"}),"Ubicación del Incidente *"]}),a.jsx("input",{type:"text",name:"ubicacion",value:c.ubicacion,onChange:I,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Ej: Av. Javier Prado 123, San Isidro"}),w.ubicacion&&a.jsxs("p",{className:"text-red-400 text-sm mt-1 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.ubicacion]})]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(ls,{className:"w-6 h-6 mr-2 text-green-400"}),"Información de Personas"]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Testigo / Víctima *"}),a.jsx("input",{type:"text",name:"testigo",value:c.testigo,onChange:I,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Nombre completo del testigo"}),w.testigo&&a.jsxs("p",{className:"text-red-400 text-sm mt-1 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.testigo]})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:[a.jsx(Fd,{className:"inline w-4 h-4 mr-1"}),"Oficial a Cargo *"]}),a.jsx("input",{type:"text",name:"oficial",value:c.oficial,onChange:I,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Nombre y rango del oficial"}),w.oficial&&a.jsxs("p",{className:"text-red-400 text-sm mt-1 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.oficial]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Observaciones Adicionales"}),a.jsx("textarea",{name:"observaciones",value:c.observaciones,onChange:I,rows:4,className:"w-full px-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Detalles adicionales sobre el sospechoso, circunstancias, etc..."})]})]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(Bo,{className:"w-6 h-6 mr-2 text-purple-400"}),"Boceto del Sospechoso *"]}),d?a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"relative bg-slate-700/50 rounded-lg p-4 border border-slate-600",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx(fh,{className:"w-8 h-8 text-blue-400"}),a.jsxs("div",{children:[a.jsx("p",{className:"text-white font-medium",children:d.name}),a.jsxs("p",{className:"text-gray-400 text-sm",children:[(d.size/1024/1024).toFixed(2)," MB"]})]})]}),a.jsx("button",{type:"button",onClick:Y,className:"p-2 text-gray-400 hover:text-red-400 hover:bg-slate-600 rounded-md transition-colors",children:a.jsx(Bd,{size:20})})]})}),h&&a.jsx("div",{className:"relative",children:a.jsx("img",{src:h,alt:"Preview del boceto",className:"w-full max-w-md mx-auto rounded-lg border border-slate-600 shadow-lg"})})]}):a.jsxs("div",{...M(),className:`border-2 border-dashed border-slate-600 rounded-lg p-8 text-center cursor-pointer transition-colors ${E?"border-blue-500 bg-blue-500/10":"hover:border-slate-500 hover:bg-slate-700/50"}`,children:[a.jsx("input",{...Q()}),a.jsx(Vo,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-white font-medium mb-2",children:E?"Suelte la imagen aquí...":"Arrastre una imagen aquí o haga clic para seleccionar"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"Formatos soportados: JPG, PNG, BMP, GIF (máx. 10MB)"})]}),w.boceto&&a.jsxs("p",{className:"text-red-400 text-sm mt-2 flex items-center",children:[a.jsx(mr,{size:16,className:"mr-1"}),w.boceto]})]}),a.jsxs("div",{className:"flex justify-end space-x-4",children:[a.jsx("button",{type:"button",onClick:()=>i(-1),className:"px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-colors",children:"Cancelar"}),a.jsx("button",{type:"submit",disabled:k,className:"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-md flex items-center space-x-2 transition-all duration-200",children:k?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),a.jsx("span",{children:"Guardando..."})]}):a.jsxs(a.Fragment,{children:[a.jsx(rs,{size:20}),a.jsx("span",{children:"Crear Caso"})]})})]})]})]})}function Dx(){const[i,c]=S.useState([]),[s,d]=S.useState([]),[p,h]=S.useState(!0),[v,k]=S.useState(""),[b,w]=S.useState(""),[z,C]=S.useState(""),[M,Q]=S.useState(""),[E,I]=S.useState("fecha"),[O,V]=S.useState("desc"),[Y,le]=S.useState(!1);S.useEffect(()=>{(async()=>{try{const we=await(await fetch("/data/casos-mock.json")).json();c(we.casos),d(we.casos)}catch(se){console.error("Error cargando casos:",se)}finally{h(!1)}})()},[]),S.useEffect(()=>{let te=i.filter(se=>{const we=se.id.toLowerCase().includes(v.toLowerCase())||se.descripcion.toLowerCase().includes(v.toLowerCase())||se.testigo.toLowerCase().includes(v.toLowerCase())||se.oficial.toLowerCase().includes(v.toLowerCase())||se.ubicacion.toLowerCase().includes(v.toLowerCase()),ue=!b||se.estado===b,B=!z||se.prioridad===z,Ce=!M||se.oficial.toLowerCase().includes(M.toLowerCase());return we&&ue&&B&&Ce});te.sort((se,we)=>{let ue=se[E],B=we[E];return E==="fecha"&&(ue=new Date(ue),B=new Date(B)),ue<B?O==="asc"?-1:1:ue>B?O==="asc"?1:-1:0}),d(te)},[i,v,b,z,M,E,O]);const K=te=>{E===te?V(O==="asc"?"desc":"asc"):(I(te),V("asc"))},ae=te=>{switch(te){case"activo":return a.jsx(hr,{className:"w-4 h-4"});case"pendiente":return a.jsx(Rl,{className:"w-4 h-4"});case"resuelto":return a.jsx(Fl,{className:"w-4 h-4"});case"archivado":return a.jsx(Ad,{className:"w-4 h-4"});default:return a.jsx(xr,{className:"w-4 h-4"})}},he=te=>{switch(te){case"activo":return"text-green-400 bg-green-900/30 border-green-700";case"pendiente":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"resuelto":return"text-blue-400 bg-blue-900/30 border-blue-700";case"archivado":return"text-gray-400 bg-gray-900/30 border-gray-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},xe=te=>{switch(te){case"muy_alta":return"text-red-400 bg-red-900/30 border-red-700";case"alta":return"text-orange-400 bg-orange-900/30 border-orange-700";case"media":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"baja":return"text-green-400 bg-green-900/30 border-green-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},Oe=te=>new Date(te).toLocaleDateString("es-PE",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),$e=()=>{k(""),w(""),C(""),Q("")},Fe=()=>{alert("Funcionalidad de exportar PDF en desarrollo")};return p?a.jsx("div",{className:"flex items-center justify-center h-96",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-300",children:"Cargando casos..."})]})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Gestión de Casos"}),a.jsxs("p",{className:"text-gray-400",children:[s.length," de ",i.length," casos mostrados"]})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{onClick:Fe,className:"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Ul,{size:20}),a.jsx("span",{children:"Exportar PDF"})]}),a.jsxs(pt,{to:"/nuevo-caso",className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-all duration-200",children:[a.jsx(Dl,{size:20}),a.jsx("span",{children:"Nuevo Caso"})]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4",children:[a.jsx("div",{className:"flex-1",children:a.jsxs("div",{className:"relative",children:[a.jsx(Tl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",value:v,onChange:te=>k(te.target.value),placeholder:"Buscar por ID, descripción, testigo, oficial o ubicación...",className:"w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20"})]})}),a.jsxs("button",{onClick:()=>le(!Y),className:`px-4 py-3 rounded-md flex items-center space-x-2 transition-colors ${Y?"bg-blue-600 text-white":"bg-slate-700 hover:bg-slate-600 text-gray-300"}`,children:[a.jsx(mh,{size:20}),a.jsx("span",{children:"Filtros"})]})]}),Y&&a.jsxs("div",{className:"mt-4 pt-4 border-t border-slate-600",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Estado"}),a.jsxs("select",{value:b,onChange:te=>w(te.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"",children:"Todos los estados"}),a.jsx("option",{value:"activo",children:"Activo"}),a.jsx("option",{value:"pendiente",children:"Pendiente"}),a.jsx("option",{value:"resuelto",children:"Resuelto"}),a.jsx("option",{value:"archivado",children:"Archivado"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Prioridad"}),a.jsxs("select",{value:z,onChange:te=>C(te.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"",children:"Todas las prioridades"}),a.jsx("option",{value:"muy_alta",children:"Muy Alta"}),a.jsx("option",{value:"alta",children:"Alta"}),a.jsx("option",{value:"media",children:"Media"}),a.jsx("option",{value:"baja",children:"Baja"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Oficial"}),a.jsx("input",{type:"text",value:M,onChange:te=>Q(te.target.value),placeholder:"Filtrar por oficial...",className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"})]})]}),a.jsx("div",{className:"mt-4 flex justify-end",children:a.jsx("button",{onClick:$e,className:"px-4 py-2 text-gray-400 hover:text-white transition-colors",children:"Limpiar filtros"})})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg shadow-xl overflow-hidden",children:[a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"w-full text-left",children:[a.jsx("thead",{className:"bg-slate-700/50 border-b border-slate-600",children:a.jsxs("tr",{children:[a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:a.jsxs("button",{onClick:()=>K("id"),className:"flex items-center space-x-1 hover:text-white transition-colors",children:[a.jsx("span",{children:"ID Caso"}),E==="id"&&(O==="asc"?a.jsx(Nl,{size:16}):a.jsx(bl,{size:16}))]})}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:a.jsxs("button",{onClick:()=>K("fecha"),className:"flex items-center space-x-1 hover:text-white transition-colors",children:[a.jsx("span",{children:"Fecha"}),E==="fecha"&&(O==="asc"?a.jsx(Nl,{size:16}):a.jsx(bl,{size:16}))]})}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:"Descripción"}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:a.jsxs("button",{onClick:()=>K("estado"),className:"flex items-center space-x-1 hover:text-white transition-colors",children:[a.jsx("span",{children:"Estado"}),E==="estado"&&(O==="asc"?a.jsx(Nl,{size:16}):a.jsx(bl,{size:16}))]})}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:a.jsxs("button",{onClick:()=>K("prioridad"),className:"flex items-center space-x-1 hover:text-white transition-colors",children:[a.jsx("span",{children:"Prioridad"}),E==="prioridad"&&(O==="asc"?a.jsx(Nl,{size:16}):a.jsx(bl,{size:16}))]})}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:"Oficial"}),a.jsx("th",{className:"px-6 py-4 text-gray-300 font-semibold",children:"Acciones"})]})}),a.jsx("tbody",{children:s.map((te,se)=>a.jsxs("tr",{className:`border-b border-slate-700 hover:bg-slate-700/30 transition-colors ${se%2===0?"bg-slate-800/30":"bg-slate-800/50"}`,children:[a.jsx("td",{className:"px-6 py-4",children:a.jsx("span",{className:"font-mono text-blue-400 font-bold",children:te.id})}),a.jsx("td",{className:"px-6 py-4",children:a.jsxs("div",{className:"flex items-center text-gray-300",children:[a.jsx(_l,{className:"w-4 h-4 mr-2"}),a.jsx("span",{className:"text-sm",children:Oe(te.fecha)})]})}),a.jsx("td",{className:"px-6 py-4",children:a.jsxs("div",{children:[a.jsx("p",{className:"text-white font-medium truncate max-w-xs",children:te.descripcion}),a.jsxs("div",{className:"flex items-center mt-1 text-sm text-gray-400",children:[a.jsx(Bl,{className:"w-3 h-3 mr-1"}),a.jsx("span",{className:"truncate max-w-xs",children:te.ubicacion})]})]})}),a.jsx("td",{className:"px-6 py-4",children:a.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded text-xs font-medium border ${he(te.estado)}`,children:[ae(te.estado),a.jsx("span",{className:"ml-1",children:te.estado.toUpperCase()})]})}),a.jsx("td",{className:"px-6 py-4",children:a.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded text-xs font-medium border ${xe(te.prioridad)}`,children:te.prioridad.replace("_"," ").toUpperCase()})}),a.jsx("td",{className:"px-6 py-4",children:a.jsxs("div",{className:"flex items-center text-gray-300",children:[a.jsx(ls,{className:"w-4 h-4 mr-2"}),a.jsx("span",{className:"text-sm truncate max-w-xs",children:te.oficial})]})}),a.jsx("td",{className:"px-6 py-4",children:a.jsxs(pt,{to:`/caso/${te.id}`,className:"inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors",children:[a.jsx(ns,{size:16,className:"mr-1"}),"Ver"]})})]},te.id))})]})}),s.length===0&&a.jsxs("div",{className:"text-center py-12",children:[a.jsx(xr,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-400 text-lg",children:"No se encontraron casos"}),a.jsx("p",{className:"text-gray-500 text-sm",children:"Intente ajustar los filtros de búsqueda"})]})]})]})}function Rx(){const{id:i}=Im(),c=Al(),[s,d]=S.useState(null),[p,h]=S.useState(!0),[v,k]=S.useState(!1),[b,w]=S.useState(null);S.useEffect(()=>{(async()=>{try{const ae=(await(await fetch("/data/casos-mock.json")).json()).casos.find(he=>he.id===i);d(ae||null)}catch(le){console.error("Error cargando caso:",le)}finally{h(!1)}})()},[i]);const z=Y=>{switch(Y){case"activo":return a.jsx(hr,{className:"w-5 h-5"});case"pendiente":return a.jsx(Rl,{className:"w-5 h-5"});case"resuelto":return a.jsx(Fl,{className:"w-5 h-5"});case"archivado":return a.jsx(Ad,{className:"w-5 h-5"});default:return a.jsx(xr,{className:"w-5 h-5"})}},C=Y=>{switch(Y){case"activo":return"text-green-400 bg-green-900/30 border-green-700";case"pendiente":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"resuelto":return"text-blue-400 bg-blue-900/30 border-blue-700";case"archivado":return"text-gray-400 bg-gray-900/30 border-gray-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},M=Y=>{switch(Y){case"muy_alta":return"text-red-400 bg-red-900/30 border-red-700";case"alta":return"text-orange-400 bg-orange-900/30 border-orange-700";case"media":return"text-yellow-400 bg-yellow-900/30 border-yellow-700";case"baja":return"text-green-400 bg-green-900/30 border-green-700";default:return"text-gray-400 bg-gray-900/30 border-gray-700"}},Q=Y=>new Date(Y).toLocaleDateString("es-PE",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),E=Y=>{w(Y),k(!0)},I=Y=>{navigator.clipboard.writeText(Y),alert("ID copiado al portapapeles")},O=()=>{alert("Funcionalidad de generar reporte en desarrollo")},V=()=>{confirm("¿Está seguro de que desea eliminar este caso? Esta acción no se puede deshacer.")&&(alert("Caso eliminado"),c("/casos"))};return p?a.jsx("div",{className:"flex items-center justify-center h-96",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-300",children:"Cargando caso..."})]})}):s?a.jsxs("div",{className:"max-w-6xl mx-auto space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>c("/casos"),className:"p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-md transition-colors",children:a.jsx(zl,{size:20})}),a.jsxs("div",{children:[a.jsxs("h1",{className:"text-3xl font-bold text-white flex items-center space-x-3",children:[a.jsxs("span",{children:["Caso ",s.id]}),a.jsx("button",{onClick:()=>I(s.id),className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Copiar ID",children:a.jsx(dh,{size:20})})]}),a.jsx("p",{className:"text-gray-400",children:"Detalles completos del caso criminalístico"})]})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{onClick:O,className:"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Ul,{size:20}),a.jsx("span",{children:"Reporte PDF"})]}),a.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Nh,{size:20}),a.jsx("span",{children:"Editar"})]}),a.jsxs("button",{onClick:V,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Sh,{size:20}),a.jsx("span",{children:"Eliminar"})]})]})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-4",children:[a.jsxs("span",{className:`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium border ${C(s.estado)}`,children:[z(s.estado),a.jsx("span",{className:"ml-2",children:s.estado.toUpperCase()})]}),a.jsxs("span",{className:`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium border ${M(s.prioridad)}`,children:[a.jsx(hr,{className:"w-4 h-4 mr-2"}),s.prioridad.replace("_"," ").toUpperCase()]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsx("div",{className:"space-y-4",children:a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-2 flex items-center",children:[a.jsx(xr,{className:"w-5 h-5 mr-2 text-blue-400"}),"Información del Caso"]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center text-gray-300",children:[a.jsx(_l,{className:"w-4 h-4 mr-2"}),a.jsx("span",{className:"text-sm",children:Q(s.fecha)})]}),a.jsxs("div",{className:"flex items-start text-gray-300",children:[a.jsx(Bl,{className:"w-4 h-4 mr-2 mt-1"}),a.jsx("span",{className:"text-sm",children:s.ubicacion})]}),a.jsx("div",{className:"text-gray-300",children:a.jsxs("span",{className:"text-sm",children:[a.jsx("strong",{children:"Tipo:"})," ",s.tipoDelito.replace("_"," ")]})})]})]})}),a.jsx("div",{className:"space-y-4",children:a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-2 flex items-center",children:[a.jsx(ls,{className:"w-5 h-5 mr-2 text-green-400"}),"Personas Involucradas"]}),a.jsxs("div",{className:"space-y-2",children:[a.jsx("div",{className:"text-gray-300",children:a.jsxs("span",{className:"text-sm",children:[a.jsx("strong",{children:"Testigo/Víctima:"})," ",s.testigo]})}),a.jsxs("div",{className:"flex items-center text-gray-300",children:[a.jsx(Fd,{className:"w-4 h-4 mr-2"}),a.jsxs("span",{className:"text-sm",children:[a.jsx("strong",{children:"Oficial:"})," ",s.oficial]})]})]})]})})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Descripción del Incidente"}),a.jsx("p",{className:"text-gray-300 leading-relaxed",children:s.descripcion})]}),s.observaciones&&a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Observaciones Adicionales"}),a.jsx("p",{className:"text-gray-300 leading-relaxed",children:s.observaciones})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Acciones del Sistema"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs(pt,{to:"/generar-ia",state:{casoId:s.id},className:"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white p-4 rounded-md flex items-center space-x-3 transition-all duration-200",children:[a.jsx(qt,{size:24}),a.jsxs("div",{children:[a.jsx("p",{className:"font-medium",children:"Procesar con IA"}),a.jsx("p",{className:"text-sm opacity-90",children:"Generar imagen fotorrealista"})]})]}),a.jsxs("button",{onClick:()=>navigator.share&&navigator.share({title:`Caso ${s.id}`,text:s.descripcion,url:window.location.href}),className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white p-4 rounded-md flex items-center space-x-3 transition-all duration-200",children:[a.jsx(bh,{size:24}),a.jsxs("div",{children:[a.jsx("p",{className:"font-medium",children:"Compartir Caso"}),a.jsx("p",{className:"text-sm opacity-90",children:"Enviar información"})]})]})]})]})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[a.jsx(Bo,{className:"w-5 h-5 mr-2 text-purple-400"}),"Boceto Original"]}),s.boceto?a.jsxs("div",{className:"relative group",children:[a.jsx("img",{src:s.boceto,alt:"Boceto del sospechoso",className:"w-full rounded-lg border border-slate-600 cursor-pointer transition-transform group-hover:scale-105",onClick:()=>E(s.boceto)}),a.jsx("button",{onClick:()=>E(s.boceto),className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 rounded-lg",children:a.jsx(cd,{className:"w-8 h-8 text-white"})})]}):a.jsx("div",{className:"w-full h-48 bg-slate-700/50 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(Bo,{className:"w-8 h-8 text-gray-500 mx-auto mb-2"}),a.jsx("p",{className:"text-gray-500 text-sm",children:"Sin boceto disponible"})]})})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[a.jsx(qt,{className:"w-5 h-5 mr-2 text-green-400"}),"Imagen Generada por IA"]}),s.fotoGenerada?a.jsxs("div",{className:"relative group",children:[a.jsx("img",{src:s.fotoGenerada,alt:"Imagen generada por IA",className:"w-full rounded-lg border border-slate-600 cursor-pointer transition-transform group-hover:scale-105",onClick:()=>E(s.fotoGenerada)}),a.jsx("button",{onClick:()=>E(s.fotoGenerada),className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 rounded-lg",children:a.jsx(cd,{className:"w-8 h-8 text-white"})})]}):a.jsx("div",{className:"w-full h-48 bg-slate-700/50 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(qt,{className:"w-8 h-8 text-gray-500 mx-auto mb-2"}),a.jsx("p",{className:"text-gray-500 text-sm",children:"Pendiente de procesamiento"}),a.jsx(pt,{to:"/generar-ia",state:{casoId:s.id},className:"text-blue-400 hover:text-blue-300 text-sm underline mt-2 block",children:"Generar ahora"})]})})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Timeline del Caso"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),a.jsxs("div",{className:"text-sm",children:[a.jsx("p",{className:"text-white font-medium",children:"Caso creado"}),a.jsx("p",{className:"text-gray-400",children:Q(s.fecha)})]})]}),s.fotoGenerada&&a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),a.jsxs("div",{className:"text-sm",children:[a.jsx("p",{className:"text-white font-medium",children:"IA procesada"}),a.jsx("p",{className:"text-gray-400",children:"Imagen fotorrealista generada"})]})]}),s.estado==="resuelto"&&a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),a.jsxs("div",{className:"text-sm",children:[a.jsx("p",{className:"text-white font-medium",children:"Caso resuelto"}),a.jsx("p",{className:"text-gray-400",children:"Sospechoso identificado"})]})]})]})]})]})]}),v&&b&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4",children:a.jsxs("div",{className:"relative max-w-4xl max-h-full",children:[a.jsx("button",{onClick:()=>k(!1),className:"absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors",children:a.jsx("span",{className:"text-2xl",children:"✕"})}),a.jsx("img",{src:b,alt:"Imagen ampliada",className:"max-w-full max-h-full object-contain rounded-lg"})]})})]}):a.jsxs("div",{className:"text-center py-12",children:[a.jsx(hr,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),a.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"Caso No Encontrado"}),a.jsxs("p",{className:"text-gray-400 mb-6",children:["El caso con ID ",i," no existe en el sistema."]}),a.jsxs(pt,{to:"/casos",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md inline-flex items-center space-x-2 transition-colors",children:[a.jsx(zl,{size:20}),a.jsx("span",{children:"Volver a Casos"})]})]})}function Tx(){var ue;const i=yr(),c=Al(),s=(ue=i.state)==null?void 0:ue.casoId,[d,p]=S.useState({modelo:"controlnet",calidad:"alta",tamaño:"768x768",steps:20,guidance:7.5,seed:-1,procesarRostro:!0,mejorarCalidad:!0}),[h,v]=S.useState(!1),[k,b]=S.useState(!1),[w,z]=S.useState(!1),[C,M]=S.useState(null),[Q,E]=S.useState({etapa:"",progreso:0,mensaje:"",completado:!1}),[I,O]=S.useState(null),[V,Y]=S.useState(null),[le,K]=S.useState([]),ae=[{nombre:"Inicializando",tiempo:2e3,mensaje:"Cargando modelo ControlNet..."},{nombre:"Preprocesado",tiempo:3e3,mensaje:"Analizando boceto de entrada..."},{nombre:"Generación IA",tiempo:8e3,mensaje:"Generando imagen fotorrealista..."},{nombre:"Refinamiento",tiempo:4e3,mensaje:"Mejorando detalles faciales..."},{nombre:"Post-proceso",tiempo:2e3,mensaje:"Aplicando filtros finales..."},{nombre:"Finalizado",tiempo:1e3,mensaje:"Imagen generada exitosamente"}];S.useEffect(()=>{s&&O("/images/case-files.jpg")},[s]);const he=async()=>{v(!0),z(!1),M(null),b(!1);try{for(let B=0;B<ae.length&&!k;B++){const Ce=ae[B];E({etapa:Ce.nombre,progreso:(B+1)/ae.length*100,mensaje:Ce.mensaje,completado:!1}),await new Promise(ve=>setTimeout(ve,Ce.tiempo))}k||(E({etapa:"Completado",progreso:100,mensaje:"Imagen fotorrealista generada exitosamente",completado:!0}),Y("/images/facial-composite.jpg"),K(B=>[...B,"/images/facial-composite.jpg"]),z(!0))}catch(B){M("Error durante el procesamiento de IA"),console.error(B)}finally{v(!1)}},xe=()=>{b(!k)},Oe=()=>{v(!1),b(!1),z(!1),M(null),E({etapa:"",progreso:0,mensaje:"",completado:!1}),Y(null)},$e=()=>{p(B=>({...B,seed:Math.floor(Math.random()*1e6)}))},Fe=()=>{V&&alert("Descargando imagen generada...")},te=()=>{V&&s&&(alert(`Resultado guardado en caso ${s}`),c(`/caso/${s}`))},se=(B,Ce)=>{p(ve=>({...ve,[B]:Ce}))},we=B=>{switch(B){case"controlnet":return{nombre:"ControlNet + Stable Diffusion",descripcion:"Recomendado - Balance calidad/velocidad",vram:"8GB VRAM",tiempo:"~15 segundos"};case"gfpgan":return{nombre:"GFPGAN Face Enhancement",descripcion:"Especializado en mejora facial",vram:"6GB VRAM",tiempo:"~8 segundos"};case"sketchface":return{nombre:"SketchFaceNeRF",descripcion:"Máxima calidad - Requiere recursos",vram:"24GB VRAM",tiempo:"~45 segundos"};default:return{nombre:"",descripcion:"",vram:"",tiempo:""}}};return a.jsxs("div",{className:"max-w-7xl mx-auto space-y-6",children:[a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>c(-1),className:"p-2 text-gray-400 hover:text-white hover:bg-slate-700 rounded-md transition-colors",children:a.jsx(zl,{size:20})}),a.jsxs("div",{children:[a.jsxs("h1",{className:"text-3xl font-bold text-white flex items-center space-x-3",children:[a.jsx(qt,{className:"w-8 h-8 text-purple-400"}),a.jsx("span",{children:"Generación IA - Identikit Fotorrealista"})]}),a.jsx("p",{className:"text-gray-400",children:s?`Procesando caso: ${s}`:"Sistema de conversión sketch-to-face con IA"})]})]})}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[a.jsxs("div",{className:"lg:col-span-1 space-y-6",children:[a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(as,{className:"w-6 h-6 mr-2 text-blue-400"}),"Configuración IA"]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Modelo de IA"}),a.jsxs("select",{value:d.modelo,onChange:B=>se("modelo",B.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"controlnet",children:"ControlNet + Stable Diffusion"}),a.jsx("option",{value:"gfpgan",children:"GFPGAN Face Enhancement"}),a.jsx("option",{value:"sketchface",children:"SketchFaceNeRF"})]}),(()=>{const B=we(d.modelo);return a.jsxs("div",{className:"mt-2 p-2 bg-slate-700/50 rounded text-xs",children:[a.jsx("p",{className:"text-white font-medium",children:B.nombre}),a.jsx("p",{className:"text-gray-400",children:B.descripcion}),a.jsxs("p",{className:"text-gray-500",children:["Requiere: ",B.vram," | Tiempo: ",B.tiempo]})]})})()]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Calidad de Salida"}),a.jsxs("select",{value:d.calidad,onChange:B=>se("calidad",B.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"baja",children:"Baja (Rápido)"}),a.jsx("option",{value:"media",children:"Media (Balanceado)"}),a.jsx("option",{value:"alta",children:"Alta (Recomendado)"}),a.jsx("option",{value:"ultra",children:"Ultra (Lento)"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Tamaño de Imagen"}),a.jsxs("select",{value:d.tamaño,onChange:B=>se("tamaño",B.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"512x512",children:"512x512 (Rápido)"}),a.jsx("option",{value:"768x768",children:"768x768 (Recomendado)"}),a.jsx("option",{value:"1024x1024",children:"1024x1024 (Alta calidad)"})]})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:["Steps de Difusión: ",d.steps]}),a.jsx("input",{type:"range",min:"10",max:"50",value:d.steps,onChange:B=>se("steps",parseInt(B.target.value)),className:"w-full"}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Más steps = mejor calidad, más tiempo"})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:["Guidance Scale: ",d.guidance]}),a.jsx("input",{type:"range",min:"1",max:"20",step:"0.5",value:d.guidance,onChange:B=>se("guidance",parseFloat(B.target.value)),className:"w-full"}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Control de adherencia al boceto"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Seed (Semilla)"}),a.jsxs("div",{className:"flex space-x-2",children:[a.jsx("input",{type:"number",value:d.seed,onChange:B=>se("seed",parseInt(B.target.value)),className:"flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",placeholder:"-1 (aleatorio)"}),a.jsx("button",{onClick:$e,className:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors",children:a.jsx(El,{size:16})})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"-1 para semilla aleatoria"})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("label",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",checked:d.procesarRostro,onChange:B=>se("procesarRostro",B.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300 text-sm",children:"Enfoque en rostro"})]}),a.jsxs("label",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",checked:d.mejorarCalidad,onChange:B=>se("mejorarCalidad",B.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300 text-sm",children:"Post-procesamiento"})]})]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Estado del Sistema"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("span",{className:"text-gray-400 flex items-center",children:[a.jsx($o,{className:"w-4 h-4 mr-2"}),"GPU"]}),a.jsx("span",{className:"text-green-400 font-medium",children:"RTX 4090 - 24GB"})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("span",{className:"text-gray-400 flex items-center",children:[a.jsx(Ud,{className:"w-4 h-4 mr-2"}),"VRAM Uso"]}),a.jsx("span",{className:"text-yellow-400 font-medium",children:"4.2GB / 24GB"})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("span",{className:"text-gray-400 flex items-center",children:[a.jsx(ts,{className:"w-4 h-4 mr-2"}),"Temperatura"]}),a.jsx("span",{className:"text-blue-400 font-medium",children:"65°C"})]})]})]})]}),a.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[!I&&a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center",children:[a.jsx(Vo,{className:"w-6 h-6 mr-2 text-green-400"}),"Cargar Boceto"]}),a.jsxs("div",{className:"border-2 border-dashed border-slate-600 rounded-lg p-8 text-center",children:[a.jsx(Vo,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-white font-medium mb-2",children:"Arrastre un boceto aquí o haga clic para seleccionar"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"Formatos soportados: JPG, PNG, BMP (máx. 10MB)"}),a.jsx("button",{className:"mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors",children:"Seleccionar Archivo"})]})]}),I&&a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsxs("h2",{className:"text-xl font-bold text-white flex items-center",children:[a.jsx(Ph,{className:"w-6 h-6 mr-2 text-yellow-400"}),"Procesamiento IA"]}),a.jsxs("div",{className:"flex space-x-2",children:[!h&&!w&&a.jsxs("button",{onClick:he,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-2 rounded-md flex items-center space-x-2 transition-all duration-200",children:[a.jsx(sd,{size:20}),a.jsx("span",{children:"Iniciar Procesamiento"})]}),h&&a.jsxs("button",{onClick:xe,className:"bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[k?a.jsx(sd,{size:20}):a.jsx(kh,{size:20}),a.jsx("span",{children:k?"Reanudar":"Pausar"})]}),a.jsxs("button",{onClick:Oe,className:"bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(El,{size:20}),a.jsx("span",{children:"Reiniciar"})]})]})]}),(h||w)&&a.jsxs("div",{className:"mb-6",children:[a.jsxs("div",{className:"flex justify-between items-center mb-2",children:[a.jsx("span",{className:"text-white font-medium",children:Q.etapa}),a.jsxs("span",{className:"text-gray-400",children:[Math.round(Q.progreso),"%"]})]}),a.jsx("div",{className:"w-full bg-slate-700 rounded-full h-3 mb-2",children:a.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300",style:{width:`${Q.progreso}%`}})}),a.jsx("p",{className:"text-gray-400 text-sm",children:Q.mensaje})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-3 flex items-center",children:[a.jsx(ns,{className:"w-5 h-5 mr-2"}),"Boceto Original"]}),a.jsx("img",{src:I,alt:"Boceto original",className:"w-full rounded-lg border border-slate-600"})]}),a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-3 flex items-center",children:[a.jsx(qt,{className:"w-5 h-5 mr-2"}),"Resultado IA"]}),V?a.jsx("img",{src:V,alt:"Imagen generada por IA",className:"w-full rounded-lg border border-slate-600"}):a.jsx("div",{className:"w-full aspect-square bg-slate-700/50 border-2 border-dashed border-slate-600 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(qt,{className:"w-12 h-12 text-gray-500 mx-auto mb-2"}),a.jsx("p",{className:"text-gray-500",children:"Resultado aparecerá aquí"})]})})]})]}),w&&a.jsxs("div",{className:"mt-6 flex justify-center space-x-4",children:[a.jsxs("button",{onClick:Fe,className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Ul,{size:20}),a.jsx("span",{children:"Descargar"})]}),s&&a.jsxs("button",{onClick:te,className:"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(rs,{size:20}),a.jsx("span",{children:"Guardar en Caso"})]}),a.jsxs("button",{onClick:Oe,className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(El,{size:20}),a.jsx("span",{children:"Generar Variación"})]})]}),C&&a.jsxs("div",{className:"mt-4 p-4 bg-red-900/30 border border-red-700 rounded-md flex items-center",children:[a.jsx(hr,{className:"w-5 h-5 text-red-400 mr-2"}),a.jsx("p",{className:"text-red-300",children:C})]}),w&&!C&&a.jsxs("div",{className:"mt-4 p-4 bg-green-900/30 border border-green-700 rounded-md flex items-center",children:[a.jsx(Fl,{className:"w-5 h-5 text-green-400 mr-2"}),a.jsxs("p",{className:"text-green-300",children:["Imagen fotorrealista generada exitosamente usando ",we(d.modelo).nombre]})]})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl",children:[a.jsxs("h3",{className:"text-lg font-bold text-white mb-4 flex items-center",children:[a.jsx(vh,{className:"w-5 h-5 mr-2 text-blue-400"}),"Información Técnica"]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[a.jsxs("div",{children:[a.jsx("h4",{className:"text-white font-medium mb-2",children:"Tecnología ControlNet"}),a.jsxs("ul",{className:"text-gray-400 space-y-1",children:[a.jsx("li",{children:"• Conversión sketch-to-face con IA"}),a.jsx("li",{children:"• Modelo Stable Diffusion 1.5"}),a.jsx("li",{children:"• Control preciso de estructura facial"}),a.jsx("li",{children:"• Optimizado para aplicaciones forenses"})]})]}),a.jsxs("div",{children:[a.jsx("h4",{className:"text-white font-medium mb-2",children:"Especificaciones"}),a.jsxs("ul",{className:"text-gray-400 space-y-1",children:[a.jsx("li",{children:"• Resolución: hasta 1024x1024"}),a.jsx("li",{children:"• Tiempo procesamiento: 15-45 seg"}),a.jsx("li",{children:"• Precisión facial: 94.7%"}),a.jsx("li",{children:"• Compatible con bocetos y fotos"})]})]})]})]})]})]})]})}function Ox({onLogin:i}){const[c,s]=S.useState(""),[d,p]=S.useState(""),[h,v]=S.useState(!1),[k,b]=S.useState(""),[w,z]=S.useState(!1),C=[{id:"001",username:"admin",password:"admin123",nombre:"Dr. Carlos Rodríguez",rango:"Inspector General",rol:"admin"},{id:"002",username:"investigador",password:"inv123",nombre:"Det. Ana Fernández",rango:"Detective Principal",rol:"investigador"},{id:"003",username:"analista",password:"ana123",nombre:"Tec. Luis Torres",rango:"Analista Forense",rol:"analista"}],M=async Q=>{Q.preventDefault(),z(!0),b(""),await new Promise(I=>setTimeout(I,1e3));const E=C.find(I=>I.username===c&&I.password===d);if(E){const{username:I,password:O,...V}=E;i(V)}else b("Credenciales inválidas. Verifique usuario y contraseña.");z(!1)};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center p-4",children:a.jsxs("div",{className:"w-full max-w-md",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("div",{className:"flex justify-center mb-4",children:a.jsx("div",{className:"p-4 bg-gradient-to-r from-blue-600 to-blue-800 rounded-full shadow-2xl",children:a.jsx(da,{className:"w-12 h-12 text-white"})})}),a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"SISTEMA FORENSE"}),a.jsx("p",{className:"text-gray-300 font-mono text-sm",children:"UNIDAD DE CRIMINALÍSTICA - IDENTIKIT IA"}),a.jsx("div",{className:"mt-4 p-2 bg-red-900/30 border border-red-700 rounded-md",children:a.jsx("p",{className:"text-red-300 text-xs font-mono",children:"🔒 ACCESO RESTRINGIDO - SOLO PERSONAL AUTORIZADO"})})]}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm rounded-lg shadow-2xl border border-slate-700 p-8",children:[a.jsxs("form",{onSubmit:M,className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Usuario"}),a.jsxs("div",{className:"relative",children:[a.jsx(Wo,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",value:c,onChange:Q=>s(Q.target.value),className:"w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Ingrese su usuario",required:!0})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-semibold mb-2",children:"Contraseña"}),a.jsxs("div",{className:"relative",children:[a.jsx(gh,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:h?"text":"password",value:d,onChange:Q=>p(Q.target.value),className:"w-full pl-10 pr-12 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20",placeholder:"Ingrese su contraseña",required:!0}),a.jsx("button",{type:"button",onClick:()=>v(!h),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:h?a.jsx(ph,{size:20}):a.jsx(ns,{size:20})})]})]}),k&&a.jsx("div",{className:"bg-red-900/30 border border-red-700 rounded-md p-3",children:a.jsx("p",{className:"text-red-300 text-sm",children:k})}),a.jsx("button",{type:"submit",disabled:w,className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-4 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20",children:w?a.jsxs("div",{className:"flex items-center justify-center",children:[a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Verificando..."]}):"Iniciar Sesión"})]}),a.jsxs("div",{className:"mt-8 pt-6 border-t border-slate-700",children:[a.jsx("p",{className:"text-gray-400 text-xs mb-3 font-semibold",children:"CREDENCIALES DE DEMOSTRACIÓN:"}),a.jsxs("div",{className:"space-y-2 text-xs",children:[a.jsx("div",{className:"bg-slate-700/50 p-2 rounded border border-slate-600",children:a.jsxs("p",{className:"text-gray-300",children:[a.jsx("span",{className:"text-blue-400 font-mono",children:"admin"})," / ",a.jsx("span",{className:"text-blue-400 font-mono",children:"admin123"})," - Inspector General"]})}),a.jsx("div",{className:"bg-slate-700/50 p-2 rounded border border-slate-600",children:a.jsxs("p",{className:"text-gray-300",children:[a.jsx("span",{className:"text-green-400 font-mono",children:"investigador"})," / ",a.jsx("span",{className:"text-green-400 font-mono",children:"inv123"})," - Detective"]})}),a.jsx("div",{className:"bg-slate-700/50 p-2 rounded border border-slate-600",children:a.jsxs("p",{className:"text-gray-300",children:[a.jsx("span",{className:"text-yellow-400 font-mono",children:"analista"})," / ",a.jsx("span",{className:"text-yellow-400 font-mono",children:"ana123"})," - Analista"]})})]})]})]}),a.jsxs("div",{className:"text-center mt-8",children:[a.jsx("p",{className:"text-gray-500 text-xs font-mono",children:"© 2025 Sistema Forense Identikit IA | Versión 1.0"}),a.jsx("p",{className:"text-gray-600 text-xs font-mono mt-1",children:"Desarrollado para Unidades de Criminalística"})]})]})})}function Ix(){const[i,c]=S.useState("general"),[s,d]=S.useState({general:{idioma:"es",tema:"dark",autoGuardado:!0,notificaciones:!0},seguridad:{sesionTimeout:30,requiereDobleAuth:!1,cifradoDatos:!0,backupAutomatico:!0},ia:{modeloDefecto:"controlnet",calidadDefecto:"alta",tamañoDefecto:"768x768",stepsDefecto:20,guidanceDefecto:7.5},sistema:{maxCasos:1e3,retencionDatos:365,compressionImagenes:!0,logAuditoria:!0}}),[p,h]=S.useState(!1),[v,k]=S.useState(null),b=[{id:"general",label:"General",icon:as},{id:"seguridad",label:"Seguridad",icon:da},{id:"ia",label:"IA & Procesamiento",icon:$o},{id:"sistema",label:"Sistema",icon:od}],w=(E,I,O)=>{d(V=>({...V,[E]:{...V[E],[I]:O}}))},z=async()=>{h(!0);try{await new Promise(E=>setTimeout(E,1e3)),localStorage.setItem("configForense",JSON.stringify(s)),k({tipo:"success",texto:"Configuración guardada exitosamente"}),setTimeout(()=>k(null),3e3)}catch{k({tipo:"error",texto:"Error al guardar la configuración"}),setTimeout(()=>k(null),3e3)}finally{h(!1)}},C=()=>{confirm("¿Está seguro de restaurar la configuración por defecto? Se perderán todos los cambios.")&&(d({general:{idioma:"es",tema:"dark",autoGuardado:!0,notificaciones:!0},seguridad:{sesionTimeout:30,requiereDobleAuth:!1,cifradoDatos:!0,backupAutomatico:!0},ia:{modeloDefecto:"controlnet",calidadDefecto:"alta",tamañoDefecto:"768x768",stepsDefecto:20,guidanceDefecto:7.5},sistema:{maxCasos:1e3,retencionDatos:365,compressionImagenes:!0,logAuditoria:!0}}),k({tipo:"success",texto:"Configuración restaurada por defecto"}),setTimeout(()=>k(null),3e3))},M=()=>{const E=JSON.stringify(s,null,2),I=new Blob([E],{type:"application/json"}),O=URL.createObjectURL(I),V=document.createElement("a");V.href=O,V.download="configuracion-forense.json",V.click(),URL.revokeObjectURL(O)},Q=()=>{switch(i){case"general":return a.jsx("div",{className:"space-y-6",children:a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Configuración General"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Idioma del Sistema"}),a.jsxs("select",{value:s.general.idioma,onChange:E=>w("general","idioma",E.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"es",children:"Español"}),a.jsx("option",{value:"en",children:"English"}),a.jsx("option",{value:"pt",children:"Português"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Tema Visual"}),a.jsxs("select",{value:s.general.tema,onChange:E=>w("general","tema",E.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"dark",children:"Oscuro (Recomendado)"}),a.jsx("option",{value:"light",children:"Claro"}),a.jsx("option",{value:"auto",children:"Automático"})]})]})]}),a.jsxs("div",{className:"mt-6 space-y-3",children:[a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.general.autoGuardado,onChange:E=>w("general","autoGuardado",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Auto-guardado de casos"})]}),a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.general.notificaciones,onChange:E=>w("general","notificaciones",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Mostrar notificaciones del sistema"})]})]})]})});case"seguridad":return a.jsx("div",{className:"space-y-6",children:a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[a.jsx(da,{className:"w-5 h-5 mr-2 text-red-400"}),"Configuración de Seguridad"]}),a.jsx("div",{className:"bg-red-900/20 border border-red-700 rounded-md p-4 mb-6",children:a.jsx("p",{className:"text-red-300 text-sm",children:"⚠️ ADVERTENCIA: Estos ajustes afectan la seguridad del sistema forense. Solo personal autorizado debe modificar estas configuraciones."})}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Timeout de Sesión (minutos)"}),a.jsx("input",{type:"number",min:"5",max:"240",value:s.seguridad.sesionTimeout,onChange:E=>w("seguridad","sesionTimeout",parseInt(E.target.value)),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none"})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.seguridad.requiereDobleAuth,onChange:E=>w("seguridad","requiereDobleAuth",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Requerir autenticación de doble factor"})]}),a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.seguridad.cifradoDatos,onChange:E=>w("seguridad","cifradoDatos",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Cifrado de datos en reposo"})]}),a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.seguridad.backupAutomatico,onChange:E=>w("seguridad","backupAutomatico",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Backup automático diario"})]})]})]})]})});case"ia":return a.jsx("div",{className:"space-y-6",children:a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[a.jsx($o,{className:"w-5 h-5 mr-2 text-purple-400"}),"Configuración de IA y Procesamiento"]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Modelo IA por Defecto"}),a.jsxs("select",{value:s.ia.modeloDefecto,onChange:E=>w("ia","modeloDefecto",E.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"controlnet",children:"ControlNet + Stable Diffusion"}),a.jsx("option",{value:"gfpgan",children:"GFPGAN Face Enhancement"}),a.jsx("option",{value:"sketchface",children:"SketchFaceNeRF"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Calidad por Defecto"}),a.jsxs("select",{value:s.ia.calidadDefecto,onChange:E=>w("ia","calidadDefecto",E.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"baja",children:"Baja (Rápido)"}),a.jsx("option",{value:"media",children:"Media"}),a.jsx("option",{value:"alta",children:"Alta (Recomendado)"}),a.jsx("option",{value:"ultra",children:"Ultra (Lento)"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Tamaño de Imagen por Defecto"}),a.jsxs("select",{value:s.ia.tamañoDefecto,onChange:E=>w("ia","tamañoDefecto",E.target.value),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none",children:[a.jsx("option",{value:"512x512",children:"512x512"}),a.jsx("option",{value:"768x768",children:"768x768 (Recomendado)"}),a.jsx("option",{value:"1024x1024",children:"1024x1024"})]})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:["Steps por Defecto: ",s.ia.stepsDefecto]}),a.jsx("input",{type:"range",min:"10",max:"50",value:s.ia.stepsDefecto,onChange:E=>w("ia","stepsDefecto",parseInt(E.target.value)),className:"w-full"})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:["Guidance Scale por Defecto: ",s.ia.guidanceDefecto]}),a.jsx("input",{type:"range",min:"1",max:"20",step:"0.5",value:s.ia.guidanceDefecto,onChange:E=>w("ia","guidanceDefecto",parseFloat(E.target.value)),className:"w-full"})]})]})]})});case"sistema":return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[a.jsx(od,{className:"w-5 h-5 mr-2 text-green-400"}),"Configuración del Sistema"]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Máximo de Casos en Base de Datos"}),a.jsx("input",{type:"number",min:"100",max:"10000",value:s.sistema.maxCasos,onChange:E=>w("sistema","maxCasos",parseInt(E.target.value)),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-gray-300 text-sm font-medium mb-2",children:"Retención de Datos (días)"}),a.jsx("input",{type:"number",min:"30",max:"3650",value:s.sistema.retencionDatos,onChange:E=>w("sistema","retencionDatos",parseInt(E.target.value)),className:"w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none"})]})]}),a.jsxs("div",{className:"mt-6 space-y-3",children:[a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.sistema.compressionImagenes,onChange:E=>w("sistema","compressionImagenes",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Compresión automática de imágenes"})]}),a.jsxs("label",{className:"flex items-center space-x-3",children:[a.jsx("input",{type:"checkbox",checked:s.sistema.logAuditoria,onChange:E=>w("sistema","logAuditoria",E.target.checked),className:"rounded"}),a.jsx("span",{className:"text-gray-300",children:"Registro de auditoría completo"})]})]})]}),a.jsxs("div",{className:"bg-slate-700/50 rounded-lg p-6",children:[a.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Estado Actual del Sistema"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[a.jsxs("div",{className:"text-center",children:[a.jsx(Ud,{className:"w-8 h-8 text-blue-400 mx-auto mb-2"}),a.jsx("p",{className:"text-white font-medium",children:"Almacenamiento"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"45GB / 500GB"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx(ts,{className:"w-8 h-8 text-green-400 mx-auto mb-2"}),a.jsx("p",{className:"text-white font-medium",children:"CPU"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"23% utilizado"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx(jh,{className:"w-8 h-8 text-yellow-400 mx-auto mb-2"}),a.jsx("p",{className:"text-white font-medium",children:"RAM"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"8.2GB / 32GB"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx(Eh,{className:"w-8 h-8 text-purple-400 mx-auto mb-2"}),a.jsx("p",{className:"text-white font-medium",children:"Red"}),a.jsx("p",{className:"text-gray-400 text-sm",children:"Conectado"})]})]})]})]});default:return null}};return a.jsxs("div",{className:"max-w-6xl mx-auto space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Configuración del Sistema"}),a.jsx("p",{className:"text-gray-400",children:"Ajustes y preferencias del sistema forense"})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{onClick:M,className:"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(Ul,{size:20}),a.jsx("span",{children:"Exportar"})]}),a.jsxs("button",{onClick:C,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors",children:[a.jsx(El,{size:20}),a.jsx("span",{children:"Restaurar"})]}),a.jsx("button",{onClick:z,disabled:p,className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 text-white px-6 py-2 rounded-md flex items-center space-x-2 transition-all duration-200",children:p?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),a.jsx("span",{children:"Guardando..."})]}):a.jsxs(a.Fragment,{children:[a.jsx(rs,{size:20}),a.jsx("span",{children:"Guardar"})]})})]})]}),v&&a.jsx("div",{className:`p-4 rounded-md border ${v.tipo==="success"?"bg-green-900/30 border-green-700 text-green-300":"bg-red-900/30 border-red-700 text-red-300"}`,children:a.jsx("p",{children:v.texto})}),a.jsxs("div",{className:"bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg shadow-xl",children:[a.jsx("div",{className:"border-b border-slate-700",children:a.jsx("nav",{className:"flex space-x-8 px-6",children:b.map(E=>{const I=E.icon;return a.jsxs("button",{onClick:()=>c(E.id),className:`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${i===E.id?"border-blue-500 text-blue-400":"border-transparent text-gray-400 hover:text-gray-300"}`,children:[a.jsx(I,{size:20}),a.jsx("span",{children:E.label})]},E.id)})})}),a.jsx("div",{className:"p-6",children:Q()})]})]})}function Mx(){const[i,c]=S.useState(null);S.useEffect(()=>{const p=localStorage.getItem("usuario");p&&c(JSON.parse(p))},[]);const s=p=>{c(p),localStorage.setItem("usuario",JSON.stringify(p))},d=()=>{c(null),localStorage.removeItem("usuario")};return i?a.jsx(ah,{children:a.jsx(zh,{usuario:i,onLogout:d,children:a.jsxs(Xm,{children:[a.jsx(Ln,{path:"/",element:a.jsx(_h,{})}),a.jsx(Ln,{path:"/nuevo-caso",element:a.jsx(_x,{})}),a.jsx(Ln,{path:"/casos",element:a.jsx(Dx,{})}),a.jsx(Ln,{path:"/caso/:id",element:a.jsx(Rx,{})}),a.jsx(Ln,{path:"/generar-ia",element:a.jsx(Tx,{})}),a.jsx(Ln,{path:"/configuracion",element:a.jsx(Ix,{})})]})})}):a.jsx(Ox,{onLogin:s})}rm.createRoot(document.getElementById("root")).render(a.jsx(S.StrictMode,{children:a.jsx(lm,{children:a.jsx(Mx,{})})}));
