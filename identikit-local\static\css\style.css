/* 
Sistema Identikit Fotorrealista - Estilos Personalizados
Versión optimizada para uso local
*/

/* Variables CSS */
:root {
    --primary-color: #1a365d;
    --secondary-color: #2d3748;
    --accent-color: #3182ce;
    --success-color: #38a169;
    --warning-color: #d69e2e;
    --danger-color: #e53e3e;
    --light-bg: #f7fafc;
    --dark-text: #2d3748;
    --border-color: #e2e8f0;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
}

/* Estilos base */
body {
    background-color: var(--light-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--dark-text);
}

/* Navbar personalizada */
.navbar-brand {
    font-weight: bold;
    color: white !important;
    font-size: 1.2rem;
}

.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    box-shadow: var(--shadow);
    border: none;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
}

/* Sidebar */
.sidebar {
    background: white;
    min-height: calc(100vh - 56px);
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    padding: 0;
    border-right: 1px solid var(--border-color);
}

.sidebar .nav-link {
    color: var(--dark-text);
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover {
    background-color: #f8f9fa;
    color: var(--accent-color);
    text-decoration: none;
}

.sidebar .nav-link.active {
    background-color: var(--accent-color);
    color: white;
    border-left: 4px solid var(--primary-color);
}

/* Contenido principal */
.main-content {
    padding: 20px;
    min-height: calc(100vh - 56px);
}

/* Cards personalizadas */
.card {
    border: none;
    box-shadow: var(--shadow);
    border-radius: 8px;
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 8px 8px 0 0 !important;
    border: none;
    padding: 15px 20px;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Botones personalizados */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #2f855a);
}

.btn-success:hover {
    background: linear-gradient(135deg, #2f855a, #276749);
    transform: translateY(-1px);
}

.btn-outline-primary {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* Tarjetas de estadísticas */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Badges personalizados */
.badge {
    font-size: 0.8rem;
    padding: 6px 10px;
    border-radius: 20px;
}

.badge-status {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Imágenes */
.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.02);
}

/* Área de carga */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--accent-color);
    background-color: #e6f3ff;
}

.upload-area.dragover {
    border-color: var(--accent-color);
    background-color: #e6f3ff;
    transform: scale(1.02);
}

.upload-content i {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 15px;
}

/* Formularios */
.form-control {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(49, 130, 206, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-text);
    margin-bottom: 8px;
}

/* Tablas */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    color: var(--dark-text);
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* Alertas */
.alert {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
}

.alert-info {
    background-color: #e6f3ff;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Progreso */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* Modales */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 20px;
}

.modal-body {
    padding: 20px;
}

/* Animaciones */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 56px);
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 20px;
    }
}

/* Utilidades */
.text-shadow {
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.border-radius {
    border-radius: 8px;
}

.shadow-sm {
    box-shadow: var(--shadow);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}
