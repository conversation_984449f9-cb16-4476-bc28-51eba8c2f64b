#!/usr/bin/env python3
"""
Script para buscar papers académicos sobre sketch-to-face generation
y tecnologías de IA generativa relacionadas
"""

import asyncio
import concurrent.futures
import json
from external_api.data_sources.client import get_client

async def search_papers(client, query, num_results=20, start_year=2020):
    """Buscar papers académicos con query específica"""
    print(f"Buscando papers sobre: {query}")
    try:
        result = await client.scholar.search_scholar(
            query=query,
            num_results=num_results,
            start_year=start_year,
            end_year=2024
        )
        if result["success"]:
            print(f"Encontrados {len(result['data']['papers'])} papers para '{query}'")
            return {"query": query, "papers": result["data"]["papers"]}
        else:
            print(f"Error en búsqueda '{query}': {result.get('error', 'Unknown error')}")
            return {"query": query, "papers": [], "error": result.get('error')}
    except Exception as e:
        print(f"Excepción en búsqueda '{query}': {str(e)}")
        return {"query": query, "papers": [], "error": str(e)}

async def main():
    """Función principal para buscar múltiples temas"""
    client = get_client()
    
    # Definir búsquedas específicas para la investigación
    search_queries = [
        "sketch to face generation deep learning",
        "facial sketch to photo synthesis",
        "ControlNet face generation from sketch",
        "StyleGAN face synthesis sketch conditioning",
        "face hallucination from sketch",
        "local deployment face generation AI",
        "open source sketch to photo face",
        "lightweight face generation models",
        "neural face synthesis from drawing",
        "forensic sketch to photo AI"
    ]
    
    # Ejecutar búsquedas concurrentemente
    search_tasks = [
        search_papers(client, query, num_results=15, start_year=2020) 
        for query in search_queries
    ]
    
    results = await asyncio.gather(*search_tasks)
    
    # Compilar todos los resultados
    all_papers = {}
    total_papers = 0
    
    for result in results:
        query = result["query"]
        papers = result["papers"]
        all_papers[query] = papers
        total_papers += len(papers)
        print(f"Query '{query}': {len(papers)} papers")
    
    print(f"\nTotal de papers encontrados: {total_papers}")
    
    # Guardar resultados
    with open("/workspace/data/academic_papers_sketch_to_face.json", "w", encoding="utf-8") as f:
        json.dump(all_papers, f, indent=2, ensure_ascii=False)
    
    print("Resultados guardados en /workspace/data/academic_papers_sketch_to_face.json")
    
    return all_papers

def run_search():
    """Ejecutar la búsqueda usando ThreadPoolExecutor"""
    with concurrent.futures.ThreadPoolExecutor() as executor:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            results = loop.run_until_complete(main())
            return results
        finally:
            loop.close()

if __name__ == "__main__":
    # Crear directorio de datos si no existe
    import os
    os.makedirs("/workspace/data", exist_ok=True)
    
    results = run_search()
    print(f"Búsqueda académica completada. Encontrados {sum(len(papers) for papers in results.values())} papers.")
