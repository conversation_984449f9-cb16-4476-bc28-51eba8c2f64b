{"extracted_information": "El contenido web proporcionado es la página principal de un repositorio de GitHub titulado 'Face-Sketch-to-Image-Generation-using-GAN'. Este repositorio documenta un sistema de generación de imágenes utilizando Redes Generativas Antagónicas (GAN) para convertir bocetos faciales en fotografías realistas. El archivo README.md en la raíz del repositorio describe los pasos clave del proyecto, incluyendo la instalación de requisitos, el preprocesamiento de datos (aumento de datos), el inicio del entrenamiento, la medición del rendimiento y la ejecución de pruebas.", "specifications": {}, "pricing": {}, "features": [], "statistics": {}, "temporal_info": {}, "geographical_data": {}, "references": [{"title": "Face Photo-Sketch Synthesis and Recognition", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "publication": "IEEE Transactions on Pattern Analysis and Machine Intelligence (PAMI)", "volume": "31", "issue": "11", "pages": "1955-1967", "year": 2009}, {"title": "Coupled Information-Theoretic Encoding for Face Photo-Sketch Recognition", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "publication": "Proceedings of IEEE Conference on Computer Vision and Pattern Recognition (CVPR)", "year": 2011}], "architecture": {"type": "ContextualGAN", "details_location": "El cuaderno Jupyter 'ContextualGAN.ipynb' contiene los detalles específicos de la arquitectura."}, "datasets": {"used": true, "details_location": "Existe una carpeta llamada 'Dataset'. Se menciona la necesidad de realizar aumento de datos utilizando el cuaderno 'Data Augmentation.ipynb'. El nombre específico del dataset utilizado no se detalla en el README.", "augmentation_process": "Descrito en el cuaderno 'Data Augmentation.ipynb'."}, "training_process": {"description": "El entrenamiento del modelo GAN se inicia utilizando el cuaderno 'ContextualGAN.ipynb'.", "details_location": "El cuaderno Jupyter 'ContextualGAN.ipynb' contiene la lógica y los parámetros del entrenamiento."}, "technical_requirements": {"libraries": "Las dependencias se listan en el archivo 'requirements.txt'.", "specific_instructions": ["Instalar requisitos usando pip: `pip install -r requirements.txt`", "Instalar keras-contrib: Clonar el repositorio `https://www.github.com/keras-team/keras-contrib.git`, navegar al directorio y ejecutar `python setup.py install`."]}, "results_obtained": {"evaluation_metrics": ["SSIM (Structural Similarity Index)", "Verification Accuracy (L2-norm)"], "computation_details_location": "El cálculo de SSIM y L2-norm se realiza utilizando el cuaderno 'Compute SSIM and L2-norm.ipynb'.", "image_generation": "La generación de una sola imagen a partir de un boceto se realiza utilizando el cuaderno 'Predict Image.ipynb'."}}