{"extracted_information": "La guía proporciona una explicación detallada de ControlNet, su funcionamiento, instalación y uso dentro de AUTOMATIC1111 Stable Diffusion Web UI. Cubre los distintos preprocesadores y modelos disponibles, sus configuraciones y ejemplos de uso para diversas tareas como control de poses, transferencia de estilo e inpainting.", "specifications": {}, "pricing": {}, "features": ["Control de generación de imágenes añadiendo condiciones extra", "Especificar poses humanas", "Copiar la composición de otra imagen", "Generar una imagen similar", "Convertir un garabato en una imagen profesional", "Uso de múltiples ControlNets simultáneamente", "Controlar peso y pasos de control", "Controlar modo de operación (Equilibrado, El prompt es más importante, ControlNet es más importante)", "Controlar modo de redimensionamiento (Solo Redimensionar, Recortar y Redimensionar, Redimensionar y Rellenar)", "Funcionalidad de Inpainting con ControlNet"], "statistics": {}, "temporal_info": {"updated_date": "July 7, 2024"}, "geographical_data": {}, "references": ["Adding Conditional Control to Text-to-Image Diffusion Models (<PERSON><PERSON><PERSON>)", "Openpose (CMU-Perceptual-Computing-Lab)", "DWPose (IDEA-Research)", "AdaIN (Adaptive Instance Normalization) paper", "IP-Adapter: Text Compatible Image Prompt Adapter for Text-to-Image Diffusion Models (<PERSON> et al.)", "HED (Holistically-Nested Edge Detection) paper", "Pidinet (Pixel Difference network)", "XDoG (EXtended Difference o­f Gaussian) paper", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Sense-X)", "ADE20K dataset", "OneFormer (SHI-Labs)", "COCO dataset", "MiDAS (isl-org)", "normal uncertainty method (<PERSON><PERSON> et al.)", "ControlNet v1.0 Github", "ControlNet v1.1 model files (HuggingFace)", "ControlNet v1.1 Github", "ControlNet Extension for AUTOMATIC1111 Web-UI Github", "Magic Poser (webapp.magicposer.com)", "lama (advimman/lama)", "T2I-Adapter (TencentARC)", "IP-Adapter HuggingFace"], "extracted_information_details": {"instalacion": {"google_colab": "En la sección Extensions del notebook Colab, marcar ControlNet. Presionar el botón Play para iniciar AUTOMATIC1111.", "windows_mac": ["Asegurarse de tener AUTOMATIC1111 actualizado.", "Navegar a la página Extensions en AUTOMATIC1111.", "Seleccionar la pestaña Install from URL.", "Introducir la URL https://github.com/Mikubill/sd-webui-controlnet en el campo URL for extension’s repository.", "<PERSON>cer clic en Install.", "Esperar el mensaje de confirmación.", "Reiniciar AUTOMATIC1111.", "Visitar la página de modelos ControlNet en HuggingFace (https://huggingface.co/lllyasviel/ControlNet-v1-1/tree/main).", "<PERSON>cargar todos los archivos de modelo (terminados en .pth). Opcionalmente, descargar openpose y canny para empezar.", "Colocar los archivos de modelo en el directorio models de la extensión ControlNet (stable-diffusion-webui\\extensions\\sd-webui-controlnet\\models).", "Reiniciar AUTOMATIC1111 webui. Un panel ControlNet aparecerá en la pestaña txt2img.", "Para T2I adapters, descargar modelos de https://huggingface.co/TencentARC/T2I-Adapter/tree/main/models (archivos t2iadapter_XXXXX.pth) y colocarlos en la misma carpeta models de ControlNet."], "actualizacion": {"web_ui": ["Ir a la página Extensions.", "En la pestaña Installed, hacer clic en Check for updates.", "Esperar mensaje de confirmación.", "Cerrar y reiniciar completamente AUTOMATIC1111 Web-UI."], "linea_comandos": ["Abrir Terminal (Mac) o PowerShell (Windows).", "Navegar a la carpeta de la extensión ControlNet (cd stable-diffusion-webui/extensions/sd-webui-controlnet).", "Eje<PERSON>ar git pull para actualizar."]}}, "requerimientos_hardware": {"vram": "Menciona una opción 'Low VRAM' para GPUs con menos de 8GB VRAM. Indica que es experimental."}, "modelos_sketch_to_image": {"general": "Los preprocesadores 'Scribbles' convierten una imagen en un garabato similar a uno dibujado a mano. Se usan con el modelo de control 'scribble'.", "preprocesadores_scribbles": ["Scribble HED: Basado en HED, produce líneas de garabato gruesas, adecuado para recolorear y reestilizar.", "Scribble Pidinet: <PERSON><PERSON><PERSON> Pi<PERSON>t, detecta curvas y bordes rectos, similar a HED pero con líneas más limpias y menos detalles.", "Scribble xdog: <PERSON><PERSON><PERSON> en XDoG, permite ajustar el nivel de detalle mediante el XDoG threshold.", "Line Art: Convierte la imagen en un dibujo simple de contorno, útil para estilos de arte lineal. Se usa con el modelo 'lineart'."]}, "configuracion_optima": {"controles_entrada": {"canvas_imagen": "Arrastrar y soltar imagen de entrada. Se procesa por el preprocesador seleccionado para crear un 'control map'.", "icono_escritura": "Crea un lienzo nuevo con una imagen blanca para dibujar un garabato directamente.", "icono_camara": "Toma una foto con la cámara del dispositivo como imagen de entrada."}, "seleccion_modelo": {"enable": "Activa/desactiva ControlNet.", "low_vram": "Opción para GPUs con menos de 8GB VRAM (experimental).", "allow_preview": "Permite previsualizar el efecto del preprocesador (recomendado). Usar icono de explosión para previsualizar.", "pixel_perfect": "Hace que la imagen preprocesada tenga el mismo tamaño que la imagen de texto a imagen (importante para detalles pequeños).", "preprocessor": "Selecciona el preprocesador (anotador) para la imagen de entrada (ej. detección de bordes, profundidad). 'None' usa la imagen de entrada directamente como 'control map'.", "model": "Selecciona el modelo ControlNet a usar. Normalmente debe corresponder con el preprocesador seleccionado. Se usa junto con el modelo Stable Diffusion principal."}, "control_weight": {"peso": "Enfasis del 'control map' relativo al prompt (similar al peso de palabras clave). Un peso menor implica que ControlNet demanda menos que la imagen siga el 'control map'. Valores entre 0 y 1 son comunes, pero pueden ser mayores.", "paso_inicio_controlnet": "Paso del muestreo donde ControlNet comienza a aplicarse. 0 es el primer paso.", "paso_fin_controlnet": "Paso del muestreo donde ControlNet deja de aplicarse. 1 es el último paso. Los pasos iniciales tienen más impacto en la composición global."}, "control_mode": {"balanced": "Modo estándar. ControlNet se aplica tanto al condicionamiento como al un-condicionamiento.", "my_prompt_is_more_important": "El efecto de ControlNet se reduce gradualmente. El prompt tiene más influencia.", "controlnet_is_more_important": "Desactiva ControlNet en el un-condicionamiento. La escala CFG actúa como multiplicador para el efecto de ControlNet."}, "resize_mode": {"just_resize": "Escala ancho y alto del 'control map' independientemente para ajustarse al lienzo, cambiando la relación de aspecto del 'control map'.", "crop_and_resize": "Ajusta el lienzo de imagen para que esté dentro del 'control map'. Recorta el 'control map' para que tenga el mismo tamaño que el lienzo.", "resize_and_fill": "Ajusta todo el 'control map' al lienzo de imagen. Extiende el 'control map' con valores vacíos para que tenga el mismo tamaño que el lienzo."}, "multiples_controlnets": "Es posible usar múltiples ControlNets (ej. uno para pose, otro para fondo). La cantidad se configura en Settings -> controlnet. Se pueden ajustar los pesos de cada ControlNet."}}}