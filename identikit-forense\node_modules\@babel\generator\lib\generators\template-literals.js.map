{"version": 3, "names": ["TaggedTemplateExpression", "node", "print", "tag", "typeParameters", "quasi", "TemplateElement", "Error", "_printTemplate", "substitutions", "quasis", "partRaw", "i", "length", "value", "raw", "token", "tokenMap", "findMatching", "_catchUpTo", "loc", "start", "TemplateLiteral", "expressions"], "sources": ["../../src/generators/template-literals.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\n\nexport function TaggedTemplateExpression(\n  this: Printer,\n  node: t.TaggedTemplateExpression,\n) {\n  this.print(node.tag);\n  if (process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n    this.print(node.typeArguments);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n    this.print(node.typeParameters);\n  }\n  this.print(node.quasi);\n}\n\nexport function TemplateElement(this: Printer) {\n  throw new Error(\"TemplateElement printing is handled in TemplateLiteral\");\n}\n\nexport type TemplateLiteralBase = t.Node & {\n  quasis: t.TemplateElement[];\n};\n\nexport function _printTemplate<T extends t.Node>(\n  this: Printer,\n  node: TemplateLiteralBase,\n  substitutions: T[],\n) {\n  const quasis = node.quasis;\n  let partRaw = \"`\";\n  for (let i = 0; i < quasis.length - 1; i++) {\n    partRaw += quasis[i].value.raw;\n    this.token(partRaw + \"${\", true);\n    this.print(substitutions[i]);\n    partRaw = \"}\";\n\n    // In Babel 7 we have individual tokens for ${ and }, so the automatic\n    // catchup logic does not work. Manually look for those tokens.\n    if (!process.env.BABEL_8_BREAKING && this.tokenMap) {\n      const token = this.tokenMap.findMatching(node, \"}\", i);\n      if (token) this._catchUpTo(token.loc.start);\n    }\n  }\n  partRaw += quasis[quasis.length - 1].value.raw;\n  this.token(partRaw + \"`\", true);\n}\n\nexport function TemplateLiteral(this: Printer, node: t.TemplateLiteral) {\n  this._printTemplate(node, node.expressions);\n}\n"], "mappings": ";;;;;;;;;AAGO,SAASA,wBAAwBA,CAEtCC,IAAgC,EAChC;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC;EAIb;IAEL,IAAI,CAACD,KAAK,CAACD,IAAI,CAACG,cAAc,CAAC;EACjC;EACA,IAAI,CAACF,KAAK,CAACD,IAAI,CAACI,KAAK,CAAC;AACxB;AAEO,SAASC,eAAeA,CAAA,EAAgB;EAC7C,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;AAC3E;AAMO,SAASC,cAAcA,CAE5BP,IAAyB,EACzBQ,aAAkB,EAClB;EACA,MAAMC,MAAM,GAAGT,IAAI,CAACS,MAAM;EAC1B,IAAIC,OAAO,GAAG,GAAG;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IAC1CD,OAAO,IAAID,MAAM,CAACE,CAAC,CAAC,CAACE,KAAK,CAACC,GAAG;IAC9B,IAAI,CAACC,KAAK,CAACL,OAAO,GAAG,IAAI,EAAE,IAAI,CAAC;IAChC,IAAI,CAACT,KAAK,CAACO,aAAa,CAACG,CAAC,CAAC,CAAC;IAC5BD,OAAO,GAAG,GAAG;IAIb,IAAqC,IAAI,CAACM,QAAQ,EAAE;MAClD,MAAMD,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACC,YAAY,CAACjB,IAAI,EAAE,GAAG,EAAEW,CAAC,CAAC;MACtD,IAAII,KAAK,EAAE,IAAI,CAACG,UAAU,CAACH,KAAK,CAACI,GAAG,CAACC,KAAK,CAAC;IAC7C;EACF;EACAV,OAAO,IAAID,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG;EAC9C,IAAI,CAACC,KAAK,CAACL,OAAO,GAAG,GAAG,EAAE,IAAI,CAAC;AACjC;AAEO,SAASW,eAAeA,CAAgBrB,IAAuB,EAAE;EACtE,IAAI,CAACO,cAAc,CAACP,IAAI,EAAEA,IAAI,CAACsB,WAAW,CAAC;AAC7C", "ignoreList": []}