<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistema Identikit</title>
    
    <!-- CSS Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .system-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .system-subtitle {
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .feature-item i {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3182ce;
            box-shadow: 0 0 0 0.2rem rgba(49, 130, 206, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #3182ce 0%, #1a365d 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(49, 130, 206, 0.4);
            color: white;
        }
        
        .credentials-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .credentials-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .system-title {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Panel izquierdo -->
                <div class="col-lg-6 login-left">
                    <div class="logo">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="system-title">Sistema Identikit Fotorrealista</div>
                    <div class="system-subtitle">Unidad de Criminalística e Investigación</div>
                    
                    <div class="features mt-4">
                        <div class="feature-item">
                            <i class="bi bi-robot"></i>
                            <span>Conversión IA de bocetos</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-database"></i>
                            <span>Base de datos criminal</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-shield-lock"></i>
                            <span>Sistema seguro offline</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-graph-up"></i>
                            <span>Análisis y reportes</span>
                        </div>
                    </div>
                </div>
                
                <!-- Panel derecho - Formulario -->
                <div class="col-lg-6 login-right">
                    <h2 class="text-center mb-4" style="color: #2d3748; font-weight: bold;">
                        Iniciar Sesión
                    </h2>
                    
                    {% if error %}
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        {{ error }}
                    </div>
                    {% endif %}
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="bi bi-person"></i> Usuario
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock"></i> Contraseña
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-login">
                            <i class="bi bi-box-arrow-in-right"></i>
                            Ingresar al Sistema
                        </button>
                    </form>
                    
                    <!-- Información de credenciales -->
                    <div class="credentials-info">
                        <div class="credentials-title">
                            <i class="bi bi-info-circle"></i> Credenciales de Prueba
                        </div>
                        <div class="credential-item">
                            <span><strong>Admin:</strong></span>
                            <span>admin / admin123</span>
                        </div>
                        <div class="credential-item">
                            <span><strong>Investigador:</strong></span>
                            <span>investigador / inv123</span>
                        </div>
                        <div class="credential-item">
                            <span><strong>Analista:</strong></span>
                            <span>analista / ana123</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animación de entrada
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.login-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
        
        // Auto-focus en el campo de usuario
        document.getElementById('username').focus();
    </script>
</body>
</html>
