#!/usr/bin/env python3
"""
Ejemplo de implementación de ControlNet para conversión sketch-to-face
Solución recomendada para producción - Balance calidad/complejidad
"""

import torch
import numpy as np
from PIL import Image
from diffusers import StableDiffusionControlNetPipeline, ControlNetModel, DDIMScheduler
import cv2
from controlnet_aux import HEDdetector, CannyDetector

class SketchToFacePipeline:
    """Pipeline para conversión de sketch a rostro fotorrealista usando ControlNet"""
    
    def __init__(self, device="cuda", low_vram_mode=False):
        """
        Inicializar pipeline ControlNet
        
        Args:
            device: "cuda" para GPU, "cpu" para CPU (lento)
            low_vram_mode: True para GPUs con <8GB VRAM
        """
        self.device = device
        self.low_vram_mode = low_vram_mode
        
        print("Inicializando pipeline ControlNet...")
        
        # Cargar modelo ControlNet para scribbles/sketches
        self.controlnet = ControlNetModel.from_pretrained(
            "lllyasviel/sd-controlnet-scribble",
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            variant="fp16" if device == "cuda" else None
        )
        
        # Cargar pipeline principal
        self.pipe = StableDiffusionControlNetPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            controlnet=self.controlnet,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            variant="fp16" if device == "cuda" else None,
            safety_checker=None,
            requires_safety_checker=False
        )
        
        # Configurar scheduler para mejor calidad
        self.pipe.scheduler = DDIMScheduler.from_config(self.pipe.scheduler.config)
        
        # Optimizaciones de memoria
        if device == "cuda":
            self.pipe = self.pipe.to(device)
            if low_vram_mode:
                self.pipe.enable_model_cpu_offload()
                self.pipe.enable_attention_slicing()
        
        # Detectores auxiliares para preprocessing
        self.hed_detector = HEDdetector.from_pretrained('lllyasviel/Annotators')
        self.canny_detector = CannyDetector()
        
        print("Pipeline inicializado correctamente.")
    
    def preprocess_sketch(self, sketch_image, method="auto"):
        """
        Preprocesar sketch/identikit para ControlNet
        
        Args:
            sketch_image: PIL Image o path del sketch
            method: "auto", "canny", "hed", "scribble"
        
        Returns:
            PIL Image del control map procesado
        """
        if isinstance(sketch_image, str):
            sketch_image = Image.open(sketch_image)
        
        # Convertir a RGB si es necesario
        if sketch_image.mode != "RGB":
            sketch_image = sketch_image.convert("RGB")
        
        # Redimensionar a resolución óptima
        sketch_image = sketch_image.resize((512, 512))
        
        if method == "auto":
            # Detectar automáticamente el mejor método
            # Para identikits policiales, HED funciona bien
            control_image = self.hed_detector(sketch_image)
        elif method == "canny":
            control_image = self.canny_detector(sketch_image)
        elif method == "hed":
            control_image = self.hed_detector(sketch_image)
        elif method == "scribble":
            # Para sketches más simples
            control_image = sketch_image
        else:
            raise ValueError(f"Método no soportado: {method}")
        
        return control_image
    
    def generate_face(self, sketch_image, prompt=None, negative_prompt=None, 
                     num_inference_steps=20, guidance_scale=7.5, controlnet_conditioning_scale=1.0,
                     method="auto", seed=None):
        """
        Generar rostro fotorrealista desde sketch
        
        Args:
            sketch_image: PIL Image o path del sketch
            prompt: Descripción del rostro deseado
            negative_prompt: Qué evitar en la generación
            num_inference_steps: Pasos de inferencia (más = mejor calidad, más lento)
            guidance_scale: Qué tan fuerte seguir el prompt
            controlnet_conditioning_scale: Qué tan fuerte seguir el sketch
            method: Método de preprocesamiento
            seed: Semilla para reproducibilidad
        
        Returns:
            PIL Image del rostro generado
        """
        # Preprocesar sketch
        control_image = self.preprocess_sketch(sketch_image, method)
        
        # Prompt por defecto para rostros realistas
        if prompt is None:
            prompt = "realistic human face portrait, high quality, detailed facial features, professional photography"
        
        if negative_prompt is None:
            negative_prompt = "cartoon, anime, drawing, sketch, low quality, blurry, distorted"
        
        # Configurar semilla para reproducibilidad
        if seed is not None:
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed)
        
        # Generar imagen
        with torch.autocast("cuda" if self.device == "cuda" else "cpu"):
            result = self.pipe(
                prompt=prompt,
                image=control_image,
                negative_prompt=negative_prompt,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                controlnet_conditioning_scale=controlnet_conditioning_scale,
                generator=torch.Generator(device=self.device).manual_seed(seed) if seed else None
            )
        
        return result.images[0]
    
    def batch_generate(self, sketch_images, prompts=None, **kwargs):
        """
        Generar múltiples rostros en lote
        
        Args:
            sketch_images: Lista de PIL Images o paths
            prompts: Lista de prompts (opcional)
            **kwargs: Argumentos adicionales para generate_face
        
        Returns:
            Lista de PIL Images generadas
        """
        results = []
        
        for i, sketch in enumerate(sketch_images):
            prompt = prompts[i] if prompts and i < len(prompts) else None
            result = self.generate_face(sketch, prompt=prompt, **kwargs)
            results.append(result)
            print(f"Generado {i+1}/{len(sketch_images)}")
        
        return results

# Ejemplo de uso para criminalística
def example_forensic_usage():
    """Ejemplo específico para uso criminalístico"""
    
    # Inicializar pipeline
    # Para GPUs de 8GB o menos, usar low_vram_mode=True
    pipeline = SketchToFacePipeline(device="cuda", low_vram_mode=True)
    
    # Cargar identikit policial
    sketch_path = "/workspace/identikits/suspect_sketch.jpg"
    
    # Prompts específicos para criminalística
    forensic_prompts = [
        "realistic human face, caucasian male, age 30-35, professional photography, high detail",
        "realistic human face, hispanic female, age 25-30, clear facial features, mugshot style",
        "realistic human face, african american male, age 40-45, detailed skin texture, neutral expression"
    ]
    
    # Configuraciones para diferentes calidades
    settings = {
        "quick": {"num_inference_steps": 15, "guidance_scale": 7.0},
        "balanced": {"num_inference_steps": 20, "guidance_scale": 7.5},
        "high_quality": {"num_inference_steps": 30, "guidance_scale": 8.0}
    }
    
    # Generar múltiples variaciones
    for i, prompt in enumerate(forensic_prompts):
        for quality, params in settings.items():
            result = pipeline.generate_face(
                sketch_path,
                prompt=prompt,
                seed=42 + i,  # Para reproducibilidad
                controlnet_conditioning_scale=1.2,  # Mayor fidelidad al sketch
                **params
            )
            
            # Guardar resultado
            result.save(f"/workspace/results/suspect_{i+1}_{quality}.jpg")
            print(f"Generado: suspect_{i+1}_{quality}.jpg")

# Instalación requerida
INSTALLATION_REQUIREMENTS = """
# Instalar dependencias principales
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
pip install diffusers==0.24.0
pip install transformers accelerate
pip install controlnet-aux
pip install xformers  # Opcional: para mejor rendimiento

# Para GPU con CUDA
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Verificar instalación
python -c "import torch; print(f'CUDA disponible: {torch.cuda.is_available()}')"
"""

if __name__ == "__main__":
    print("Ejemplo de implementación ControlNet para sketch-to-face")
    print("Requerimientos de instalación:")
    print(INSTALLATION_REQUIREMENTS)
    
    # Verificar disponibilidad de CUDA
    if torch.cuda.is_available():
        print(f"GPU detectada: {torch.cuda.get_device_name(0)}")
        print(f"VRAM disponible: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        print("GPU no detectada. Usando CPU (será más lento).")
    
    # Ejecutar ejemplo si hay GPU disponible
    if torch.cuda.is_available():
        try:
            example_forensic_usage()
        except Exception as e:
            print(f"Error en ejemplo: {e}")
            print("Asegúrate de tener los modelos descargados y suficiente VRAM.")
