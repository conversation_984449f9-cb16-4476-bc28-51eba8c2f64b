#!/usr/bin/env python3
"""
Script de instalación para Sistema Identikit Local
Instala dependencias y configura el sistema automáticamente
"""

import os
import sys
import subprocess
import platform

def print_banner():
    """Mostrar banner del sistema"""
    print("=" * 60)
    print("🏛️  SISTEMA IDENTIKIT FOTORREALISTA - INSTALADOR")
    print("    Versión Optimizada Local")
    print("=" * 60)
    print()

def check_python_version():
    """Verificar versión de Python"""
    print("🔍 Verificando versión de Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True

def check_pip():
    """Verificar que pip esté disponible"""
    print("🔍 Verificando pip...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip disponible - OK")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error: pip no está disponible")
        return False

def install_requirements():
    """Instalar dependencias desde requirements.txt"""
    print("📦 Instalando dependencias...")
    
    try:
        # Actualizar pip primero
        print("   Actualizando pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Instalar dependencias
        print("   Instalando paquetes requeridos...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        
        print("✅ Dependencias instaladas correctamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def setup_directories():
    """Crear directorios necesarios"""
    print("📁 Configurando directorios...")
    
    directories = [
        "uploads",
        "generated", 
        "data",
        "static/css",
        "static/js",
        "static/images",
        "templates"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ {directory}")
    
    print("✅ Directorios configurados")
    return True

def initialize_database():
    """Inicializar base de datos"""
    print("🗄️ Inicializando base de datos...")
    
    try:
        from database import init_database
        init_database()
        print("✅ Base de datos inicializada")
        return True
    except Exception as e:
        print(f"❌ Error inicializando base de datos: {e}")
        return False

def create_run_script():
    """Crear script de ejecución"""
    print("📝 Creando script de ejecución...")
    
    # Script para Windows
    if platform.system() == "Windows":
        script_content = """@echo off
echo 🚀 Iniciando Sistema Identikit Local...
echo.
echo 🌐 El sistema estará disponible en: http://localhost:5000
echo 👤 Credenciales: admin/admin123
echo.
python app.py
pause
"""
        with open("ejecutar.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("   ✅ ejecutar.bat creado")
    
    # Script para Unix/Linux/Mac
    script_content = """#!/bin/bash
echo "🚀 Iniciando Sistema Identikit Local..."
echo ""
echo "🌐 El sistema estará disponible en: http://localhost:5000"
echo "👤 Credenciales: admin/admin123"
echo ""
python3 app.py
"""
    with open("ejecutar.sh", "w") as f:
        f.write(script_content)
    
    # Hacer ejecutable en Unix
    if platform.system() != "Windows":
        os.chmod("ejecutar.sh", 0o755)
        print("   ✅ ejecutar.sh creado")
    
    print("✅ Scripts de ejecución creados")
    return True

def test_installation():
    """Probar que la instalación funcione"""
    print("🧪 Probando instalación...")
    
    try:
        # Importar módulos principales
        import flask
        import PIL
        import cv2
        import numpy
        
        print("   ✅ Flask importado")
        print("   ✅ PIL importado") 
        print("   ✅ OpenCV importado")
        print("   ✅ NumPy importado")
        
        # Probar base de datos
        from database import get_db_connection
        conn = get_db_connection()
        conn.close()
        print("   ✅ Base de datos accesible")
        
        print("✅ Instalación verificada correctamente")
        return True
        
    except ImportError as e:
        print(f"❌ Error importando módulo: {e}")
        return False
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        return False

def main():
    """Función principal de instalación"""
    print_banner()
    
    # Verificaciones previas
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Instalación
    steps = [
        ("Instalando dependencias", install_requirements),
        ("Configurando directorios", setup_directories),
        ("Inicializando base de datos", initialize_database),
        ("Creando scripts de ejecución", create_run_script),
        ("Verificando instalación", test_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n❌ Error en: {step_name}")
            print("   La instalación no se completó correctamente")
            sys.exit(1)
    
    # Instalación completada
    print("\n" + "=" * 60)
    print("🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE!")
    print("=" * 60)
    print()
    print("📋 PRÓXIMOS PASOS:")
    print()
    
    if platform.system() == "Windows":
        print("   1. Ejecutar: ejecutar.bat")
        print("   2. O ejecutar: python app.py")
    else:
        print("   1. Ejecutar: ./ejecutar.sh")
        print("   2. O ejecutar: python3 app.py")
    
    print()
    print("🌐 Acceder en: http://localhost:5000")
    print("👤 Credenciales:")
    print("   - Admin: admin/admin123")
    print("   - Investigador: investigador/inv123")
    print("   - Analista: analista/ana123")
    print()
    print("📚 Documentación: README_PROYECTO_MEJORADO.md")
    print()

if __name__ == "__main__":
    main()
