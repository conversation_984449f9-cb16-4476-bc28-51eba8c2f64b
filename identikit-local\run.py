#!/usr/bin/env python3
"""
Script de ejecución rápida para Sistema Identikit Local
Ejecuta el sistema con configuración optimizada
"""

import os
import sys
import webbrowser
import time
from threading import Timer

def print_banner():
    """Mostrar información del sistema"""
    print("=" * 60)
    print("🏛️  SISTEMA IDENTIKIT FOTORREALISTA")
    print("    Versión Local Optimizada")
    print("=" * 60)
    print()
    print("🚀 Iniciando servidor local...")
    print("🌐 URL: http://localhost:5000")
    print("👤 Credenciales por defecto:")
    print("   - Admin: admin/admin123")
    print("   - Investigador: investigador/inv123") 
    print("   - Analista: analista/ana123")
    print()
    print("💡 Presiona Ctrl+C para detener el servidor")
    print("=" * 60)
    print()

def open_browser():
    """Abrir navegador automáticamente"""
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Navegador abierto automáticamente")
    except:
        print("⚠️  No se pudo abrir el navegador automáticamente")
        print("   Abre manualmente: http://localhost:5000")

def check_dependencies():
    """Verificar dependencias básicas"""
    try:
        import flask
        import PIL
        return True
    except ImportError as e:
        print(f"❌ Error: Dependencia faltante - {e}")
        print("💡 Ejecuta primero: python install.py")
        return False

def main():
    """Función principal"""
    print_banner()
    
    # Verificar dependencias
    if not check_dependencies():
        sys.exit(1)
    
    # Configurar variables de entorno
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '0'  # Desactivar debug para producción local
    
    # Programar apertura del navegador
    Timer(2.0, open_browser).start()
    
    try:
        # Importar y ejecutar la aplicación
        from app import app
        print("✅ Aplicación cargada correctamente")
        print("🔄 Servidor iniciando...")
        print()
        
        # Ejecutar servidor
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Servidor detenido por el usuario")
        print("👋 ¡Hasta luego!")
        
    except Exception as e:
        print(f"\n❌ Error ejecutando la aplicación: {e}")
        print("💡 Verifica que la instalación esté completa")
        sys.exit(1)

if __name__ == "__main__":
    main()
