{"extracted_information": "El contenido web describe un modelo de detección facial ultra-ligero llamado Ultra-Light-Fast-Generic-Face-Detector-1MB, diseñado para dispositivos de computación de borde. Proporciona especificaciones técnicas, detalles de rendimiento (precisión y velocidad de inferencia en varios dispositivos) e información sobre la implementación, incluyendo soporte para Python y varios frameworks.", "specifications": {"model_name": "Ultra-Light-Fast-Generic-Face-Detector-1MB", "model_size": {"fp32": "1.04 ~ 1.1 MB (.pth)", "int8_quantization": "Aproximadamente 300 KB (para frameworks de inferencia)"}, "computational_cost": {"input_320x240": "Aproximadamente 90 ~ 109 MFlops"}, "versions": ["version-slim (simplificación del backbone de la red, ligeramente más rápido)", "version-RFB (con módulo RFB modificado, mayor precisión)"], "input_resolutions_trained": ["320x240", "640x480"], "supported_frameworks_formats": ["PyTorch", "ONNX (exportación)", "NCNN (inferencia C++)", "MNN (inferencia C++ y Python, modelos FP32/INT8)", "Caffe (modelo y conversión onnx2caffe, inferencia Python, inferencia OpencvDNN)", "TFLite", "Paddle (código de entrenamiento)", "NNCase (Kendryte K210 C++)"]}, "pricing": {}, "features": ["Diseño ultra-ligero para dispositivos de borde.", "Dos versiones: slim para velocidad y RFB para precisión.", "Modelos pre-entrenados disponibles para resoluciones 320x240 y 640x480.", "Soporte para exportación a ONNX para migración.", "Implementaciones de inferencia en múltiples frameworks (NCNN, MNN, Caffe, OpencvDNN, NNCase).", "Código de entrenamiento y conversión disponibles.", "Capacidad para filtrar caras pequeñas durante la generación del dataset de entrenamiento."], "statistics": {"accuracy_widerface_val_320x240": {"version-slim": {"Easy Set": 0.77, "Medium Set": 0.671, "Hard Set": 0.395}, "version-RFB": {"Easy Set": 0.787, "Medium Set": 0.698, "Hard Set": 0.438}}, "accuracy_widerface_val_640x480": {"version-slim": {"Easy Set": 0.853, "Medium Set": 0.819, "Hard Set": 0.539}, "version-RFB": {"Easy Set": 0.855, "Medium Set": 0.822, "Hard Set": 0.579}}, "inference_latency_raspberrypi4b_mnn_int8_320x240_ms": {"version-slim": {"1 core": 29, "2 core": 16, "3 core": 12, "4 core": 9.5}, "version-RFB": {"1 core": 35, "2 core": 19.6, "3 core": 14.8, "4 core": 11}}, "inference_latency_iphone6splus_mnn_320x240_ms": {"version-slim-320": 6.33, "version-RFB-320": 7.8}, "inference_latency_kendrytek210_nncase_int8_320x240_ms": {"slim-320": 65.6, "RFB-320": 164.8}}, "temporal_info": {"last_commit": "Feb 10, 2022"}, "geographical_data": {}, "references": ["pytorch-ssd (https://github.com/qfgaohao/pytorch-ssd)", "libfacedetection (https://github.com/ShiqiYu/libfacedetection/)", "RFBNet (https://github.com/ruinmessi/RFBNet)", "RFSong-779 (https://github.com/songwsx/RFSong-779)", "Retinaface (https://github.com/deepinsight/insightface/blob/master/RetinaFace/README.md)"]}