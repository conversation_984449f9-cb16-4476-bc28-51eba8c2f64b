/* Sistema Forense - Demo Casos Criminales */

#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Efectos de cristal */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Animaciones personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Efectos de hover para elementos interactivos */
.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Sombras especiales para elementos criminalísticos */
.shadow-forensic {
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15), 
              0 1px 3px rgba(0, 0, 0, 0.3);
}

.shadow-forensic-lg {
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.2), 
              0 4px 6px rgba(0, 0, 0, 0.4);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Efectos de carga */
@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.pulse-processing {
  animation: pulse-blue 2s infinite;
}

/* Bordes metálicos para elementos profesionales */
.border-metallic {
  border: 2px solid transparent;
  background: linear-gradient(#334155, #334155) padding-box,
              linear-gradient(45deg, #64748b, #94a3b8, #64748b) border-box;
}

/* Efectos de texto */
.text-glow {
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.text-forensic {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

/* Estados de prioridad */
.priority-muy-alta {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: white;
  font-weight: bold;
}

.priority-alta {
  background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
  color: white;
  font-weight: bold;
}

.priority-media {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  color: white;
  font-weight: bold;
}

.priority-baja {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
  font-weight: bold;
}

/* Estados de casos */
.status-activo {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
}

.status-pendiente {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  color: white;
}

.status-resuelto {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
}

/* Efectos para imágenes */
.image-forensic {
  filter: contrast(1.1) brightness(0.95);
  transition: filter 0.3s ease;
}

.image-forensic:hover {
  filter: contrast(1.2) brightness(1.05);
}

/* Animaciones de procesamiento */
@keyframes processing {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-processing {
  animation: processing 2s linear infinite;
}

/* Efectos de comparación de imágenes */
.image-comparison {
  position: relative;
  overflow: hidden;
}

.image-comparison::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(59, 130, 246, 0.1) 50%, transparent 51%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.image-comparison:hover::before {
  opacity: 1;
}

/* Utilidades para formularios */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #374151;
  border: 1px solid #4b5563;
  border-radius: 0.375rem;
  color: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Efectos para botones */
.btn-forensic {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-forensic:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-forensic:active {
  transform: translateY(0);
}

/* Efectos especiales para casos criminales */
.caso-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.caso-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.caso-card:hover::before {
  left: 100%;
}

.caso-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Efectos de estado para badges */
.badge-activo {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.5); }
  50% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.8); }
}

.badge-muy-alta {
  animation: pulse-red 1.5s infinite;
}

@keyframes pulse-red {
  0%, 100% { box-shadow: 0 0 5px rgba(239, 68, 68, 0.5); }
  50% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.8); }
}

/* Media queries para responsividad */
@media (max-width: 768px) {
  .text-4xl {
    font-size: 2rem;
  }
  
  .text-3xl {
    font-size: 1.875rem;
  }
  
  .text-2xl {
    font-size: 1.5rem;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Efectos de carga específicos */
.loading-spinner {
  border: 4px solid rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para modal de imagen */
.image-modal {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.image-modal img {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

/* Efectos de hover para navegación */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}