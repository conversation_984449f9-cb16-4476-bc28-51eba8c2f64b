import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Filter, 
  Search, 
  Eye, 
  Brain, 
  Calendar, 
  MapPin, 
  User,
  Target,
  ArrowLeftRight,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { Caso } from '../App'

interface GaleriaProps {
  casos: Caso[]
}

export default function Galeria({ casos }: GaleriaProps) {
  const [filtroEstado, setFiltroEstado] = useState('')
  const [filtroPrioridad, setFiltroPrioridad] = useState('')
  const [filtroTipo, setFiltroTipo] = useState('')
  const [busqueda, setBusqueda] = useState('')
  const [modoComparacion, setModoComparacion] = useState<{ [key: string]: boolean }>({})

  const toggleComparacion = (casoId: string) => {
    setModoComparacion(prev => ({
      ...prev,
      [casoId]: !prev[casoId]
    }))
  }

  const casosFiltrados = casos.filter(caso => {
    const matchesSearch = 
      caso.id.toLowerCase().includes(busqueda.toLowerCase()) ||
      caso.titulo.toLowerCase().includes(busqueda.toLowerCase()) ||
      caso.descripcion.toLowerCase().includes(busqueda.toLowerCase()) ||
      caso.ubicacion.toLowerCase().includes(busqueda.toLowerCase())

    const matchesEstado = !filtroEstado || caso.estado === filtroEstado
    const matchesPrioridad = !filtroPrioridad || caso.prioridad === filtroPrioridad
    const matchesTipo = !filtroTipo || caso.tipoDelito.includes(filtroTipo)

    return matchesSearch && matchesEstado && matchesPrioridad && matchesTipo
  })

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'activo': return <AlertTriangle className="w-4 h-4" />
      case 'pendiente': return <Clock className="w-4 h-4" />
      case 'resuelto': return <CheckCircle className="w-4 h-4" />
      default: return <Eye className="w-4 h-4" />
    }
  }

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'activo': return 'text-green-400 bg-green-900/30 border-green-700'
      case 'pendiente': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'resuelto': return 'text-blue-400 bg-blue-900/30 border-blue-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const getPrioridadColor = (prioridad: string) => {
    switch (prioridad) {
      case 'muy_alta': return 'text-red-400 bg-red-900/30 border-red-700'
      case 'alta': return 'text-orange-400 bg-orange-900/30 border-orange-700'
      case 'media': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700'
      case 'baja': return 'text-green-400 bg-green-900/30 border-green-700'
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700'
    }
  }

  const formatFecha = (fecha: string) => {
    return new Date(fecha).toLocaleDateString('es-PE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  const limpiarFiltros = () => {
    setFiltroEstado('')
    setFiltroPrioridad('')
    setFiltroTipo('')
    setBusqueda('')
  }

  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">
          Galería de Casos Criminales
        </h1>
        <p className="text-xl text-gray-300 mb-2">
          9 Casos Reales Procesados con IA - Comparación Boceto vs Fotorrealista
        </p>
        <p className="text-gray-400">
          Tecnología ControlNet + Stable Diffusion - Precisión promedio: 93.9%
        </p>
      </div>

      {/* Filtros y Búsqueda */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-end">
          {/* Búsqueda */}
          <div className="lg:col-span-2">
            <label className="block text-gray-300 text-sm font-medium mb-2">
              Buscar casos
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={busqueda}
                onChange={(e) => setBusqueda(e.target.value)}
                placeholder="ID, título, descripción, ubicación..."
                className="w-full pl-10 pr-4 py-3 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
            </div>
          </div>

          {/* Filtro Estado */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">Estado</label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full px-3 py-3 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none"
            >
              <option value="">Todos</option>
              <option value="activo">Activo</option>
              <option value="pendiente">Pendiente</option>
              <option value="resuelto">Resuelto</option>
            </select>
          </div>

          {/* Filtro Prioridad */}
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">Prioridad</label>
            <select
              value={filtroPrioridad}
              onChange={(e) => setFiltroPrioridad(e.target.value)}
              className="w-full px-3 py-3 bg-slate-700 border border-slate-600 rounded-md text-white focus:border-blue-500 focus:outline-none"
            >
              <option value="">Todas</option>
              <option value="muy_alta">Muy Alta</option>
              <option value="alta">Alta</option>
              <option value="media">Media</option>
              <option value="baja">Baja</option>
            </select>
          </div>

          {/* Acciones */}
          <div className="flex space-x-2">
            <button
              onClick={limpiarFiltros}
              className="px-4 py-3 bg-slate-600 hover:bg-slate-700 text-white rounded-md transition-colors flex items-center space-x-2"
            >
              <Filter size={16} />
              <span>Limpiar</span>
            </button>
          </div>
        </div>

        <div className="mt-4 flex justify-between items-center">
          <p className="text-gray-400 text-sm">
            Mostrando {casosFiltrados.length} de {casos.length} casos
          </p>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-gray-400">Activo</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <span className="text-gray-400">Pendiente</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <span className="text-gray-400">Resuelto</span>
            </div>
          </div>
        </div>
      </div>

      {/* Galería de Casos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {casosFiltrados.map((caso) => (
          <div key={caso.id} className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300">
            {/* Header del Caso */}
            <div className="p-4 border-b border-slate-700">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-blue-400 font-bold text-lg">{caso.id}</span>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium border ${getEstadoColor(caso.estado)}`}>
                    {getEstadoIcon(caso.estado)}
                    <span className="ml-1">{caso.estado.toUpperCase()}</span>
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium border ${getPrioridadColor(caso.prioridad)}`}>
                    {caso.prioridad.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>
              <h3 className="text-white font-bold text-lg mb-2">{caso.titulo}</h3>
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-400">
                <span className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatFecha(caso.fecha)}
                </span>
                <span className="flex items-center">
                  <Target className="w-3 h-3 mr-1" />
                  {caso.parametrosIA.precision}
                </span>
                <span className="flex items-center">
                  <MapPin className="w-3 h-3 mr-1" />
                  {caso.ubicacion.split(',')[0]}
                </span>
                <span className="flex items-center">
                  <Zap className="w-3 h-3 mr-1" />
                  {caso.parametrosIA.tiempoProcesamiento}
                </span>
              </div>
            </div>

            {/* Comparación de Imágenes */}
            <div className="relative">
              <div className="aspect-square bg-slate-900 relative overflow-hidden">
                <img
                  src={modoComparacion[caso.id] ? caso.imagenProcesada : caso.imagenOriginal}
                  alt={modoComparacion[caso.id] ? 'Imagen procesada por IA' : 'Boceto original'}
                  className="w-full h-full object-cover transition-all duration-500"
                />
                
                {/* Overlay con información */}
                <div className="absolute top-4 left-4 right-4">
                  <div className="bg-black/70 backdrop-blur-sm rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="text-white text-sm font-medium">
                        {modoComparacion[caso.id] ? (
                          <span className="flex items-center">
                            <Brain className="w-4 h-4 mr-2 text-purple-400" />
                            IA Fotorrealista
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <Eye className="w-4 h-4 mr-2 text-blue-400" />
                            Boceto Original
                          </span>
                        )}
                      </div>
                      <button
                        onClick={() => toggleComparacion(caso.id)}
                        className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded transition-colors"
                      >
                        <ArrowLeftRight size={16} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Indicador de Precisión IA */}
                {modoComparacion[caso.id] && (
                  <div className="absolute bottom-4 right-4">
                    <div className="bg-purple-600/90 backdrop-blur-sm rounded-lg px-3 py-2">
                      <span className="text-white text-sm font-bold">
                        {caso.parametrosIA.precision}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Información del Caso */}
            <div className="p-4">
              <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                {caso.descripcion}
              </p>
              
              <div className="space-y-2 text-xs text-gray-400 mb-4">
                <div className="flex items-center">
                  <User className="w-3 h-3 mr-2" />
                  <span><strong>Oficial:</strong> {caso.oficial.nombre}</span>
                </div>
                <div className="flex items-center">
                  <Brain className="w-3 h-3 mr-2" />
                  <span><strong>Modelo:</strong> {caso.parametrosIA.modelo}</span>
                </div>
              </div>

              {/* Botones de Acción */}
              <div className="flex space-x-2">
                <button
                  onClick={() => toggleComparacion(caso.id)}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <ArrowLeftRight size={16} />
                  <span>Comparar</span>
                </button>
                <Link
                  to={`/caso/${caso.id}`}
                  className="flex-1 bg-slate-600 hover:bg-slate-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <Eye size={16} />
                  <span>Detalles</span>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No hay resultados */}
      {casosFiltrados.length === 0 && (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No se encontraron casos</h3>
          <p className="text-gray-400 mb-4">Intenta ajustar los filtros de búsqueda</p>
          <button
            onClick={limpiarFiltros}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors"
          >
            Limpiar Filtros
          </button>
        </div>
      )}

      {/* Información de Tecnología */}
      <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700 rounded-lg p-6 shadow-xl">
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
          <Brain className="w-6 h-6 mr-3 text-purple-400" />
          Tecnología de Procesamiento IA
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="bg-purple-600/20 rounded-lg p-4 mb-3">
              <Brain className="w-8 h-8 text-purple-400 mx-auto" />
            </div>
            <h3 className="text-white font-semibold mb-1">Modelo IA</h3>
            <p className="text-gray-400 text-sm">ControlNet + Stable Diffusion</p>
          </div>
          <div className="text-center">
            <div className="bg-green-600/20 rounded-lg p-4 mb-3">
              <Target className="w-8 h-8 text-green-400 mx-auto" />
            </div>
            <h3 className="text-white font-semibold mb-1">Precisión</h3>
            <p className="text-gray-400 text-sm">89.7% - 97.1%</p>
          </div>
          <div className="text-center">
            <div className="bg-yellow-600/20 rounded-lg p-4 mb-3">
              <Zap className="w-8 h-8 text-yellow-400 mx-auto" />
            </div>
            <h3 className="text-white font-semibold mb-1">Velocidad</h3>
            <p className="text-gray-400 text-sm">12-35 segundos</p>
          </div>
          <div className="text-center">
            <div className="bg-blue-600/20 rounded-lg p-4 mb-3">
              <CheckCircle className="w-8 h-8 text-blue-400 mx-auto" />
            </div>
            <h3 className="text-white font-semibold mb-1">Casos Exitosos</h3>
            <p className="text-gray-400 text-sm">8 de 9 procesados</p>
          </div>
        </div>
      </div>
    </div>
  )
}
